{"name": "buddychip-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "npx vite --port=3001", "build": "npx vite build", "serve": "npx vite preview", "start": "npx vite", "check-types": "tsc --noEmit"}, "devDependencies": {"@tanstack/react-router-devtools": "^1.114.27", "@tanstack/router-plugin": "^1.114.27", "@types/node": "^22.13.13", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "vite": "^6.2.2"}, "dependencies": {"@BuddyChipAI/backend": "workspace:*", "@clerk/clerk-react": "^5.31.8", "@clerk/themes": "^2.2.49", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@tanstack/react-form": "^1.0.5", "@tanstack/react-router": "^1.114.25", "animate.css": "^4.1.1", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.23.0", "crypto-browserify": "^3.12.1", "events": "^3.3.0", "framer-motion": "^12.18.1", "lottie-react": "^2.4.1", "lucide-react": "^0.513.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-confetti": "^6.4.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-icons": "^5.5.0", "sonner": "^1.7.4", "stream-browserify": "^3.0.0", "tailwind-merge": "^2.6.0", "tw-animate-css": "^1.2.5", "util": "^0.12.5", "zod": "^3.25.64"}}