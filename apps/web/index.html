<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>BuddyChipAI</title>
    <meta name="description" content="BuddyChipAI - AI-powered social media management and engagement platform" />
    <link rel="icon" href="/Logo.svg" type="image/svg+xml" />
    <link rel="shortcut icon" href="/Logo.svg" />
    <link rel="apple-touch-icon" href="/Logo.svg" />
  </head>

  <body>
    <div id="app"></div>
    <script>
      // EventEmitter polyfill for browser compatibility
      if (typeof global === 'undefined') {
        var global = globalThis;
      }
      if (typeof process === 'undefined') {
        var process = { env: {} };
      }
    </script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
