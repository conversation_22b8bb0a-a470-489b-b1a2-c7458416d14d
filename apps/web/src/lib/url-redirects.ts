/**
 * Intelligent URL Redirect System
 * Handles common URL patterns, typos, and legacy URLs to provide
 * smart redirects for better user experience.
 */

export interface RedirectSuggestion {
  /** The suggested URL to redirect to */
  url: string;
  /** Confidence score (0-1) of the suggestion */
  confidence: number;
  /** Reason for the suggestion */
  reason: string;
  /** Whether this should be an automatic redirect or just a suggestion */
  autoRedirect: boolean;
}

export interface RedirectRule {
  /** Pattern to match against (regex string or exact match) */
  pattern: string | RegExp;
  /** Target URL or function to generate target URL */
  target: string | ((matches: RegExpMatchArray | null, originalUrl: string) => string);
  /** Confidence score for this rule */
  confidence: number;
  /** Whether to auto-redirect or just suggest */
  autoRedirect: boolean;
  /** Description of what this rule handles */
  description: string;
}

/**
 * Predefined redirect rules for common patterns and typos
 */
const REDIRECT_RULES: RedirectRule[] = [
  // Common typos in main routes
  {
    pattern: /^\/dashbaord$/i,
    target: '/dashboard',
    confidence: 0.95,
    autoRedirect: true,
    description: 'Dashboard typo correction'
  },
  {
    pattern: /^\/dash$/i,
    target: '/dashboard',
    confidence: 0.8,
    autoRedirect: false,
    description: 'Dashboard abbreviation'
  },
  {
    pattern: /^\/signin$/i,
    target: '/sign-in',
    confidence: 0.9,
    autoRedirect: true,
    description: 'Sign-in URL format correction'
  },
  {
    pattern: /^\/signup$/i,
    target: '/sign-up',
    confidence: 0.9,
    autoRedirect: true,
    description: 'Sign-up URL format correction'
  },
  {
    pattern: /^\/login$/i,
    target: '/sign-in',
    confidence: 0.85,
    autoRedirect: false,
    description: 'Login to sign-in redirect'
  },
  {
    pattern: /^\/register$/i,
    target: '/sign-up',
    confidence: 0.85,
    autoRedirect: false,
    description: 'Register to sign-up redirect'
  },
  
  // Feature-specific redirects
  {
    pattern: /^\/tweets?$/i,
    target: '/tweet-assistant',
    confidence: 0.8,
    autoRedirect: false,
    description: 'Tweet to tweet-assistant redirect'
  },
  {
    pattern: /^\/mentions?$/i,
    target: '/mentions',
    confidence: 0.9,
    autoRedirect: true,
    description: 'Mention to mentions redirect'
  },
  {
    pattern: /^\/images?$/i,
    target: '/image-generation',
    confidence: 0.8,
    autoRedirect: false,
    description: 'Image to image-generation redirect'
  },
  {
    pattern: /^\/search$/i,
    target: '/live-search',
    confidence: 0.8,
    autoRedirect: false,
    description: 'Search to live-search redirect'
  },
  
  // Legacy URL patterns (if migrating from another system)
  {
    pattern: /^\/app\/(.+)$/i,
    target: (matches) => `/${matches?.[1] || ''}`,
    confidence: 0.7,
    autoRedirect: false,
    description: 'Legacy app prefix removal'
  },
  {
    pattern: /^\/admin$/i,
    target: '/dashboard',
    confidence: 0.6,
    autoRedirect: false,
    description: 'Admin to dashboard redirect'
  },
  
  // Common path variations
  {
    pattern: /^\/home$/i,
    target: '/',
    confidence: 0.8,
    autoRedirect: false,
    description: 'Home to root redirect'
  },
  {
    pattern: /^\/index$/i,
    target: '/',
    confidence: 0.9,
    autoRedirect: true,
    description: 'Index to root redirect'
  },
  
  // Handle trailing slashes and case variations
  {
    pattern: /^(.+)\/$/, // Remove trailing slash
    target: (matches) => matches?.[1] || '/',
    confidence: 0.95,
    autoRedirect: true,
    description: 'Trailing slash removal'
  },
];

/**
 * Valid routes in the application (for fuzzy matching)
 */
const VALID_ROUTES = [
  '/',
  '/dashboard',
  '/sign-in',
  '/sign-up',
  '/onboarding',
  '/mentions',
  '/live-search',
  '/image-generation',
  '/tweet-assistant',
];

/**
 * URL Redirect Manager
 */
export class UrlRedirectManager {
  private rules: RedirectRule[];

  constructor(customRules: RedirectRule[] = []) {
    this.rules = [...REDIRECT_RULES, ...customRules];
    console.log('🔄 UrlRedirectManager initialized with', this.rules.length, 'rules');
  }

  /**
   * Find redirect suggestions for a given URL
   */
  findRedirectSuggestions(url: string): RedirectSuggestion[] {
    const suggestions: RedirectSuggestion[] = [];
    const cleanUrl = this.cleanUrl(url);

    console.log('🔍 Finding redirects for:', cleanUrl);

    // Check against predefined rules
    for (const rule of this.rules) {
      const suggestion = this.checkRule(cleanUrl, rule);
      if (suggestion) {
        suggestions.push(suggestion);
      }
    }

    // Add fuzzy matching suggestions
    const fuzzyMatches = this.findFuzzyMatches(cleanUrl);
    suggestions.push(...fuzzyMatches);

    // Sort by confidence (highest first)
    suggestions.sort((a, b) => b.confidence - a.confidence);

    // Remove duplicates and limit to top 3 suggestions
    const uniqueSuggestions = this.removeDuplicates(suggestions).slice(0, 3);

    console.log('💡 Found', uniqueSuggestions.length, 'redirect suggestions:', uniqueSuggestions);

    return uniqueSuggestions;
  }

  /**
   * Get the best automatic redirect (if any)
   */
  getAutoRedirect(url: string): string | null {
    const suggestions = this.findRedirectSuggestions(url);
    const autoRedirect = suggestions.find(s => s.autoRedirect && s.confidence >= 0.8);
    
    if (autoRedirect) {
      console.log('🚀 Auto-redirecting to:', autoRedirect.url, 'with confidence:', autoRedirect.confidence);
      return autoRedirect.url;
    }

    return null;
  }

  /**
   * Check a URL against a specific rule
   */
  private checkRule(url: string, rule: RedirectRule): RedirectSuggestion | null {
    let matches: RegExpMatchArray | null = null;

    if (rule.pattern instanceof RegExp) {
      matches = url.match(rule.pattern);
    } else {
      matches = url === rule.pattern ? [url] : null;
    }

    if (matches) {
      let targetUrl: string;

      if (typeof rule.target === 'function') {
        targetUrl = rule.target(matches, url);
      } else {
        targetUrl = rule.target;
      }

      return {
        url: targetUrl,
        confidence: rule.confidence,
        reason: rule.description,
        autoRedirect: rule.autoRedirect,
      };
    }

    return null;
  }

  /**
   * Find fuzzy matches using string similarity
   */
  private findFuzzyMatches(url: string): RedirectSuggestion[] {
    const suggestions: RedirectSuggestion[] = [];

    for (const validRoute of VALID_ROUTES) {
      const similarity = this.calculateSimilarity(url, validRoute);
      
      if (similarity > 0.6 && similarity < 1.0) { // Don't suggest exact matches
        suggestions.push({
          url: validRoute,
          confidence: similarity * 0.7, // Reduce confidence for fuzzy matches
          reason: `Similar to "${validRoute}" (${Math.round(similarity * 100)}% match)`,
          autoRedirect: false, // Never auto-redirect fuzzy matches
        });
      }
    }

    return suggestions;
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) {
      return 1.0;
    }

    const distance = this.levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i;
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j;
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Clean and normalize URL for processing
   */
  private cleanUrl(url: string): string {
    // Remove query parameters and hash
    const cleanUrl = url.split('?')[0].split('#')[0];
    
    // Ensure it starts with /
    return cleanUrl.startsWith('/') ? cleanUrl : `/${cleanUrl}`;
  }

  /**
   * Remove duplicate suggestions
   */
  private removeDuplicates(suggestions: RedirectSuggestion[]): RedirectSuggestion[] {
    const seen = new Set<string>();
    return suggestions.filter(suggestion => {
      if (seen.has(suggestion.url)) {
        return false;
      }
      seen.add(suggestion.url);
      return true;
    });
  }
}

// Create singleton instance
let redirectManager: UrlRedirectManager | null = null;

export function getUrlRedirectManager(): UrlRedirectManager {
  if (!redirectManager) {
    redirectManager = new UrlRedirectManager();
  }
  return redirectManager;
}

export function createUrlRedirectManager(customRules: RedirectRule[] = []): UrlRedirectManager {
  redirectManager = new UrlRedirectManager(customRules);
  return redirectManager;
}
