/**
 * 404 Error Analytics Tracking
 * Integrates with the existing Convex analytics system to track 404 errors
 * and provide insights into broken links and navigation issues.
 */

import { ConvexReactClient } from "convex/react";

export interface NotFoundAnalyticsData {
  /** The URL that was not found */
  requestedUrl: string;
  /** The page the user came from (referrer) */
  referrer?: string;
  /** User agent string for device/browser info */
  userAgent?: string;
  /** Timestamp when the 404 occurred */
  timestamp: number;
  /** Current pathname for context */
  currentPath?: string;
  /** Query parameters if any */
  queryParams?: Record<string, string>;
  /** Whether this was a direct navigation or link click */
  navigationType?: 'direct' | 'link' | 'back_forward' | 'reload';
  /** User ID if authenticated */
  userId?: string;
  /** Session ID for tracking user journey */
  sessionId?: string;
}

export interface UrlRedirectAttempt {
  /** Original URL that was attempted */
  originalUrl: string;
  /** URL that was suggested/redirected to */
  suggestedUrl: string;
  /** Whether the user accepted the redirect */
  accepted: boolean;
  /** Timestamp of the redirect attempt */
  timestamp: number;
  /** Confidence score of the redirect suggestion (0-1) */
  confidence: number;
}

/**
 * Analytics tracker for 404 errors and navigation issues
 */
export class NotFoundAnalytics {
  private convex: ConvexReactClient;
  private sessionId: string;

  constructor(convex: ConvexReactClient) {
    this.convex = convex;
    this.sessionId = this.generateSessionId();
    
    console.log('🔍 NotFoundAnalytics initialized with session:', this.sessionId);
  }

  /**
   * Track a 404 error occurrence
   */
  async track404Error(data: Partial<NotFoundAnalyticsData>): Promise<void> {
    try {
      const analyticsData: NotFoundAnalyticsData = {
        requestedUrl: data.requestedUrl || window.location.href,
        referrer: data.referrer || document.referrer,
        userAgent: data.userAgent || navigator.userAgent,
        timestamp: data.timestamp || Date.now(),
        currentPath: data.currentPath || window.location.pathname,
        queryParams: data.queryParams || this.parseQueryParams(),
        navigationType: data.navigationType || this.detectNavigationType(),
        sessionId: this.sessionId,
        ...data,
      };

      console.log('📊 Tracking 404 error:', {
        url: analyticsData.requestedUrl,
        referrer: analyticsData.referrer,
        sessionId: this.sessionId,
      });

      // Use the existing analytics system
      await this.convex.mutation("analytics:ingestAnalyticsBatch" as any, {
        events: [{
          eventType: "404_error",
          eventData: analyticsData,
          timestamp: analyticsData.timestamp,
          path: analyticsData.currentPath,
        }]
      });

    } catch (error) {
      console.error('❌ Failed to track 404 error:', error);
      // Don't throw - analytics failures shouldn't break the user experience
    }
  }

  /**
   * Track a redirect attempt and its outcome
   */
  async trackRedirectAttempt(data: UrlRedirectAttempt): Promise<void> {
    try {
      console.log('🔄 Tracking redirect attempt:', {
        from: data.originalUrl,
        to: data.suggestedUrl,
        accepted: data.accepted,
        confidence: data.confidence,
      });

      await this.convex.mutation("analytics:ingestAnalyticsBatch" as any, {
        events: [{
          eventType: "404_redirect_attempt",
          eventData: {
            ...data,
            sessionId: this.sessionId,
          },
          timestamp: data.timestamp,
          path: window.location.pathname,
        }]
      });

    } catch (error) {
      console.error('❌ Failed to track redirect attempt:', error);
    }
  }

  /**
   * Track user actions on the 404 page
   */
  async trackUserAction(action: string, data?: Record<string, any>): Promise<void> {
    try {
      console.log('👆 Tracking 404 page action:', action, data);

      await this.convex.mutation("analytics:ingestAnalyticsBatch" as any, {
        events: [{
          eventType: "404_user_action",
          eventData: {
            action,
            sessionId: this.sessionId,
            timestamp: Date.now(),
            ...data,
          },
          timestamp: Date.now(),
          path: window.location.pathname,
        }]
      });

    } catch (error) {
      console.error('❌ Failed to track user action:', error);
    }
  }

  /**
   * Generate a unique session ID for tracking user journey
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Parse query parameters from current URL
   */
  private parseQueryParams(): Record<string, string> {
    const params: Record<string, string> = {};
    const searchParams = new URLSearchParams(window.location.search);
    
    for (const [key, value] of searchParams.entries()) {
      params[key] = value;
    }
    
    return params;
  }

  /**
   * Detect how the user navigated to this page
   */
  private detectNavigationType(): 'direct' | 'link' | 'back_forward' | 'reload' {
    if (typeof window !== 'undefined' && window.performance) {
      const navigation = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      switch (navigation?.type) {
        case 'navigate':
          return document.referrer ? 'link' : 'direct';
        case 'back_forward':
          return 'back_forward';
        case 'reload':
          return 'reload';
        default:
          return 'direct';
      }
    }
    
    return 'direct';
  }
}

/**
 * Create a singleton instance of the analytics tracker
 */
let analyticsInstance: NotFoundAnalytics | null = null;

export function createNotFoundAnalytics(convex: ConvexReactClient): NotFoundAnalytics {
  if (!analyticsInstance) {
    analyticsInstance = new NotFoundAnalytics(convex);
  }
  return analyticsInstance;
}

export function getNotFoundAnalytics(): NotFoundAnalytics | null {
  return analyticsInstance;
}

/**
 * Hook for using 404 analytics in React components
 */
export function useNotFoundAnalytics(convex: ConvexReactClient) {
  const analytics = createNotFoundAnalytics(convex);
  
  return {
    track404Error: (data?: Partial<NotFoundAnalyticsData>) => analytics.track404Error(data || {}),
    trackRedirectAttempt: (data: UrlRedirectAttempt) => analytics.trackRedirectAttempt(data),
    trackUserAction: (action: string, data?: Record<string, any>) => analytics.trackUserAction(action, data),
  };
}
