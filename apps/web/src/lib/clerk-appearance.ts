import { dark } from "@clerk/themes";

/**
 * Enhanced BuddyChip Clerk appearance configuration.
 * Provides a polished, branded authentication experience.
 */
export const clerkAppearance = {
  baseTheme: dark,
  elements: {
    // Main card styling with improved backdrop and shadows
    card: "bg-[#000000]/95 border-[#202631] backdrop-blur-sm shadow-2xl rounded-2xl overflow-hidden",

    // Header styling with better typography
    headerTitle: "text-[#F5F7FA] text-2xl font-bold tracking-tight",
    headerSubtitle: "text-[#6E7A8C] text-sm leading-relaxed mt-2",

    // Primary button with enhanced hover effects
    formButtonPrimary: `
      bg-gradient-to-r from-[#316FE3] to-[#2563eb]
      hover:from-[#2563eb] hover:to-[#1d4ed8]
      text-white font-semibold rounded-lg
      transition-all duration-300 ease-out
      shadow-lg hover:shadow-xl hover:shadow-[#316FE3]/25
      border-0 transform hover:scale-[1.02] active:scale-[0.98]
      focus:ring-2 focus:ring-[#316FE3]/50 focus:ring-offset-2 focus:ring-offset-[#000000]
    `,

    // Enhanced input fields with better focus states
    formFieldInput: `
      bg-[#0E1117] border-[#202631] text-[#F5F7FA]
      placeholder:text-[#6E7A8C] rounded-lg
      transition-all duration-200 ease-out
      focus:border-[#316FE3] focus:ring-2 focus:ring-[#316FE3]/20
      hover:border-[#484f58] hover:bg-[#161b22]
      focus:bg-[#161b22] focus:shadow-lg
    `,

    // Form field labels with better contrast
    formFieldLabel: "text-[#F5F7FA] font-medium text-sm mb-2",

    // Enhanced footer links
    footerActionLink: `
      text-[#316FE3] hover:text-[#2563eb] font-medium
      transition-colors duration-200 underline-offset-4
      hover:underline focus:underline focus:outline-none
    `,

    // Identity preview styling
    identityPreviewText: "text-[#6E7A8C] text-sm",
    identityPreviewEditButton: `
      text-[#316FE3] hover:text-[#2563eb]
      transition-colors duration-200 font-medium
    `,

    // Enhanced social buttons
    socialButtonsIconButton: `
      border-[#202631] hover:border-[#316FE3]
      bg-[#0E1117] hover:bg-[#161b22]
      transition-all duration-200 ease-out
      rounded-lg shadow-sm hover:shadow-md
      transform hover:scale-[1.02] active:scale-[0.98]
    `,

    // Divider styling
    dividerLine: "bg-gradient-to-r from-transparent via-[#202631] to-transparent",
    dividerText: "text-[#6E7A8C] text-xs font-medium px-4 bg-[#000000]",

    // Error states
    formFieldInputShowPasswordButton: "text-[#6E7A8C] hover:text-[#F5F7FA]",
    formFieldSuccessText: "text-[#3fb950] text-sm",
    formFieldErrorText: "text-[#f85149] text-sm font-medium",

    // Loading states
    spinner: "text-[#316FE3]",

    // Additional form elements
    formFieldAction: "text-[#316FE3] hover:text-[#2563eb] font-medium text-sm",
    formFieldHintText: "text-[#6E7A8C] text-xs",

    // Modal and overlay improvements
    modalContent: "bg-[#000000] border-[#202631] shadow-2xl",
    modalCloseButton: "text-[#6E7A8C] hover:text-[#F5F7FA] transition-colors duration-200",

    // Breadcrumb styling for multi-step flows
    breadcrumbsItem: "text-[#6E7A8C]",
    breadcrumbsItemCurrent: "text-[#F5F7FA] font-medium",
    breadcrumbsItemDivider: "text-[#484f58]",
  },
  variables: {
    colorPrimary: "#316FE3",
    colorBackground: "#000000",
    colorInputBackground: "#0E1117",
    colorInputText: "#F5F7FA",
    colorText: "#F5F7FA",
    colorTextSecondary: "#6E7A8C",
    colorTextOnPrimaryBackground: "#ffffff",
    colorSuccess: "#3fb950",
    colorWarning: "#d29922",
    colorDanger: "#f85149",
    borderRadius: "8px",
    fontFamily: "inherit",
    fontWeight: {
      normal: "400",
      medium: "500",
      semibold: "600",
      bold: "700",
    },
    fontSize: {
      xs: "0.75rem",
      sm: "0.875rem",
      base: "1rem",
      lg: "1.125rem",
      xl: "1.25rem",
      "2xl": "1.5rem",
    },
    spacingUnit: "1rem",
  },
  layout: {
    socialButtonsVariant: "blockButton" as const,
    socialButtonsPlacement: "top" as const,
    logoPlacement: "inside" as const,
    showOptionalFields: true,
  },
} as const;