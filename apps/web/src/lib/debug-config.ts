/**
 * Debug Configuration System
 * 
 * Comprehensive debug mode controls with environment variable switches.
 * Ensures debug functionality is disabled in production for security and performance.
 */

import React from 'react';

// Environment-based debug configuration
const getEnvBoolean = (key: string, defaultValue: boolean = false): boolean => {
  const value = import.meta.env[key];
  if (value === undefined) return defaultValue;
  return value === "true" || value === "1";
};

// Master debug switch - controls all debug functionality
export const DEBUG_ENABLED = getEnvBoolean("VITE_DEBUG_MODE", false);

// Granular debug controls for specific features
export const DEBUG_CONFIG = {
  // Authentication debugging
  auth: {
    enabled: DEBUG_ENABLED && getEnvBoolean("VITE_AUTH_DEBUG", true),
    components: DEBUG_ENABLED && getEnvBoolean("VITE_AUTH_DEBUG_COMPONENTS", true),
    logging: DEBUG_ENABLED && getEnvBoolean("VITE_AUTH_DEBUG_LOGGING", true),
    testing: DEBUG_ENABLED && getEnvBoolean("VITE_AUTH_DEBUG_TESTING", true),
  },
  
  // Performance monitoring debugging
  performance: {
    enabled: DEBUG_ENABLED && getEnvBoolean("VITE_PERFORMANCE_DEBUG", true),
    monitoring: DEBUG_ENABLED && getEnvBoolean("VITE_PERFORMANCE_MONITORING", true),
    analytics: DEBUG_ENABLED && getEnvBoolean("VITE_PERFORMANCE_ANALYTICS", true),
    caching: DEBUG_ENABLED && getEnvBoolean("VITE_CACHE_DEBUG", false),
  },
  
  // General application debugging
  app: {
    enabled: DEBUG_ENABLED && getEnvBoolean("VITE_APP_DEBUG", true),
    console: DEBUG_ENABLED && getEnvBoolean("VITE_CONSOLE_DEBUG", true),
    errors: DEBUG_ENABLED && getEnvBoolean("VITE_ERROR_DEBUG", true),
    network: DEBUG_ENABLED && getEnvBoolean("VITE_NETWORK_DEBUG", false),
  },
  
  // Development tools
  devTools: {
    enabled: DEBUG_ENABLED && getEnvBoolean("VITE_DEV_TOOLS", true),
    hotkeys: DEBUG_ENABLED && getEnvBoolean("VITE_DEBUG_HOTKEYS", true),
    overlay: DEBUG_ENABLED && getEnvBoolean("VITE_DEBUG_OVERLAY", false),
  }
} as const;

// Debug logging utility with levels
export enum DebugLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  TRACE = 4,
}

const currentLogLevel = DEBUG_ENABLED 
  ? parseInt(import.meta.env.VITE_DEBUG_LOG_LEVEL || "3") 
  : DebugLevel.ERROR;

export const debugLog = {
  error: (category: string, message: string, ...args: any[]) => {
    if (currentLogLevel >= DebugLevel.ERROR) {
      console.error(`🔴 [${category}]`, message, ...args);
    }
  },
  
  warn: (category: string, message: string, ...args: any[]) => {
    if (currentLogLevel >= DebugLevel.WARN) {
      console.warn(`🟡 [${category}]`, message, ...args);
    }
  },
  
  info: (category: string, message: string, ...args: any[]) => {
    if (currentLogLevel >= DebugLevel.INFO) {
      console.info(`🔵 [${category}]`, message, ...args);
    }
  },
  
  debug: (category: string, message: string, ...args: any[]) => {
    if (currentLogLevel >= DebugLevel.DEBUG) {
      console.debug(`🟢 [${category}]`, message, ...args);
    }
  },
  
  trace: (category: string, message: string, ...args: any[]) => {
    if (currentLogLevel >= DebugLevel.TRACE) {
      console.trace(`🔍 [${category}]`, message, ...args);
    }
  },
};

// Debug component wrapper - conditionally renders debug components
export function DebugWrapper({ 
  children, 
  category, 
  enabled = true 
}: { 
  children: React.ReactNode; 
  category: keyof typeof DEBUG_CONFIG;
  enabled?: boolean;
}): React.ReactElement | null {
  const isEnabled = DEBUG_CONFIG[category].enabled && enabled;
  
  if (!isEnabled) {
    return null;
  }
  
  return React.createElement(React.Fragment, null, children);
}

// Debug panel visibility control
export const shouldShowDebugPanel = (category: keyof typeof DEBUG_CONFIG): boolean => {
  return DEBUG_CONFIG[category].enabled;
};

// Performance timer for debug mode
export const debugTimer = {
  start: (label: string) => {
    if (DEBUG_CONFIG.performance.enabled) {
      console.time(`⏱️ ${label}`);
    }
  },
  
  end: (label: string) => {
    if (DEBUG_CONFIG.performance.enabled) {
      console.timeEnd(`⏱️ ${label}`);
    }
  },
  
  mark: (label: string, data?: any) => {
    if (DEBUG_CONFIG.performance.enabled) {
      console.log(`📊 [${label}]`, data || 'checkpoint reached');
    }
  },
};

// Environment info for debugging
export const getDebugInfo = () => {
  if (!DEBUG_ENABLED) {
    return { debug: false, mode: 'production' };
  }
  
  return {
    debug: DEBUG_ENABLED,
    mode: import.meta.env.MODE,
    dev: import.meta.env.DEV,
    config: DEBUG_CONFIG,
    environment: {
      VITE_DEBUG_MODE: import.meta.env.VITE_DEBUG_MODE,
      VITE_AUTH_DEBUG: import.meta.env.VITE_AUTH_DEBUG,
      VITE_PERFORMANCE_DEBUG: import.meta.env.VITE_PERFORMANCE_DEBUG,
      VITE_DEBUG_LOG_LEVEL: import.meta.env.VITE_DEBUG_LOG_LEVEL,
    },
    timestamp: new Date().toISOString(),
  };
};

// Hot key support for debug mode
if (DEBUG_CONFIG.devTools.hotkeys && typeof window !== 'undefined') {
  window.addEventListener('keydown', (event) => {
    // Ctrl+Shift+D to toggle debug info
    if (event.ctrlKey && event.shiftKey && event.key === 'D') {
      event.preventDefault();
      console.group('🛠️ Debug Information');
      console.table(getDebugInfo());
      console.groupEnd();
    }
    
    // Ctrl+Shift+C to clear console
    if (event.ctrlKey && event.shiftKey && event.key === 'C') {
      event.preventDefault();
      console.clear();
      debugLog.info('Debug', 'Console cleared via hotkey');
    }
  });
}

// Initialize debug mode
if (DEBUG_ENABLED) {
  debugLog.info('Debug', 'Debug mode initialized', getDebugInfo());
} else {
  // Silence all debug output in production
  console.log('🔒 Debug mode is DISABLED - all debug functionality turned off');
  const noOp = () => {};
  Object.keys(debugLog).forEach(key => {
    (debugLog as any)[key] = noOp;
  });
}