import { ConvexReactClient } from "convex/react";
// Adjust the path to your actual generated API
// For this subtask, we'll use a placeholder for `api.analytics.ingestAnalyticsBatch`
// The string "analytics:ingestAnalyticsBatch" is often how Convex client resolves this.

// Placeholder for the actual API type. In a real scenario, this would be imported
// from convex/_generated/api using `import { api } from "@/convex/_generated/api";`
// or similar, depending on project structure and aliases.
type ConvexApiClient = ConvexReactClient; // More specific type based on ConvexReactClient

export type AnalyticsEventPayload = {
  eventName: string;
  timestamp: number; // Will be set by the batcher
  userId?: string; // Assuming string ID for client-side ease, maps to Id<"users"> or clerkUserId on backend
  clerkUserId?: string;
  sessionId?: string;
  path?: string; // Will be set by the batcher
  properties?: Record<string, any>;
};

// Type for the event passed to the track method (timestamp and path are auto-added)
export type TrackEventArgs = Omit<AnalyticsEventPayload, 'timestamp' | 'path'>;

export class AnalyticsBatcher {
  private buffer: AnalyticsEventPayload[] = [];
  private flushTimer: NodeJS.Timeout | null = null;
  private convex: ConvexApiClient;
  private batchSize: number;
  private flushIntervalMs: number;

  constructor(
    convexClient: ConvexApiClient,
    options: { batchSize?: number; flushIntervalMs?: number } = {}
  ) {
    this.convex = convexClient;
    this.batchSize = options.batchSize || 50;
    this.flushIntervalMs = options.flushIntervalMs || 30000; // 30 seconds

    // Add a listener for page visibility changes to flush events when page is hidden
    // This helps ensure data is sent before the user closes the tab/browser
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'hidden') {
          this.flush();
        }
      });
      // Also consider flushing on 'beforeunload' but it's less reliable
      window.addEventListener('beforeunload', () => {
        this.flush();
      });
    }
  }

  public track(eventArgs: TrackEventArgs): void {
    const eventWithTimestamp: AnalyticsEventPayload = {
      ...eventArgs,
      timestamp: Date.now(),
      path: typeof window !== 'undefined' ? window.location.pathname : undefined, // Automatically add current path
    };
    this.buffer.push(eventWithTimestamp);

    if (this.buffer.length >= this.batchSize) {
      this.flush();
    } else {
      this.scheduleFlush();
    }
  }

  private scheduleFlush(): void {
    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
    }
    this.flushTimer = setTimeout(() => {
      this.flush();
    }, this.flushIntervalMs);
  }

  public async flush(): Promise<void> {
    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
      this.flushTimer = null;
    }

    if (this.buffer.length === 0) {
      return;
    }

    const eventsToFlush = [...this.buffer];
    this.buffer = [];

    try {
      // Using the string path for the mutation, as Convex client can resolve this.
      // This corresponds to `api.analytics.ingestAnalyticsBatch`
      await this.convex.mutation("analytics:ingestAnalyticsBatch" as any, { events: eventsToFlush });
      console.log(`Analytics: Flushed ${eventsToFlush.length} events.`);
    } catch (error) {
      console.error("Analytics: Failed to flush events", error, eventsToFlush);
      // Optional: Add events back to buffer for retry, with caution
      // To avoid infinite loops or oversized buffers, implement a max retries or max buffer size.
      // For simplicity here, we log the error and the events that failed.
      // this.buffer.unshift(...eventsToFlush);
    }
  }

  // Call this method when the user logs out or application is shutting down
  // to ensure all pending events are sent.
  public async closeAndFlush(): Promise<void> {
    await this.flush();
    // Any other cleanup if necessary
  }
}

// Example of how it might be instantiated globally (e.g., in main.tsx or a context)
// import convexClient from "./convexClient"; // Path to your ConvexReactClient instance
// export const analyticsBatcher = new AnalyticsBatcher(convexClient);

// It's often better to provide the analyticsBatcher via React Context
// or a similar dependency injection pattern rather than a global singleton
// if you need it to be easily testable or have multiple instances.
// For typical web app usage, a singleton provided via context is common.
