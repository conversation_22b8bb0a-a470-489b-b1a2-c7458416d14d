import { createFileRoute } from '@tanstack/react-router'
import { lazy } from 'react'
import { AuthGuards } from '../components/auth/route-guard'

const DashboardPage = lazy(() => import('../components/pages/DashboardPage'))

function DashboardComponent() {
  return (
    <AuthGuards.Dashboard>
      <DashboardPage />
    </AuthGuards.Dashboard>
  )
}

export const Route = createFileRoute('/dashboard')({
  component: DashboardComponent,
})