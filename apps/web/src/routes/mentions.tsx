import { createFileRoute } from '@tanstack/react-router'
import { MentionsCenter } from '../components/reply-guy/mentions-center'
import { AuthGuards } from '../components/auth/enhanced-auth-guards'
import { AuthDebugComponent } from "../components/auth/auth-debug-component";
import { useState } from "react";
import { Button } from "../components/ui/button";
import { DEBUG_CONFIG, DebugWrapper } from "../lib/debug-config";
import { Clock, RefreshCw, Settings } from "lucide-react";
import { useAction } from "convex/react";
import { api } from "@BuddyChipAI/backend";

export const Route = createFileRoute('/mentions')({
  component: MentionsPage,
})

function MentionsPage() {
  const [showDebug, setShowDebug] = useState(false);

  // --- Refresh button state & logic ------------------------------------------------
  const refreshMentions = useAction(api.mentions.mentionMutations.refreshMentions);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<number | null>(null);

  const handleRefresh = async () => {
    if (isRefreshing) return;
    setIsRefreshing(true);
    try {
      await refreshMentions({
        enableViralDetection: true,
        priorityMode: true,
        maxConcurrency: 3,
      });
      setLastUpdate(Date.now());
    } catch (err) {
      console.error("❌ Failed to refresh mentions", err);
    } finally {
      setIsRefreshing(false);
    }
  };

  // --- Settings button logic ------------------------------------------------------
  const openSettingsTab = () => {
    // Use hash navigation so MentionsCenter can listen & switch tabs
    if (typeof window !== "undefined") {
      window.location.hash = "settings";
      // Fallback scroll to bottom if hash-based tab switching is not yet implemented
      setTimeout(() => {
        const settingsEl = document.getElementById("mentions-settings-section");
        if (settingsEl) settingsEl.scrollIntoView({ behavior: "smooth" });
      }, 100);
    }
  };

  return (
    <AuthGuards.Dashboard>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-[var(--buddychip-accent)] to-purple-400 bg-clip-text text-transparent mb-3">
              Mentions Center
            </h1>
            <p className="text-lg text-[var(--buddychip-white)]">
              Monitor mentions and engage with your community
            </p>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 text-xs text-[#6E7A8C] bg-[#000000]/40 px-3 py-2 rounded-lg border border-[#202631]">
              <Clock className="h-3 w-3" />
              <span>
                Last update: {lastUpdate ? `${Math.round((Date.now() - lastUpdate)/1000)}s ago` : "—"}
              </span>
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)] transition-all duration-200"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`${isRefreshing ? "animate-spin" : ""} h-4 w-4 mr-2`} />
              {isRefreshing ? "Refreshing" : "Refresh"}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]"
              onClick={openSettingsTab}
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <DebugWrapper category="auth">
              <Button 
                onClick={() => setShowDebug(!showDebug)}
                variant="outline"
                size="sm"
                className="border-[#316FE3] text-[#316FE3] hover:bg-[#316FE3] hover:text-white"
              >
                {showDebug ? "Hide" : "Show"} Auth Debug
              </Button>
            </DebugWrapper>
          </div>
        </div>
        
        {/* Debug component only rendered if auth debugging is enabled */}
        <DebugWrapper category="auth" enabled={showDebug}>
          <div className="space-y-4 mb-6">
            <AuthDebugComponent />
          </div>
        </DebugWrapper>
        
        <MentionsCenter />
      </div>
    </AuthGuards.Dashboard>
  )
}