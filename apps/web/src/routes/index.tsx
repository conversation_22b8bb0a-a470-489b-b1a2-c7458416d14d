import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { LandingLayout } from "@/components/landing/landing-layout";
import { useAuth } from "@/components/auth/use-auth";
import { useEffect } from "react";

export const Route = createFileRoute("/")({
  component: HomeComponent,
});

function HomeComponent() {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Auto-redirect authenticated users to dashboard
    if (isAuthenticated && !isLoading) {
      navigate({ to: '/dashboard' });
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Don't render landing page if user is authenticated (to prevent flash)
  if (isAuthenticated) {
    return null;
  }

  return <LandingLayout />;
}
