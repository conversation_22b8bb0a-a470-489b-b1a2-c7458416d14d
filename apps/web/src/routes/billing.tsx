import { createFileRoute } from '@tanstack/react-router';
import { useState } from 'react';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { SubscriptionDashboard } from '@/components/billing/subscription-dashboard';
import { BillingHistory } from '@/components/billing/billing-history';
import { AuthGuards } from '@/components/auth/enhanced-auth-guards';
import { 
  CreditCard, 
  History, 
  Settings, 
  TrendingUp,
  Shield
} from 'lucide-react';

export const Route = createFileRoute('/billing')({
  component: BillingPage,
});

function BillingPage() {
  console.log("💳 Rendering BillingPage");
  
  const [activeTab, setActiveTab] = useState("dashboard");

  return (
    <AuthGuards>
      <div className="min-h-screen bg-[var(--buddychip-dark-bg)] text-[var(--buddychip-white)]">
        <div className="container mx-auto px-4 py-8">
          {/* <PERSON> Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-[var(--buddychip-accent)]/10 rounded-lg">
                <CreditCard className="h-6 w-6 text-[var(--buddychip-accent)]" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-[var(--buddychip-white)]">
                  Billing & Subscription
                </h1>
                <p className="text-[var(--buddychip-grey-text)]">
                  Manage your subscription, view usage, and billing history
                </p>
              </div>
            </div>
          </div>

          {/* Billing Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4 bg-[var(--buddychip-light-bg)] border border-[var(--buddychip-grey-stroke)]">
              <TabsTrigger 
                value="dashboard" 
                className="flex items-center space-x-2 data-[state=active]:bg-[var(--buddychip-accent)] data-[state=active]:text-white"
              >
                <TrendingUp className="h-4 w-4" />
                <span className="hidden sm:inline">Dashboard</span>
              </TabsTrigger>
              <TabsTrigger 
                value="history"
                className="flex items-center space-x-2 data-[state=active]:bg-[var(--buddychip-accent)] data-[state=active]:text-white"
              >
                <History className="h-4 w-4" />
                <span className="hidden sm:inline">History</span>
              </TabsTrigger>
              <TabsTrigger 
                value="settings"
                className="flex items-center space-x-2 data-[state=active]:bg-[var(--buddychip-accent)] data-[state=active]:text-white"
              >
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline">Settings</span>
              </TabsTrigger>
              <TabsTrigger 
                value="security"
                className="flex items-center space-x-2 data-[state=active]:bg-[var(--buddychip-accent)] data-[state=active]:text-white"
              >
                <Shield className="h-4 w-4" />
                <span className="hidden sm:inline">Security</span>
              </TabsTrigger>
            </TabsList>

            {/* Dashboard Tab */}
            <TabsContent value="dashboard" className="space-y-6">
              <SubscriptionDashboard />
            </TabsContent>

            {/* History Tab */}
            <TabsContent value="history" className="space-y-6">
              <BillingHistory />
            </TabsContent>

            {/* Settings Tab */}
            <TabsContent value="settings" className="space-y-6">
              <BillingSettings />
            </TabsContent>

            {/* Security Tab */}
            <TabsContent value="security" className="space-y-6">
              <BillingSecurity />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </AuthGuards>
  );
}

/**
 * Billing settings component
 */
function BillingSettings() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-[var(--buddychip-white)] mb-2">
          Billing Settings
        </h2>
        <p className="text-[var(--buddychip-grey-text)]">
          Configure your billing preferences and notifications
        </p>
      </div>
      
      <div className="bg-[var(--buddychip-light-bg)] border border-[var(--buddychip-grey-stroke)] rounded-lg p-6">
        <h3 className="text-lg font-semibold text-[var(--buddychip-white)] mb-4">
          Email Notifications
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-[var(--buddychip-white)]">Payment confirmations</p>
              <p className="text-sm text-[var(--buddychip-grey-text)]">Receive email when payments are processed</p>
            </div>
            <input type="checkbox" defaultChecked className="toggle" />
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-[var(--buddychip-white)]">Usage alerts</p>
              <p className="text-sm text-[var(--buddychip-grey-text)]">Get notified when approaching usage limits</p>
            </div>
            <input type="checkbox" defaultChecked className="toggle" />
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-[var(--buddychip-white)]">Billing reminders</p>
              <p className="text-sm text-[var(--buddychip-grey-text)]">Reminders before subscription renewal</p>
            </div>
            <input type="checkbox" defaultChecked className="toggle" />
          </div>
        </div>
      </div>

      <div className="bg-[var(--buddychip-light-bg)] border border-[var(--buddychip-grey-stroke)] rounded-lg p-6">
        <h3 className="text-lg font-semibold text-[var(--buddychip-white)] mb-4">
          Billing Address
        </h3>
        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <label className="block text-sm font-medium text-[var(--buddychip-white)] mb-2">
              Country
            </label>
            <select className="w-full p-2 bg-[var(--buddychip-dark-bg)] border border-[var(--buddychip-grey-stroke)] rounded text-[var(--buddychip-white)]">
              <option>United States</option>
              <option>Canada</option>
              <option>United Kingdom</option>
              <option>Other</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-[var(--buddychip-white)] mb-2">
              Tax ID (Optional)
            </label>
            <input 
              type="text" 
              placeholder="Enter tax ID"
              className="w-full p-2 bg-[var(--buddychip-dark-bg)] border border-[var(--buddychip-grey-stroke)] rounded text-[var(--buddychip-white)] placeholder-[var(--buddychip-grey-text)]"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Billing security component
 */
function BillingSecurity() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-[var(--buddychip-white)] mb-2">
          Billing Security
        </h2>
        <p className="text-[var(--buddychip-grey-text)]">
          Manage security settings for your billing and payments
        </p>
      </div>

      <div className="bg-[var(--buddychip-light-bg)] border border-[var(--buddychip-grey-stroke)] rounded-lg p-6">
        <h3 className="text-lg font-semibold text-[var(--buddychip-white)] mb-4">
          Payment Security
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-[var(--buddychip-white)]">Two-factor authentication</p>
              <p className="text-sm text-[var(--buddychip-grey-text)]">Require 2FA for billing changes</p>
            </div>
            <span className="px-2 py-1 bg-green-600 text-white text-xs rounded">Enabled</span>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-[var(--buddychip-white)]">Payment method verification</p>
              <p className="text-sm text-[var(--buddychip-grey-text)]">Verify identity when adding payment methods</p>
            </div>
            <span className="px-2 py-1 bg-green-600 text-white text-xs rounded">Enabled</span>
          </div>
        </div>
      </div>

      <div className="bg-[var(--buddychip-light-bg)] border border-[var(--buddychip-grey-stroke)] rounded-lg p-6">
        <h3 className="text-lg font-semibold text-[var(--buddychip-white)] mb-4">
          Recent Activity
        </h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between py-2">
            <div>
              <p className="text-sm text-[var(--buddychip-white)]">Payment method updated</p>
              <p className="text-xs text-[var(--buddychip-grey-text)]">2 days ago</p>
            </div>
            <span className="text-xs text-green-500">Verified</span>
          </div>
          <div className="flex items-center justify-between py-2">
            <div>
              <p className="text-sm text-[var(--buddychip-white)]">Subscription upgraded</p>
              <p className="text-xs text-[var(--buddychip-grey-text)]">1 week ago</p>
            </div>
            <span className="text-xs text-green-500">Verified</span>
          </div>
        </div>
      </div>
    </div>
  );
}
