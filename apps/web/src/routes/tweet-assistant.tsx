import { createFileRoute } from '@tanstack/react-router';
import { useAuth } from '@/components/auth/use-auth';
import { AuthGuards } from '@/components/auth/route-guard';
import { EnhancedTweetAssistant } from '@/components/tweet-assistant/enhanced-tweet-assistant';
import { PageHeader } from '@/components/ui/page-header';
import { Wand2 } from 'lucide-react';

export const Route = createFileRoute('/tweet-assistant')({
  component: () => (
    <AuthGuards.Dashboard>
      <TweetAssistantComponent />
    </AuthGuards.Dashboard>
  ),
});

function TweetAssistantComponent() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0E1117] via-[#0F1419] to-[#0E1117] text-[#F5F7FA] flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 border-4 border-[#316FE3] border-t-transparent rounded-full animate-spin mx-auto" />
          <div className="text-lg text-[#6E7A8C]">Loading Tweet Assistant...</div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0E1117] via-[#0F1419] to-[#0E1117] text-[#F5F7FA] flex items-center justify-center">
        <div className="text-center space-y-6 max-w-md mx-auto p-8">
          <div className="w-16 h-16 bg-gradient-to-br from-[#316FE3] to-purple-600 rounded-2xl flex items-center justify-center mx-auto">
            <Wand2 className="h-8 w-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold mb-4 bg-gradient-to-r from-[#316FE3] to-purple-600 bg-clip-text text-transparent">
              Authentication Required
            </h1>
            <p className="text-[#6E7A8C] text-lg leading-relaxed">
              Please sign in to access the Tweet Assistant and start generating AI-powered responses.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0E1117] via-[#0F1419] to-[#0E1117] text-[#F5F7FA]">
      {/* Animated Background */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-[#316FE3]/5 rounded-full blur-3xl animate-pulse" />
        <div className="absolute top-40 right-20 w-48 h-48 bg-purple-500/3 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute bottom-20 left-1/3 w-40 h-40 bg-cyan-500/4 rounded-full blur-3xl animate-pulse delay-2000" />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <PageHeader
          title="Tweet Assistant"
          subtitle="AI-powered tweet analysis and response generation with advanced features"
        />

        <EnhancedTweetAssistant />
      </div>
    </div>
  );
}

