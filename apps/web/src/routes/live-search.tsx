import { createFileRoute } from '@tanstack/react-router'
import { lazy } from 'react'
import { AuthGuards } from '../components/auth/route-guard'

const LiveSearchPage = lazy(() => import('../components/pages/LiveSearchPage'))

function LiveSearchComponent() {
  return (
    <AuthGuards.Dashboard>
      <LiveSearchPage />
    </AuthGuards.Dashboard>
  )
}

export const Route = createFileRoute('/live-search')({
  component: LiveSearchComponent,
})