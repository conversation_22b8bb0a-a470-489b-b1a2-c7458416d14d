/**
 * Catch-all route for TanStack Router
 * Handles all unmatched routes and displays the 404 page
 * with intelligent redirect suggestions and analytics tracking.
 */

import { createFileRoute, useRouter } from "@tanstack/react-router";
import { useEffect } from "react";
import { NotFoundPage } from "@/components/error/not-found-page";
import { getUrlRedirectManager } from "@/lib/url-redirects";

export const Route = createFileRoute("/$splat")({
  component: CatchAllComponent,
});

function CatchAllComponent() {
  const router = useRouter();
  const splat = Route.useParams().splat;
  const requestedUrl = `/${splat}`;

  useEffect(() => {
    console.log('🔍 Catch-all route triggered for:', requestedUrl);
    
    // Check for automatic redirects
    const redirectManager = getUrlRedirectManager();
    const autoRedirectUrl = redirectManager.getAutoRedirect(requestedUrl);
    
    if (autoRedirectUrl) {
      console.log('🚀 Auto-redirecting from', requestedUrl, 'to', autoRedirectUrl);
      
      // Use replace to avoid adding to history
      router.navigate({ 
        to: autoRedirectUrl,
        replace: true,
      });
      
      return;
    }

    // No auto-redirect found, show 404 page
    console.log('❌ No auto-redirect found for:', requestedUrl);
  }, [requestedUrl, router]);

  return (
    <NotFoundPage 
      requestedUrl={requestedUrl}
      showSearch={true}
      showSuggestions={true}
    />
  );
}
