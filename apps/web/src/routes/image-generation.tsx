import { createFileRoute } from '@tanstack/react-router'
import { lazy } from 'react'
import { AuthGuards } from '../components/auth/route-guard'

const ImageGenerationPage = lazy(() => import('../components/pages/ImageGenerationPage'))

function ImageGenerationComponent() {
  return (
    <AuthGuards.Dashboard>
      <ImageGenerationPage />
    </AuthGuards.Dashboard>
  )
}

export const Route = createFileRoute('/image-generation')({
  component: ImageGenerationComponent,
})