/**
 * Dedicated 404 route
 * Provides a clean URL for 404 errors and can be used
 * for manual redirects or error handling.
 */

import { createFileRoute } from "@tanstack/react-router";
import { NotFoundPage } from "@/components/error/not-found-page";

export const Route = createFileRoute("/404")({
  component: NotFound404Component,
});

function NotFound404Component() {
  return (
    <NotFoundPage 
      message="This page could not be found."
      showSearch={true}
      showSuggestions={false} // Don't show suggestions for explicit 404 route
    />
  );
}
