import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { AuthLayout } from "@/components/auth/auth-layout";
import { ProgressIndicator } from "@/components/auth/progress-indicator";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { WelcomeStep } from "@/components/auth/onboarding-steps/welcome-step";
import { ProfileSetupStep } from "@/components/auth/onboarding-steps/profile-setup-step";
import { TwitterConnectStep } from "@/components/auth/onboarding-steps/twitter-connect-step";
import { PreferencesStep } from "@/components/auth/onboarding-steps/preferences-step";
import { CompleteStep } from "@/components/auth/onboarding-steps/complete-step";
import { useAuth } from "@/components/auth/use-auth";
import { LoadingSkeleton, OnboardingStepSkeleton } from "@/components/auth/loading-skeleton";

export const Route = createFileRoute("/onboarding")({
  component: OnboardingFlow,
});

function OnboardingFlow() {
  const [currentStep, setCurrentStep] = useState(1);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const totalSteps = 5;
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useAuth();

  console.log(`🚀 Onboarding flow - Current step: ${currentStep}/${totalSteps}, Auth loading: ${isLoading}, Authenticated: ${isAuthenticated}`);

  const stepLabels = [
    "Welcome",
    "Profile",
    "Twitter",
    "Preferences",
    "Complete"
  ];

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      console.log("🔒 User not authenticated, redirecting to sign-in");
      navigate({ to: "/sign-in" });
    }
  }, [isAuthenticated, isLoading, navigate]);

  const next = async () => {
    console.log(`➡️ Moving to next step from ${currentStep}`);
    setIsTransitioning(true);

    // Add a small delay for smooth transition
    await new Promise(resolve => setTimeout(resolve, 200));

    setCurrentStep((s) => Math.min(s + 1, totalSteps));
    setIsTransitioning(false);
  };

  const back = async () => {
    console.log(`⬅️ Moving to previous step from ${currentStep}`);
    setIsTransitioning(true);

    // Add a small delay for smooth transition
    await new Promise(resolve => setTimeout(resolve, 200));

    setCurrentStep((s) => Math.max(s - 1, 1));
    setIsTransitioning(false);
  };

  const StepComponent = () => {
    if (isTransitioning) {
      return <OnboardingStepSkeleton />;
    }

    switch (currentStep) {
      case 1:
        return <WelcomeStep onNext={next} />;
      case 2:
        return <ProfileSetupStep onNext={next} onBack={back} />;
      case 3:
        return <TwitterConnectStep onNext={next} onBack={back} />;
      case 4:
        return <PreferencesStep onNext={next} onBack={back} />;
      case 5:
      default:
        return <CompleteStep />;
    }
  };

  // Show loading skeleton while auth is loading
  if (isLoading) {
    return (
      <AuthLayout>
        <div className="space-y-6">
          <LoadingSkeleton variant="rectangular" height={8} />
          <OnboardingStepSkeleton />
        </div>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout>
      <div className="space-y-8">
        {/* Enhanced Progress Indicator */}
        <ProgressIndicator
          current={currentStep}
          total={totalSteps}
          variant="steps"
          showLabels={true}
          labels={stepLabels}
        />

        {/* Step Content with Animation */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="min-h-[400px]"
          >
            <StepComponent />
          </motion.div>
        </AnimatePresence>

        {/* Step Counter */}
        <div className="text-center">
          <p className="text-xs text-[#6E7A8C]">
            Step {currentStep} of {totalSteps} • {stepLabels[currentStep - 1]}
          </p>
        </div>
      </div>
    </AuthLayout>
  );
}