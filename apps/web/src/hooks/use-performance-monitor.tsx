/**
 * 🚀 PERFORMANCE MONITORING HOOK
 * 
 * Real-time performance tracking to monitor optimization effectiveness
 * and detect performance regressions early.
 */

import { useEffect, useRef, useState, useCallback } from 'react';

interface PerformanceMetrics {
  // Memory metrics
  usedHeap: number;
  totalHeap: number;
  heapLimit: number;
  
  // Timing metrics
  renderTime: number;
  queryTime: number;
  cacheHitRate: number;
  
  // Bundle metrics
  chunkLoadTimes: Record<string, number>;
  totalBundleSize: number;
  
  // Error metrics
  errorCount: number;
  lastError?: Error;
  
  // User interaction metrics
  timeToInteractive: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
}

interface PerformanceThresholds {
  maxMemoryUsage: number; // MB
  maxRenderTime: number; // ms
  minCacheHitRate: number; // percentage
  maxBundleSize: number; // KB
}

const DEFAULT_THRESHOLDS: PerformanceThresholds = {
  maxMemoryUsage: 100, // 100MB
  maxRenderTime: 100, // 100ms
  minCacheHitRate: 80, // 80%
  maxBundleSize: 1000, // 1MB
};

export function usePerformanceMonitor(
  options: {
    enabled?: boolean;
    thresholds?: Partial<PerformanceThresholds>;
    alertCallback?: (metric: string, value: number, threshold: number) => void;
  } = {}
) {
  const { 
    enabled = true, 
    thresholds = {}, 
    alertCallback 
  } = options;
  
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    usedHeap: 0,
    totalHeap: 0,
    heapLimit: 0,
    renderTime: 0,
    queryTime: 0,
    cacheHitRate: 0,
    chunkLoadTimes: {},
    totalBundleSize: 0,
    errorCount: 0,
    timeToInteractive: 0,
    firstContentfulPaint: 0,
    largestContentfulPaint: 0,
  });
  
  const performanceRef = useRef<{
    renderStartTime: number;
    queryStartTime: number;
    cacheHits: number;
    cacheMisses: number;
    errorCount: number;
  }>({
    renderStartTime: 0,
    queryStartTime: 0,
    cacheHits: 0,
    cacheMisses: 0,
    errorCount: 0,
  });
  
  const finalThresholds = { ...DEFAULT_THRESHOLDS, ...thresholds };

  // 🚀 MEMORY MONITORING
  const measureMemoryUsage = useCallback(() => {
    if (!enabled || typeof window === 'undefined') return;
    
    try {
      // @ts-ignore - performance.memory is available in Chrome
      const memory = performance.memory;
      if (memory) {
        const usedHeap = Math.round(memory.usedJSHeapSize / 1024 / 1024 * 100) / 100;
        const totalHeap = Math.round(memory.totalJSHeapSize / 1024 / 1024 * 100) / 100;
        const heapLimit = Math.round(memory.jsHeapSizeLimit / 1024 / 1024 * 100) / 100;
        
        setMetrics(prev => ({
          ...prev,
          usedHeap,
          totalHeap,
          heapLimit,
        }));
        
        // Alert if memory usage is too high
        if (usedHeap > finalThresholds.maxMemoryUsage) {
          alertCallback?.('memory', usedHeap, finalThresholds.maxMemoryUsage);
        }
      }
    } catch (error) {
      console.warn('Memory monitoring failed:', error);
    }
  }, [enabled, finalThresholds.maxMemoryUsage, alertCallback]);

  // 🚀 RENDER TIME MONITORING
  const measureRenderTime = useCallback(() => {
    if (!enabled) return { start: () => {}, end: () => {} };
    
    return {
      start: () => {
        performanceRef.current.renderStartTime = performance.now();
      },
      end: () => {
        const renderTime = performance.now() - performanceRef.current.renderStartTime;
        
        setMetrics(prev => ({
          ...prev,
          renderTime: Math.round(renderTime * 100) / 100,
        }));
        
        // Alert if render time is too slow
        if (renderTime > finalThresholds.maxRenderTime) {
          alertCallback?.('renderTime', renderTime, finalThresholds.maxRenderTime);
        }
      },
    };
  }, [enabled, finalThresholds.maxRenderTime, alertCallback]);

  // 🚀 CACHE HIT RATE MONITORING
  const trackCacheHit = useCallback((hit: boolean) => {
    if (!enabled) return;
    
    if (hit) {
      performanceRef.current.cacheHits++;
    } else {
      performanceRef.current.cacheMisses++;
    }
    
    const total = performanceRef.current.cacheHits + performanceRef.current.cacheMisses;
    const hitRate = total > 0 ? (performanceRef.current.cacheHits / total) * 100 : 0;
    
    setMetrics(prev => ({
      ...prev,
      cacheHitRate: Math.round(hitRate * 100) / 100,
    }));
    
    // Alert if cache hit rate is too low
    if (total > 10 && hitRate < finalThresholds.minCacheHitRate) {
      alertCallback?.('cacheHitRate', hitRate, finalThresholds.minCacheHitRate);
    }
  }, [enabled, finalThresholds.minCacheHitRate, alertCallback]);

  // 🚀 BUNDLE SIZE MONITORING
  const measureBundleSize = useCallback(() => {
    if (!enabled || typeof window === 'undefined') return;
    
    try {
      // Get resource timing for JavaScript bundles
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const jsResources = resources.filter(resource => 
        resource.name.includes('.js') && 
        !resource.name.includes('node_modules')
      );
      
      let totalSize = 0;
      const chunkLoadTimes: Record<string, number> = {};
      
      jsResources.forEach(resource => {
        const size = resource.transferSize || resource.encodedBodySize || 0;
        totalSize += size;
        
        const chunkName = resource.name.split('/').pop() || 'unknown';
        chunkLoadTimes[chunkName] = Math.round(resource.loadEnd - resource.loadStart);
      });
      
      const totalSizeKB = Math.round(totalSize / 1024 * 100) / 100;
      
      setMetrics(prev => ({
        ...prev,
        totalBundleSize: totalSizeKB,
        chunkLoadTimes,
      }));
      
      // Alert if bundle size is too large
      if (totalSizeKB > finalThresholds.maxBundleSize) {
        alertCallback?.('bundleSize', totalSizeKB, finalThresholds.maxBundleSize);
      }
    } catch (error) {
      console.warn('Bundle size monitoring failed:', error);
    }
  }, [enabled, finalThresholds.maxBundleSize, alertCallback]);

  // 🚀 WEB VITALS MONITORING
  const measureWebVitals = useCallback(() => {
    if (!enabled || typeof window === 'undefined') return;
    
    try {
      // Get navigation timing
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        const timeToInteractive = navigation.loadEventEnd - navigation.loadEventStart;
        
        setMetrics(prev => ({
          ...prev,
          timeToInteractive: Math.round(timeToInteractive),
        }));
      }
      
      // Get paint timing
      const paintEntries = performance.getEntriesByType('paint');
      const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
      if (fcp) {
        setMetrics(prev => ({
          ...prev,
          firstContentfulPaint: Math.round(fcp.startTime),
        }));
      }
      
      // Get LCP if available
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lcp = entries[entries.length - 1];
          if (lcp) {
            setMetrics(prev => ({
              ...prev,
              largestContentfulPaint: Math.round(lcp.startTime),
            }));
          }
        });
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
        
        // Cleanup observer after 10 seconds
        setTimeout(() => observer.disconnect(), 10000);
      }
    } catch (error) {
      console.warn('Web vitals monitoring failed:', error);
    }
  }, [enabled]);

  // 🚀 ERROR TRACKING
  const trackError = useCallback((error: Error) => {
    if (!enabled) return;
    
    performanceRef.current.errorCount++;
    
    setMetrics(prev => ({
      ...prev,
      errorCount: performanceRef.current.errorCount,
      lastError: error,
    }));
  }, [enabled]);

  // Set up monitoring intervals
  useEffect(() => {
    if (!enabled) return;
    
    // Initial measurements
    measureMemoryUsage();
    measureBundleSize();
    measureWebVitals();
    
    // Set up intervals
    const memoryInterval = setInterval(measureMemoryUsage, 5000); // Every 5 seconds
    const bundleInterval = setInterval(measureBundleSize, 30000); // Every 30 seconds
    
    // Cleanup
    return () => {
      clearInterval(memoryInterval);
      clearInterval(bundleInterval);
    };
  }, [enabled, measureMemoryUsage, measureBundleSize, measureWebVitals]);

  // Error boundary integration
  useEffect(() => {
    if (!enabled) return;
    
    const handleError = (event: ErrorEvent) => {
      trackError(new Error(event.message));
    };
    
    const handleRejection = (event: PromiseRejectionEvent) => {
      trackError(new Error(event.reason));
    };
    
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleRejection);
    
    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleRejection);
    };
  }, [enabled, trackError]);

  // Reset function
  const resetMetrics = useCallback(() => {
    performanceRef.current = {
      renderStartTime: 0,
      queryStartTime: 0,
      cacheHits: 0,
      cacheMisses: 0,
      errorCount: 0,
    };
    
    setMetrics({
      usedHeap: 0,
      totalHeap: 0,
      heapLimit: 0,
      renderTime: 0,
      queryTime: 0,
      cacheHitRate: 0,
      chunkLoadTimes: {},
      totalBundleSize: 0,
      errorCount: 0,
      timeToInteractive: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
    });
  }, []);

  return {
    metrics,
    measureRenderTime,
    trackCacheHit,
    trackError,
    resetMetrics,
    
    // Utility functions
    isPerformanceGood: () => {
      return (
        metrics.usedHeap < finalThresholds.maxMemoryUsage &&
        metrics.renderTime < finalThresholds.maxRenderTime &&
        metrics.cacheHitRate > finalThresholds.minCacheHitRate &&
        metrics.totalBundleSize < finalThresholds.maxBundleSize
      );
    },
    
    getPerformanceScore: () => {
      let score = 100;
      
      // Memory score (0-25 points)
      if (metrics.usedHeap > finalThresholds.maxMemoryUsage) {
        score -= Math.min(25, (metrics.usedHeap / finalThresholds.maxMemoryUsage - 1) * 50);
      }
      
      // Render time score (0-25 points)
      if (metrics.renderTime > finalThresholds.maxRenderTime) {
        score -= Math.min(25, (metrics.renderTime / finalThresholds.maxRenderTime - 1) * 50);
      }
      
      // Cache hit rate score (0-25 points)
      if (metrics.cacheHitRate < finalThresholds.minCacheHitRate) {
        score -= Math.min(25, (finalThresholds.minCacheHitRate - metrics.cacheHitRate) / 2);
      }
      
      // Bundle size score (0-25 points)
      if (metrics.totalBundleSize > finalThresholds.maxBundleSize) {
        score -= Math.min(25, (metrics.totalBundleSize / finalThresholds.maxBundleSize - 1) * 50);
      }
      
      return Math.max(0, Math.round(score));
    },
  };
}

/**
 * HOC for automatic render time tracking
 */
export function withPerformanceTracking<P extends object>(
  Component: React.ComponentType<P>,
  componentName: string = 'Component'
) {
  return function PerformanceTrackedComponent(props: P) {
    const { measureRenderTime } = usePerformanceMonitor();
    const renderTimer = measureRenderTime();
    
    useEffect(() => {
      renderTimer.start();
      return () => {
        renderTimer.end();
      };
    });
    
    return <Component {...props} />;
  };
}