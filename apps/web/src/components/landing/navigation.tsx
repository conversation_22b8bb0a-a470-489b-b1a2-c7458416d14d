import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { useAuth } from "@/components/auth/use-auth";
import { SignInButton, SignOutButton, SignedIn, SignedOut, useUser } from "@clerk/clerk-react";
import { useQuery } from 'convex/react';
import { api } from '@BuddyChipPro/backend';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Link } from "@tanstack/react-router";
import { WalletConnectionModal } from "@/components/wallet/wallet-connection-modal";
import { useState } from "react";
import IconButton from "../atoms/icon-button";
import { RiTwitterXLine } from "react-icons/ri";

export function Navigation() {
  const { isAuthenticated, isLoading } = useAuth();
  const { user } = useUser();
  const primaryWallet = useQuery(api.auth.walletDetection.getPrimaryWallet);
  const [showWalletModal, setShowWalletModal] = useState(false);

  const formatAddress = (address: string, length = 6) => {
    return `${address.slice(0, length)}...${address.slice(-length)}`;
  };

  return (
    <nav className="w-full z-50 flex justify-between items-center px-4 md:px-6 h-[60px] sm:h-[66px] md:h-[104px] animate__animated animate__fadeIn">
      <div className="flex items-center gap-2">
        <img src="/Logo.svg" alt="logo" width={48} height={48} className="sm:w-[30px] sm:h-[30px]" />
        <span className="hidden sm:block text-white text-lg sm:text-xl md:text-2xl font-manrope">BuddyChip</span>
      </div>
      
      <div className="flex items-center gap-1 sm:gap-2">
        {/* Social Icons */}
        <IconButton 
          icon={RiTwitterXLine} 
          onClick={() => window.open('https://x.com/BuddychipAI', '_blank')} 
          className="hover:bg-[#316FE3] hover:text-[#F5F7FA] w-8 h-8 sm:w-10 sm:h-10" 
        />
        
        <SignedOut>
          {!isLoading && (
            <>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => window.open('https://t.me/+4n7CEX8GKApkMmQ0', '_blank')}
                className="border border-[var(--buddychip-grey-stroke)] bg-transparent text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]"
              >
                Access Beta!
              </Button>
              <SignInButton>
                <Button 
                  size="sm"
                  className="bg-[var(--buddychip-accent)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent)]/80"
                >
                  Sign In
                </Button>
              </SignInButton>
            </>
          )}
        </SignedOut>
        
        <SignedIn>
          {!isLoading && (
            <>
              <Link to="/dashboard">
                <Button 
                  size="sm"
                  className="bg-[var(--buddychip-accent)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent)]/80 flex items-center space-x-1"
                >
                  <span>Dashboard</span>
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    className="relative h-10 flex items-center gap-2 hover:bg-[var(--buddychip-grey-stroke)]"
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user?.imageUrl} alt={user?.fullName || 'User'} />
                      <AvatarFallback className="bg-[var(--buddychip-accent)] text-[var(--buddychip-white)]">
                        {(user?.fullName || user?.firstName || 'U').charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="text-left">
                      <p className="text-sm font-medium text-[var(--buddychip-white)]">
                        {user?.fullName || user?.firstName || 'User'}
                      </p>
                      {primaryWallet && (
                        <p className="text-xs text-[var(--buddychip-grey-text)] font-mono">
                          ◎ {formatAddress(primaryWallet.address)}
                        </p>
                      )}
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-64 bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
                  <div className="flex items-center gap-3 p-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={user?.imageUrl} alt={user?.fullName || 'User'} />
                      <AvatarFallback className="bg-[var(--buddychip-accent)] text-[var(--buddychip-white)]">
                        {(user?.fullName || user?.firstName || 'U').charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium text-[var(--buddychip-white)]">
                        {user?.fullName || user?.firstName || 'User'}
                      </p>
                      <p className="text-xs text-[var(--buddychip-grey-text)]">
                        {user?.primaryEmailAddress?.emailAddress}
                      </p>
                    </div>
                  </div>
                  
                  <DropdownMenuSeparator />
                  
                  {primaryWallet ? (
                    <div className="px-3 py-2">
                      <div className="flex items-center gap-2">
                        <span>◎</span>
                        <span className="font-mono text-xs text-[var(--buddychip-white)]">
                          {formatAddress(primaryWallet.address)}
                        </span>
                        <Badge variant="secondary" className="text-xs">Primary</Badge>
                      </div>
                    </div>
                  ) : (
                    <DropdownMenuItem onClick={() => setShowWalletModal(true)} className="text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent)]/10">
                      Connect Wallet
                    </DropdownMenuItem>
                  )}
                  
                  <DropdownMenuSeparator />
                  
                  <SignOutButton>
                    <DropdownMenuItem className="text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent)]/10 cursor-pointer">
                      Sign Out
                    </DropdownMenuItem>
                  </SignOutButton>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          )}
        </SignedIn>
      </div>
      
      <WalletConnectionModal
        isOpen={showWalletModal}
        onClose={() => setShowWalletModal(false)}
        blockchain="solana"
      />
    </nav>
  );
}