import React, { useRef, useEffect } from 'react';
import { MdArrowOutward } from 'react-icons/md';
import PrimaryButton from '../atoms/button';
import { useAuth } from '@/components/auth/use-auth';
import { SignInButton } from '@clerk/clerk-react';
import { useNavigate } from '@tanstack/react-router';

export function HeroSection() {
  const videoRef = useRef<HTMLVideoElement>(null);
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();

  const handleStartJourney = () => {
    if (isAuthenticated) {
      navigate({ to: '/dashboard' });
    }
    // For unauthenticated users, we'll wrap the button in SignInButton
  };

  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      const handleLoadedMetadata = () => {
        const playPromise = video.play();
        if (playPromise !== undefined) {
          playPromise.catch(() => {
            console.log('Video autoplay failed - user interaction required');
          });
        }
      };
      
      video.addEventListener('loadedmetadata', handleLoadedMetadata);
      return () => video.removeEventListener('loadedmetadata', handleLoadedMetadata);
    }
  }, []);

  return (
    <header className="min-h-[100vh] flex flex-col items-end justify-between relative overflow-hidden">

      {/* Desktop Content */}
      <div className="z-10 hidden md:flex flex-1 flex-col items-start justify-end p-6">
        {isAuthenticated ? (
          <PrimaryButton 
            icon={MdArrowOutward} 
            onClick={handleStartJourney} 
            iconVariant="tertiary" 
            className="animate__animated animate__slideInLeft mb-4"
          >
            Start your Yap Journey!
          </PrimaryButton>
        ) : (
          <SignInButton>
            <PrimaryButton 
              icon={MdArrowOutward} 
              onClick={() => {}} 
              iconVariant="tertiary" 
              className="animate__animated animate__slideInLeft mb-4"
            >
              Start your Yap Journey!
            </PrimaryButton>
          </SignInButton>
        )}
        <h1 className="w-[80%] tracking-[1.5px] text-white text-[78px] leading-[5rem] animate__animated animate__slideInUp">BuddyChip — AI-Powered Twitter Assistant That Never Sleeps</h1>
      </div>

      {/* Mobile Content */}
      <div className="z-10 md:hidden w-full flex flex-col justify-between flex-1 p-4 pt-8 pb-8">
        {/* Dark overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/20 to-black/40 md:hidden"></div>
        
        {/* Title at top */}
        <div className="relative z-20 flex flex-col items-center mt-8">
          <h1 className="w-full text-center tracking-[1px] text-white text-[32px] leading-tight font-medium animate__animated animate__slideInDown">
            BuddyChip — AI-Powered Twitter Assistant
          </h1>
        </div>

        {/* Bottom content */}
        <div className="relative z-20 flex flex-col items-center gap-6">
          <div className="max-w-sm rounded-lg border border-[var(--buddychip-grey-stroke)] bg-[var(--buddychip-black)]/80 p-3 text-sm text-[var(--buddychip-grey-text)] animate__animated animate__slideInUp">
            AI-powered mentions, multi-model responses, and real-time analytics — all in one powerful Twitter assistant.
          </div>
          {isAuthenticated ? (
            <PrimaryButton 
              icon={MdArrowOutward} 
              onClick={handleStartJourney} 
              iconVariant="tertiary" 
              className="animate__animated animate__slideInLeft"
            >
              Start your Yap Journey!
            </PrimaryButton>
          ) : (
            <SignInButton>
              <PrimaryButton 
                icon={MdArrowOutward} 
                onClick={() => {}} 
                iconVariant="tertiary" 
                className="animate__animated animate__slideInLeft"
              >
                Start your Yap Journey!
              </PrimaryButton>
            </SignInButton>
          )}
        </div>
      </div>
      
      <div className="z-10 hidden md:block absolute top-[110px] right-[24px] max-w-[440px] rounded-lg border border-[var(--buddychip-grey-stroke)] bg-[var(--buddychip-black)]/80 p-4 text-[var(--buddychip-grey-text)] animate__animated animate__slideInUp">
        AI-powered mentions, multi-model responses, and real-time analytics — all in one powerful Twitter assistant.
      </div>
     
      <video
        ref={videoRef}
        className="w-full h-full absolute top-0 left-0 object-cover"
        autoPlay
        muted
        loop
        playsInline
        controls={false}
        preload="metadata"
        poster="/headerBackground-poster.jpg"
        onError={() => console.log('Video failed to load')}
        onLoadedMetadata={() => console.log('Video metadata loaded')}
      >
        <source src="https://yo2pcnft8a.ufs.sh/f/nskFA2JaD20hOZDzeVHTrvafW1kp0QAyPq7j4wnhxbNoV63e" type="video/mp4" />
        Your browser does not support the video tag.
      </video>
    </header>
  );
}