import React from 'react';
import FeaturedGrid from '../featured-grid';
import MovingTextCarousel from '../moving-text-carousel';

export function FeaturesSection() {
  return (
    <section className="min-h-[100vh] flex items-center justify-center relative py-4 px-3">
      <div className="w-full max-w-[95vw] md:max-w-[90vw] min-h-[90vh] mx-auto bg-[#F5F7FA] flex flex-col gap-6 md:flex-row items-stretch justify-center rounded-[16px] md:rounded-[20px] p-5 md:p-6 pb-[3rem] md:pb-[6rem] relative overflow-hidden">
        <div className="md:w-[48%] w-full flex flex-col justify-between min-h-[120px] md:min-h-full">
          <h1 className="text-[#000000] text-[28px] sm:text-[32px] md:text-[48px] lg:text-[68px] w-full md:w-[90%] leading-tight md:leading-[3rem] lg:leading-[5rem] mb-4 md:mb-0 font-medium">
            Powerful AI Features for Twitter Success
          </h1>
          <img src="/Logo.svg" alt="services" width={80} height={80} className='hidden md:block md:w-[100px] md:h-[100px] lg:w-[120px] lg:h-[120px]'/>
        </div>
        <div className="md:w-[52%] w-full flex-1 flex items-center justify-center rounded-[12px] md:rounded-[12px] min-h-[450px] md:min-h-full">
          <FeaturedGrid />
        </div>
        <div className="absolute bottom-0 left-0 w-full h-[45px] md:h-[50px] rounded-b-[16px] md:rounded-b-[20px] overflow-hidden">
          <MovingTextCarousel />
        </div>
      </div>
    </section>
  );
}