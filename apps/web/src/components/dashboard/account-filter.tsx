import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { TwitterAccount } from "@/types/responses";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, Filter, Users, Plus } from "lucide-react";
import { AddAccountModal } from "./add-account-modal";

interface AccountFilterProps {
  accounts?: TwitterAccount[];
  selectedAccountIds?: string[];
  onAccountToggle?: (accountId: string) => void;
  onSelectAll?: () => void;
  onClearAll?: () => void;
  onAccountAdded?: () => void;
}

export function AccountFilter({
  accounts,
  selectedAccountIds = [], // Default to empty array
  onAccountToggle,
  onSelectAll,
  onClearAll,
  onAccountAdded,
}: AccountFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  
  // Add null checks
  const safeSelectedIds = selectedAccountIds || [];
  const safeAccounts = accounts || [];
  
  const allSelected = safeSelectedIds.length === safeAccounts.length && safeAccounts.length > 0;
  const someSelected = safeSelectedIds.length > 0 && safeSelectedIds.length < safeAccounts.length;
  const noneSelected = safeSelectedIds.length === 0;

  const getFilterText = () => {
    if (noneSelected) return "All Accounts";
    if (allSelected) return "All Accounts";
    if (safeSelectedIds.length === 1) {
      const account = safeAccounts.find(acc => acc._id === safeSelectedIds[0]);
      return `@${account?.handle || 'unknown'}`;
    }
    return `${safeSelectedIds.length} accounts`;
  };

  return (
    <>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="bg-[var(--buddychip-grey-stroke)]/50 border border-[var(--buddychip-grey-stroke)]/50 text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/70 hover:border-[var(--buddychip-accent)]/50 hover:text-[var(--buddychip-white)] transition-all duration-200"
          >
            <Filter className="h-4 w-4 mr-2" />
            {getFilterText()}
            <ChevronDown className="h-4 w-4 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent 
          align="start" 
          className="w-64 bg-[var(--buddychip-black)]/95 backdrop-blur-sm border border-[var(--buddychip-grey-stroke)]/50 text-[var(--buddychip-white)]"
        >
          <DropdownMenuLabel className="flex items-center text-[var(--buddychip-white)]">
            <Users className="h-4 w-4 mr-2" />
            Filter by Account
          </DropdownMenuLabel>
          <DropdownMenuSeparator className="bg-[var(--buddychip-grey-stroke)]/30" />
          
          {/* Select All/Clear All Controls */}
          <div className="flex items-center justify-between px-2 py-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onSelectAll?.()}
              className="text-xs text-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/20 h-6 px-2"
            >
              Select All
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onClearAll?.()}
              className="text-xs text-[var(--buddychip-grey-text)] hover:bg-[var(--buddychip-grey-stroke)]/50 h-6 px-2"
            >
              Clear All
            </Button>
          </div>
          
          <DropdownMenuSeparator className="bg-[var(--buddychip-grey-stroke)]/30" />
          
          {/* Account List */}
          <div className="max-h-64 overflow-y-auto">
            {safeAccounts.map((account) => {
              const isSelected = safeSelectedIds.includes(account._id);
              return (
                <DropdownMenuItem
                  key={account._id}
                  className="flex items-center space-x-3 px-3 py-2 cursor-pointer hover:bg-[var(--buddychip-grey-stroke)]/30 focus:bg-[var(--buddychip-grey-stroke)]/30"
                  onSelect={(e) => {
                    e.preventDefault();
                    onAccountToggle?.(account._id);
                  }}
                >
                  <Checkbox
                    checked={isSelected}
                    onChange={() => onAccountToggle?.(account._id)}
                    className="border-[var(--buddychip-grey-stroke)] data-[state=checked]:bg-[var(--buddychip-accent)] data-[state=checked]:border-[var(--buddychip-accent)]"
                  />
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <div className="h-8 w-8 rounded-full bg-[var(--buddychip-accent)]/20 flex items-center justify-center flex-shrink-0">
                      <span className="text-[var(--buddychip-accent)] text-xs font-semibold">
                        {account.displayName?.charAt(0)?.toUpperCase() || '?'}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-[var(--buddychip-white)] truncate">
                        {account.displayName || 'Unknown'}
                      </div>
                      <div className="text-xs text-[var(--buddychip-grey-text)] truncate">
                        @{account.handle || 'unknown'}
                      </div>
                    </div>
                    <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                      account.isActive 
                        ? 'bg-green-500/20 text-green-400' 
                        : 'bg-red-500/20 text-red-400'
                    }`}>
                      <div className={`h-1.5 w-1.5 rounded-full ${
                        account.isActive ? 'bg-green-500' : 'bg-red-500'
                      }`} />
                      <span className="hidden sm:inline">
                        {account.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </DropdownMenuItem>
              );
            })}
          </div>
          
          {safeAccounts.length === 0 && (
            <div className="px-3 py-6 text-center text-[var(--buddychip-grey-text)]">
              <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm mb-3">No accounts added yet</p>
              <Button
                onClick={() => {
                  setIsOpen(false);
                  setShowAddModal(true);
                }}
                size="sm"
                className="bg-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/80 text-[var(--buddychip-white)]"
              >
                <Plus className="h-3 w-3 mr-1" />
                Add Account
              </Button>
            </div>
          )}
          
          {/* Add Account Button for when accounts exist */}
          {safeAccounts.length > 0 && (
            <>
              <DropdownMenuSeparator className="bg-[var(--buddychip-grey-stroke)]/30" />
              <div className="px-2 py-2">
                <Button
                  onClick={() => {
                    setIsOpen(false);
                    setShowAddModal(true);
                  }}
                  variant="ghost"
                  size="sm"
                  className="w-full text-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/20 h-8"
                >
                  <Plus className="h-3 w-3 mr-2" />
                  Add New Account
                </Button>
              </div>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      
      {/* Add Account Modal */}
      <AddAccountModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={() => {
          onAccountAdded?.();
          setShowAddModal(false);
        }}
      />
    </>
  );
}