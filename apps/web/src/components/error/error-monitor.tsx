/**
 * Error Monitoring Component
 * Shows real-time error metrics and system health
 */

import { useState, useEffect } from "react";
import { Card } from "../ui/card";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import { AlertTriangle, Activity, Clock, TrendingDown, RefreshCw } from "lucide-react";

interface ErrorMetrics {
  errorCount: number;
  errorsByType: Record<string, number>;
  errorsByOperation: Record<string, number>;
  lastErrors: Array<{
    timestamp: number;
    type: string;
    operation: string;
    message: string;
  }>;
}

interface ErrorMonitorProps {
  className?: string;
}

export function ErrorMonitor({ className = "" }: ErrorMonitorProps) {
  const [metrics, setMetrics] = useState<ErrorMetrics | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchMetrics = async () => {
    try {
      // This would connect to your error monitoring endpoint
      // For now, we'll simulate metrics from localStorage
      const storedErrors = JSON.parse(localStorage.getItem('error_metrics') || '{}');
      setMetrics(storedErrors);
    } catch (error) {
      console.error('Failed to fetch error metrics:', error);
    }
  };

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      fetchMetrics();
      
      if (autoRefresh) {
        const interval = setInterval(fetchMetrics, 30000); // Refresh every 30 seconds
        return () => clearInterval(interval);
      }
    }
  }, [autoRefresh]);

  // Only show in development mode
  if (process.env.NODE_ENV !== 'development' || !isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        size="sm"
        variant="ghost"
        className="fixed bottom-4 right-4 z-50 bg-[var(--buddychip-card-bg)] border border-[var(--buddychip-border)]"
      >
        <Activity className="h-4 w-4 mr-1" />
        Error Monitor
      </Button>
    );
  }

  if (!metrics) {
    return (
      <Card className={`fixed bottom-4 right-4 w-80 z-50 bg-[var(--buddychip-card-bg)] border-[var(--buddychip-border)] ${className}`}>
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-semibold text-[var(--buddychip-white)]">Error Monitor</h3>
            <Button
              onClick={() => setIsVisible(false)}
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
          <p className="text-sm text-[var(--buddychip-grey-text)]">Loading metrics...</p>
        </div>
      </Card>
    );
  }

  const totalErrors = metrics.errorCount || 0;
  const recentErrors = metrics.lastErrors?.filter(
    error => Date.now() - error.timestamp < 300000 // Last 5 minutes
  ) || [];

  const getErrorTypeColor = (type: string) => {
    switch (type) {
      case 'RATE_LIMIT_EXCEEDED':
        return 'bg-orange-500/20 text-orange-300';
      case 'AUTHENTICATION_REQUIRED':
        return 'bg-red-500/20 text-red-300';
      case 'EXTERNAL_API_ERROR':
        return 'bg-blue-500/20 text-blue-300';
      case 'QUOTA_EXCEEDED':
        return 'bg-purple-500/20 text-purple-300';
      default:
        return 'bg-gray-500/20 text-gray-300';
    }
  };

  const getHealthStatus = () => {
    if (recentErrors.length === 0) return { status: 'healthy', color: 'text-green-400' };
    if (recentErrors.length < 5) return { status: 'warning', color: 'text-yellow-400' };
    return { status: 'critical', color: 'text-red-400' };
  };

  const health = getHealthStatus();

  return (
    <Card className={`fixed bottom-4 right-4 w-80 max-h-96 overflow-y-auto z-50 bg-[var(--buddychip-card-bg)] border-[var(--buddychip-border)] ${className}`}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Activity className="h-4 w-4 text-[var(--buddychip-accent)]" />
            <h3 className="text-sm font-semibold text-[var(--buddychip-white)]">Error Monitor</h3>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              onClick={fetchMetrics}
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
            <Button
              onClick={() => setIsVisible(false)}
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        </div>

        {/* System Health */}
        <div className="mb-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-[var(--buddychip-white)]">System Health</span>
            <span className={`text-sm font-medium ${health.color}`}>
              {health.status.toUpperCase()}
            </span>
          </div>
          <div className="flex items-center space-x-4 mt-2 text-xs text-[var(--buddychip-grey-text)]">
            <div className="flex items-center space-x-1">
              <AlertTriangle className="h-3 w-3" />
              <span>Total: {totalErrors}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>Recent: {recentErrors.length}</span>
            </div>
          </div>
        </div>

        {/* Error Types */}
        {Object.keys(metrics.errorsByType || {}).length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-[var(--buddychip-white)] mb-2">Error Types</h4>
            <div className="space-y-1">
              {Object.entries(metrics.errorsByType).map(([type, count]) => (
                <div key={type} className="flex items-center justify-between">
                  <Badge variant="outline" className={`text-xs ${getErrorTypeColor(type)}`}>
                    {type.replace('_', ' ')}
                  </Badge>
                  <span className="text-xs text-[var(--buddychip-grey-text)]">{count}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Recent Errors */}
        {recentErrors.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-[var(--buddychip-white)] mb-2">Recent Errors</h4>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {recentErrors.slice(0, 5).map((error, index) => (
                <div key={index} className="p-2 rounded border border-[var(--buddychip-border)] bg-[var(--buddychip-black)]">
                  <div className="flex items-center justify-between mb-1">
                    <Badge variant="outline" className={`text-xs ${getErrorTypeColor(error.type)}`}>
                      {error.operation}
                    </Badge>
                    <span className="text-xs text-[var(--buddychip-grey-text)]">
                      {new Date(error.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <p className="text-xs text-[var(--buddychip-grey-text)] truncate">
                    {error.message}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Controls */}
        <div className="mt-4 pt-3 border-t border-[var(--buddychip-border)]">
          <div className="flex items-center justify-between">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="w-3 h-3"
              />
              <span className="text-xs text-[var(--buddychip-grey-text)]">Auto refresh</span>
            </label>
            <Button
              onClick={() => {
                localStorage.removeItem('error_metrics');
                setMetrics({ errorCount: 0, errorsByType: {}, errorsByOperation: {}, lastErrors: [] });
              }}
              size="sm"
              variant="ghost"
              className="text-xs text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
            >
              Clear
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
}

/**
 * Error tracking utility for frontend errors
 */
export class ErrorTracker {
  private static instance: ErrorTracker;
  private metrics: ErrorMetrics = {
    errorCount: 0,
    errorsByType: {},
    errorsByOperation: {},
    lastErrors: []
  };

  static getInstance(): ErrorTracker {
    if (!ErrorTracker.instance) {
      ErrorTracker.instance = new ErrorTracker();
    }
    return ErrorTracker.instance;
  }

  trackError(error: any, operation: string = 'unknown') {
    const errorType = this.classifyError(error);
    
    this.metrics.errorCount++;
    this.metrics.errorsByType[errorType] = (this.metrics.errorsByType[errorType] || 0) + 1;
    this.metrics.errorsByOperation[operation] = (this.metrics.errorsByOperation[operation] || 0) + 1;
    
    this.metrics.lastErrors.unshift({
      timestamp: Date.now(),
      type: errorType,
      operation,
      message: error?.message || String(error)
    });
    
    // Keep only last 50 errors
    this.metrics.lastErrors = this.metrics.lastErrors.slice(0, 50);
    
    // Store in localStorage for the monitor
    localStorage.setItem('error_metrics', JSON.stringify(this.metrics));
  }

  private classifyError(error: any): string {
    const message = error?.message?.toLowerCase() || '';
    
    if (message.includes('rate limit') || error?.statusCode === 429) {
      return 'RATE_LIMIT_EXCEEDED';
    }
    if (message.includes('auth') || error?.statusCode === 401) {
      return 'AUTHENTICATION_REQUIRED';
    }
    if (message.includes('network') || message.includes('fetch')) {
      return 'NETWORK_ERROR';
    }
    if (message.includes('quota') || error?.statusCode === 402) {
      return 'QUOTA_EXCEEDED';
    }
    if (message.includes('validation') || error?.statusCode === 400) {
      return 'VALIDATION_ERROR';
    }
    
    return 'UNKNOWN_ERROR';
  }

  getMetrics(): ErrorMetrics {
    return { ...this.metrics };
  }

  clearMetrics() {
    this.metrics = {
      errorCount: 0,
      errorsByType: {},
      errorsByOperation: {},
      lastErrors: []
    };
    localStorage.removeItem('error_metrics');
  }
}

// Global error handler setup
if (typeof window !== 'undefined') {
  const tracker = ErrorTracker.getInstance();
  
  // Catch unhandled promises
  window.addEventListener('unhandledrejection', (event) => {
    tracker.trackError(event.reason, 'unhandled_promise');
  });
  
  // Catch JavaScript errors
  window.addEventListener('error', (event) => {
    tracker.trackError(event.error, 'javascript_error');
  });
}