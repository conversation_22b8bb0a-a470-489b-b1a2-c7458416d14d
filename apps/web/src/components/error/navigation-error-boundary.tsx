/**
 * Navigation Error Boundary
 * Catches navigation errors and routing failures, providing fallback UI
 * and analytics tracking for debugging navigation issues.
 */

import { Component, ReactNode } from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { Card } from "../ui/card";
import { AlertTriangle, Home, ArrowLeft, RefreshCw } from "lucide-react";
import { ErrorDisplay } from "./error-display";
import { ErrorTracker } from "./error-monitor";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: any;
  retryCount: number;
}

export class NavigationErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { 
      hasError: false,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    console.error('🔴 Navigation Error Boundary Caught:', error);
    return { 
      hasError: true, 
      error,
      retryCount: 0,
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error("🔴 Navigation Error Details:", {
      error: error.message,
      stack: error.stack,
      errorInfo,
      url: window.location.href,
      timestamp: new Date().toISOString(),
    });
    
    this.setState({ errorInfo });
    
    // Track error with enhanced error tracker
    const tracker = ErrorTracker.getInstance();
    tracker.trackError(error, 'navigation');
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
    
    // Track the navigation error for analytics (legacy)
    this.trackNavigationError(error, errorInfo);
  }

  private async trackNavigationError(error: Error, errorInfo: any) {
    try {
      const errorData = {
        type: 'navigation_error',
        message: error.message,
        stack: error.stack,
        errorInfo,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
      };

      console.log('📊 Tracking navigation error:', errorData);

      // Store in localStorage for debugging (will be picked up by analytics)
      const existingErrors = JSON.parse(localStorage.getItem('navigation_errors') || '[]');
      existingErrors.push(errorData);
      localStorage.setItem('navigation_errors', JSON.stringify(existingErrors.slice(-10))); // Keep last 10 errors

    } catch (trackingError) {
      console.error('❌ Failed to track navigation error:', trackingError);
    }
  }

  private handleRetry = () => {
    console.log('🔄 Retrying navigation after error...');
    this.setState(prevState => ({ 
      hasError: false, 
      error: undefined, 
      errorInfo: undefined,
      retryCount: prevState.retryCount + 1,
    }));
  };

  private handleGoHome = () => {
    console.log('🏠 Navigating to home after navigation error...');
    window.location.href = '/';
  };

  private handleGoBack = () => {
    console.log('⬅️ Going back after navigation error...');
    if (window.history.length > 1) {
      window.history.back();
    } else {
      this.handleGoHome();
    }
  };

  private handleReload = () => {
    console.log('🔄 Reloading page after navigation error...');
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Use enhanced error display
      return (
        <div className="min-h-screen bg-[var(--buddychip-app-bg)] flex items-center justify-center p-4">
          <div className="max-w-md w-full">
            <ErrorDisplay
              error={this.state.error}
              onRetry={this.handleRetry}
              operation="Navigation"
              className="mb-4"
            />
            
            {/* Additional navigation-specific actions */}
            <Card className="bg-[var(--buddychip-card-bg)] border-[var(--buddychip-border)]">
              <div className="p-4 text-center space-y-3">
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    onClick={this.handleGoBack}
                    variant="outline"
                    className="border-[var(--buddychip-border)] text-[var(--buddychip-grey-text)] hover:bg-[var(--buddychip-grey-stroke)]"
                  >
                    <ArrowLeft className="h-4 w-4 mr-1" />
                    Go Back
                  </Button>
                  
                  <Button
                    onClick={this.handleGoHome}
                    variant="outline"
                    className="border-[var(--buddychip-border)] text-[var(--buddychip-grey-text)] hover:bg-[var(--buddychip-grey-stroke)]"
                  >
                    <Home className="h-4 w-4 mr-1" />
                    Home
                  </Button>
                </div>

                <Button
                  onClick={this.handleReload}
                  variant="ghost"
                  className="w-full text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
                >
                  Reload Page
                </Button>
                
                {/* Retry count indicator */}
                {this.state.retryCount > 0 && (
                  <div className="text-xs text-[var(--buddychip-grey-text)]">
                    Retry attempt: {this.state.retryCount}
                  </div>
                )}
              </div>
            </Card>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook version for functional components
 */
export function useNavigationErrorHandler() {
  const handleNavigationError = (error: Error) => {
    console.error('🔴 Navigation error handled:', error);
    
    // Track the error
    const errorData = {
      type: 'navigation_error_hook',
      message: error.message,
      stack: error.stack,
      url: window.location.href,
      timestamp: Date.now(),
    };

    // Store for analytics
    const existingErrors = JSON.parse(localStorage.getItem('navigation_errors') || '[]');
    existingErrors.push(errorData);
    localStorage.setItem('navigation_errors', JSON.stringify(existingErrors.slice(-10)));
  };

  return { handleNavigationError };
}

/**
 * Higher-order component for wrapping components with navigation error boundary
 */
export function withNavigationErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  return function WrappedComponent(props: P) {
    return (
      <NavigationErrorBoundary fallback={fallback}>
        <Component {...props} />
      </NavigationErrorBoundary>
    );
  };
}
