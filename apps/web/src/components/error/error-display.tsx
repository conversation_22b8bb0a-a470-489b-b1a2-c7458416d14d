/**
 * Enhanced Error Display Component
 * Shows user-friendly error messages with recovery suggestions
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>fresh<PERSON><PERSON>, Clock, WifiOff, Shield, DollarSign } from "lucide-react";
import { Button } from "../ui/button";
import { Card } from "../ui/card";

interface ErrorDisplayProps {
  error: any;
  onRetry?: () => void;
  onDismiss?: () => void;
  operation?: string;
  className?: string;
}

interface ParsedError {
  type: 'rate_limit' | 'auth' | 'network' | 'quota' | 'validation' | 'generic';
  message: string;
  suggestions: string[];
  retryAfter?: number;
  statusCode?: number;
}

function parseError(error: any): ParsedError {
  const message = error?.message || 'An unexpected error occurred';
  const statusCode = error?.statusCode || error?.status;
  
  // Extract suggestions if available
  const suggestions = error?.context?.suggestions || error?.suggestions || [];
  
  // Determine error type based on message content
  if (message.includes('rate limit') || statusCode === 429) {
    return {
      type: 'rate_limit',
      message: 'Rate limit exceeded. Please wait a moment before trying again.',
      suggestions: [
        'Wait a few minutes before retrying',
        'Reduce the frequency of your requests',
        ...suggestions
      ]
    };
  }
  
  if (message.includes('Authentication') || message.includes('unauthorized') || statusCode === 401) {
    return {
      type: 'auth',
      message: 'Authentication required. Please sign in to continue.',
      suggestions: [
        'Sign in to your account',
        'Check if your session has expired',
        'Refresh the page and try again',
        ...suggestions
      ]
    };
  }
  
  if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
    return {
      type: 'network',
      message: 'Network connection issue. Please check your internet and try again.',
      suggestions: [
        'Check your internet connection',
        'Try again in a few seconds',
        'Refresh the page if the problem persists',
        ...suggestions
      ]
    };
  }
  
  if (message.includes('quota') || message.includes('Usage quota') || statusCode === 402) {
    return {
      type: 'quota',
      message: 'Usage quota exceeded. Please upgrade your plan or wait for quota reset.',
      suggestions: [
        'Consider upgrading your plan',
        'Wait until your quota resets',
        'Contact support for more information',
        ...suggestions
      ]
    };
  }
  
  if (message.includes('validation') || message.includes('Invalid') || statusCode === 400) {
    return {
      type: 'validation',
      message: 'Invalid input. Please check your request and try again.',
      suggestions: [
        'Check your input for errors',
        'Make sure all required fields are filled',
        'Verify the format of your data',
        ...suggestions
      ]
    };
  }
  
  return {
    type: 'generic',
    message: 'Something went wrong. Please try again.',
    suggestions: suggestions.length > 0 ? suggestions : [
      'Try refreshing the page',
      'Wait a moment and try again',
      'Contact support if the problem persists'
    ]
  };
}

function getErrorIcon(type: ParsedError['type']) {
  switch (type) {
    case 'rate_limit':
      return <Clock className="h-6 w-6 text-orange-500" />;
    case 'auth':
      return <Shield className="h-6 w-6 text-red-500" />;
    case 'network':
      return <WifiOff className="h-6 w-6 text-blue-500" />;
    case 'quota':
      return <DollarSign className="h-6 w-6 text-purple-500" />;
    default:
      return <AlertTriangle className="h-6 w-6 text-yellow-500" />;
  }
}

function getErrorColor(type: ParsedError['type']) {
  switch (type) {
    case 'rate_limit':
      return 'border-orange-500/20 bg-orange-500/5';
    case 'auth':
      return 'border-red-500/20 bg-red-500/5';
    case 'network':
      return 'border-blue-500/20 bg-blue-500/5';
    case 'quota':
      return 'border-purple-500/20 bg-purple-500/5';
    default:
      return 'border-yellow-500/20 bg-yellow-500/5';
  }
}

export function ErrorDisplay({ 
  error, 
  onRetry, 
  onDismiss, 
  operation,
  className = "" 
}: ErrorDisplayProps) {
  const parsedError = parseError(error);
  const showRetry = onRetry && parsedError.type !== 'auth' && parsedError.type !== 'validation';
  
  return (
    <Card className={`p-4 ${getErrorColor(parsedError.type)} ${className}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          {getErrorIcon(parsedError.type)}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-[var(--buddychip-white)]">
              {operation ? `${operation} failed` : 'Error'}
            </h3>
            {onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onDismiss}
                className="h-6 w-6 p-0 text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
              >
                ×
              </Button>
            )}
          </div>
          
          <p className="text-sm text-[var(--buddychip-grey-text)] mb-3">
            {parsedError.message}
          </p>
          
          {parsedError.suggestions.length > 0 && (
            <div className="mb-3">
              <p className="text-xs font-medium text-[var(--buddychip-white)] mb-1">
                What you can do:
              </p>
              <ul className="text-xs text-[var(--buddychip-grey-text)] space-y-1">
                {parsedError.suggestions.slice(0, 3).map((suggestion, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-[var(--buddychip-accent)] mr-1">•</span>
                    {suggestion}
                  </li>
                ))}
              </ul>
            </div>
          )}
          
          <div className="flex items-center space-x-2">
            {showRetry && (
              <Button
                size="sm"
                onClick={onRetry}
                className="bg-[var(--buddychip-accent)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent)]/80"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Try Again
              </Button>
            )}
            
            {parsedError.type === 'auth' && (
              <Button
                size="sm"
                onClick={() => window.location.reload()}
                variant="outline"
                className="border-[var(--buddychip-border)] text-[var(--buddychip-grey-text)]"
              >
                Sign In
              </Button>
            )}
          </div>
          
          {/* Show technical details in development */}
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-3">
              <summary className="text-xs text-[var(--buddychip-grey-text)] cursor-pointer">
                Technical Details (Dev Mode)
              </summary>
              <pre className="text-xs text-[var(--buddychip-grey-text)] mt-1 p-2 bg-[var(--buddychip-black)] rounded border border-[var(--buddychip-border)] overflow-auto">
                {JSON.stringify(error, null, 2)}
              </pre>
            </details>
          )}
        </div>
      </div>
    </Card>
  );
}

/**
 * Toast-style error display for inline errors
 */
export function ErrorToast({ 
  error, 
  onRetry, 
  onDismiss,
  className = "" 
}: Omit<ErrorDisplayProps, 'operation'>) {
  const parsedError = parseError(error);
  
  return (
    <div className={`flex items-center space-x-3 p-3 rounded-lg border ${getErrorColor(parsedError.type)} ${className}`}>
      {getErrorIcon(parsedError.type)}
      
      <div className="flex-1 min-w-0">
        <p className="text-sm text-[var(--buddychip-white)] font-medium">
          {parsedError.message}
        </p>
        {parsedError.suggestions.length > 0 && (
          <p className="text-xs text-[var(--buddychip-grey-text)] mt-1">
            {parsedError.suggestions[0]}
          </p>
        )}
      </div>
      
      <div className="flex items-center space-x-1">
        {onRetry && parsedError.type !== 'auth' && (
          <Button
            size="sm"
            variant="ghost"
            onClick={onRetry}
            className="h-6 px-2 text-xs text-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/10"
          >
            Retry
          </Button>
        )}
        {onDismiss && (
          <Button
            size="sm"
            variant="ghost"
            onClick={onDismiss}
            className="h-6 w-6 p-0 text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
          >
            ×
          </Button>
        )}
      </div>
    </div>
  );
}

/**
 * Hook for managing error state with retry logic
 */
export function useErrorHandler() {
  const handleError = (error: any, operation?: string) => {
    console.error(`Error in ${operation || 'operation'}:`, error);
    return parseError(error);
  };
  
  const createRetryHandler = (originalFunction: () => Promise<any>, maxRetries = 3) => {
    return async () => {
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          await originalFunction();
          break;
        } catch (error) {
          if (attempt === maxRetries) {
            throw error;
          }
          // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempt - 1)));
        }
      }
    };
  };
  
  return { handleError, createRetryHandler };
}