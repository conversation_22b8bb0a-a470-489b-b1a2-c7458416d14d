import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@BuddyChipPro/backend";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { But<PERSON> } from "../ui/button";
import { Checkbox } from "../ui/checkbox";
import { Label } from "../ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { 
  Brain, 
  Briefcase, 
  MessageCircle, 
  Laugh, 
  Code2, 
  Heart,
  Settings,
  Info,
  Star
} from "lucide-react";
import { toast } from "sonner";
import { ResponseStyle } from "../../types/responses";

interface StyleConfig {
  name: ResponseStyle;
  description: string;
  icon: any;
  color: string;
  characteristics: string[];
  examples: string[];
}

const STYLE_CONFIGS: Record<ResponseStyle, StyleConfig> = {
  professional: {
    name: "professional",
    description: "Formal, authoritative, and business-appropriate",
    icon: Briefcase,
    color: "bg-blue-100 text-blue-600",
    characteristics: [
      "Formal language and proper grammar",
      "Maintains professional distance",
      "Focuses on facts and expertise",
      "No emojis or casual expressions"
    ],
    examples: [
      "Thank you for bringing this to our attention. We'll investigate this matter thoroughly.",
      "I appreciate your feedback and will ensure our team reviews this recommendation."
    ]
  },
  casual: {
    name: "casual",
    description: "Friendly, approachable, and conversational",
    icon: MessageCircle,
    color: "bg-green-100 text-green-600",
    characteristics: [
      "Casual language and contractions",
      "Feels like talking to a friend",
      "Shows personality and warmth",
      "Light humor when appropriate"
    ],
    examples: [
      "Thanks for sharing this! That's a really good point you've made.",
      "Hey, I totally agree with you on this one. Great observation!"
    ]
  },
  humorous: {
    name: "humorous",
    description: "Light-hearted, witty, and entertaining",
    icon: Laugh,
    color: "bg-yellow-100 text-yellow-600",
    characteristics: [
      "Uses appropriate humor and wit",
      "Makes clever observations",
      "Includes relevant references",
      "Keeps things light and fun"
    ],
    examples: [
      "Ha! You've clearly been reading my mind - or my code comments 😄",
      "Plot twist: you're absolutely right! Thanks for keeping me on my toes."
    ]
  },
  technical: {
    name: "technical",
    description: "Detailed, precise, and technically accurate",
    icon: Code2,
    color: "bg-purple-100 text-purple-600",
    characteristics: [
      "Uses technical terminology",
      "Provides detailed explanations",
      "References specific technologies",
      "Focuses on accuracy and precision"
    ],
    examples: [
      "The implementation you're describing follows the Observer pattern, which is optimal for this use case.",
      "This aligns with the CAP theorem - you're trading consistency for availability."
    ]
  },
  supportive: {
    name: "supportive",
    description: "Encouraging, empathetic, and helpful",
    icon: Heart,
    color: "bg-pink-100 text-pink-600",
    characteristics: [
      "Shows empathy and understanding",
      "Offers encouragement and support",
      "Provides helpful suggestions",
      "Acknowledges challenges"
    ],
    examples: [
      "I can understand how frustrating that must be. Here's what helped me in a similar situation...",
      "You're definitely not alone in feeling this way. Many developers face this challenge."
    ]
  },
  engaging: {
    name: "engaging",
    description: "Interactive, thought-provoking, and conversational",
    icon: Star,
    color: "bg-orange-100 text-orange-600",
    characteristics: [
      "Asks questions to spark discussion",
      "Encourages interaction and engagement",
      "Uses thought-provoking language",
      "Invites others to share perspectives"
    ],
    examples: [
      "This is fascinating! What's your take on how this might evolve in the next few years?",
      "Great point! I'm curious - have you tried any alternatives to this approach?"
    ]
  }
};

interface ResponseStyleSelectorProps {
  selectedStyles: ResponseStyle[];
  onStylesChange: (styles: ResponseStyle[]) => void;
  defaultStyle?: ResponseStyle;
  onDefaultStyleChange?: (style: ResponseStyle) => void;
  multiple?: boolean;
  showPreferences?: boolean;
  userId?: string;
}

export function ResponseStyleSelector({
  selectedStyles,
  onStylesChange,
  defaultStyle = "professional",
  onDefaultStyleChange,
  multiple = true,
  showPreferences = false,
  userId
}: ResponseStyleSelectorProps) {
  const [showDetails, setShowDetails] = useState<ResponseStyle | null>(null);
  
  // Get response analytics to show performance
  const styleAnalytics = useQuery(
    api.responseQueries.getStylePerformanceAnalytics,
    userId ? { userId: userId as any } : "skip"
  );

  const updatePreferences = useMutation(api.responseMutations.setUserPreferredStyle);

  const handleStyleToggle = (style: ResponseStyle) => {
    if (multiple) {
      const isSelected = selectedStyles.includes(style);
      if (isSelected) {
        onStylesChange(selectedStyles.filter(s => s !== style));
      } else {
        onStylesChange([...selectedStyles, style]);
      }
    } else {
      onStylesChange([style]);
    }
  };

  const handleDefaultStyleChange = async (style: ResponseStyle) => {
    if (onDefaultStyleChange) {
      onDefaultStyleChange(style);
    }
    
    if (userId) {
      try {
        await updatePreferences({ 
          userId: userId as any, 
          preferredStyle: style 
        });
        toast.success("Default style updated");
      } catch (error) {
        toast.error("Failed to update default style");
      }
    }
  };

  const getStylePerformance = (style: ResponseStyle) => {
    if (!styleAnalytics) return null;
    return styleAnalytics.find((s: any) => s.style === style);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2 text-[var(--buddychip-white)]">
            <Brain className="h-5 w-5 text-[var(--buddychip-accent)]" />
            Response Styles
          </h3>
          <p className="text-sm text-[var(--buddychip-grey-text)]">
            Choose the tone and style for AI-generated responses
          </p>
        </div>
        {showPreferences && (
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Preferences
          </Button>
        )}
      </div>

      {/* Default Style Selector */}
      {onDefaultStyleChange && (
        <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2 text-[var(--buddychip-white)]">
              <Star className="h-4 w-4 text-[var(--buddychip-accent)]" />
              Default Style
            </CardTitle>
            <CardDescription className="text-[var(--buddychip-grey-text)]">
              This style will be used automatically for new responses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Select value={defaultStyle} onValueChange={handleDefaultStyleChange}>
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.values(STYLE_CONFIGS).map((config) => {
                  const Icon = config.icon;
                  return (
                    <SelectItem key={config.name} value={config.name}>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        <span className="capitalize">{config.name}</span>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>
      )}

      {/* Style Selection Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Object.values(STYLE_CONFIGS).map((config) => {
          const Icon = config.icon;
          const isSelected = selectedStyles.includes(config.name);
          const performance = getStylePerformance(config.name);
          
          return (
            <Card 
              key={config.name}
              className={`cursor-pointer transition-all hover:shadow-md bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] ${
                isSelected ? 'ring-2 ring-[var(--buddychip-accent)] bg-[var(--buddychip-accent)]/10' : ''
              }`}
              onClick={() => handleStyleToggle(config.name)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    {multiple && (
                      <Checkbox 
                        checked={isSelected}
                        onChange={() => {}} // Handled by card click
                      />
                    )}
                    <div className="p-2 rounded-lg bg-[var(--buddychip-grey-stroke)]">
                      <Icon className="h-5 w-5 text-[var(--buddychip-accent)]" />
                    </div>
                    <div>
                      <CardTitle className="text-base capitalize text-[var(--buddychip-white)]">
                        {config.name}
                      </CardTitle>
                      {performance && (
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="secondary" className="text-xs bg-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] border-[var(--buddychip-grey-stroke)]">
                            {performance.totalResponses} responses
                          </Badge>
                          <Badge variant="outline" className="text-xs border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)]">
                            {Math.round(performance.averageQuality * 100)}% quality
                          </Badge>
                        </div>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowDetails(showDetails === config.name ? null : config.name);
                    }}
                  >
                    <Info className="h-4 w-4" />
                  </Button>
                </div>
                <CardDescription className="mt-2 text-[var(--buddychip-grey-text)]">
                  {config.description}
                </CardDescription>
              </CardHeader>

              {/* Performance Metrics */}
              {performance && (
                <CardContent className="pt-0">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-[var(--buddychip-grey-text)]">Avg Engagement</div>
                      <div className="font-medium text-[var(--buddychip-white)]">
                        {Math.round(performance.averageEngagement)}
                      </div>
                    </div>
                    <div>
                      <div className="text-[var(--buddychip-grey-text)]">Approval Rate</div>
                      <div className="font-medium text-[var(--buddychip-white)]">
                        {Math.round(performance.approvalRate * 100)}%
                      </div>
                    </div>
                  </div>
                </CardContent>
              )}

              {/* Detailed Information */}
              {showDetails === config.name && (
                <CardContent className="pt-0 border-t">
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium text-[var(--buddychip-white)]">Characteristics</Label>
                      <ul className="mt-1 space-y-1">
                        {config.characteristics.map((char, index) => (
                          <li key={index} className="text-xs text-[var(--buddychip-grey-text)] flex items-start gap-2">
                            <span className="w-1 h-1 bg-[var(--buddychip-grey-text)] rounded-full mt-2 flex-shrink-0" />
                            {char}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-[var(--buddychip-white)]">Example Responses</Label>
                      <div className="mt-1 space-y-2">
                        {config.examples.map((example, index) => (
                          <div key={index} className="text-xs text-[var(--buddychip-grey-text)] p-2 bg-[var(--buddychip-black)] border border-[var(--buddychip-grey-stroke)] rounded border-l-2 border-l-[var(--buddychip-accent)]">
                            "{example}"
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>

      {/* Selection Summary */}
      {selectedStyles.length > 0 && (
        <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-[var(--buddychip-white)]">Selected styles:</span>
                <div className="flex gap-1">
                  {selectedStyles.map((style) => (
                    <Badge key={style} variant="secondary" className="capitalize bg-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] border-[var(--buddychip-grey-stroke)]">
                      {style}
                    </Badge>
                  ))}
                </div>
              </div>
              <div className="text-sm text-[var(--buddychip-grey-text)]">
                {multiple 
                  ? `${selectedStyles.length} style${selectedStyles.length !== 1 ? 's' : ''} will generate ${selectedStyles.length} response${selectedStyles.length !== 1 ? 's' : ''}`
                  : `1 response will be generated`
                }
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}