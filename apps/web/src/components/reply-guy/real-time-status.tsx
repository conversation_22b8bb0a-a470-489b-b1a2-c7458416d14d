import { useState, useEffect } from "react";
import { useQuery } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Progress } from "../ui/progress";
import { DEBUG_CONFIG, DebugWrapper } from "../../lib/debug-config";
import { 
  Activity, 
  Clock, 
  Zap, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Database,
  Cpu,
  Wifi,
  BarChart3 
} from "lucide-react";
import { cn } from "../../lib/utils";

interface RealTimeStatusProps {
  isRefreshing?: boolean;
  lastRefreshTime?: number;
  refreshMetrics?: any;
}

export function RealTimeStatus({ 
  isRefreshing = false, 
  lastRefreshTime, 
  refreshMetrics 
}: RealTimeStatusProps) {
  const [systemHealth, setSystemHealth] = useState<'excellent' | 'good' | 'warning' | 'error'>('good');
  const [liveMetrics, setLiveMetrics] = useState({
    processingSpeed: 0,
    cacheHitRate: 0,
    apiResponseTime: 0,
    queueDepth: 0,
  });

  // Get real-time performance metrics
  const processingMetrics = useQuery(api.ai.ensembleOrchestrator.getProcessingMetrics, {
    timeRange: "24h",
  });

  // Get cache statistics
  const cacheStats = useQuery(api.lib.mentionCache.getCacheStats, {});

  // Get trending analytics
  const trendingAnalytics = useQuery(api.ai.viralDetection.getTrendingAnalytics, {
    timeRange: "6h",
  });

  // Update live metrics when new data arrives
  useEffect(() => {
    if (refreshMetrics) {
      setLiveMetrics({
        processingSpeed: refreshMetrics.totalProcessingTime || 0,
        cacheHitRate: refreshMetrics.cacheHits && refreshMetrics.apiCalls ? 
          (refreshMetrics.cacheHits / (refreshMetrics.cacheHits + refreshMetrics.apiCalls)) * 100 : 0,
        apiResponseTime: refreshMetrics.avgApiResponseTime || 0,
        queueDepth: refreshMetrics.pendingProcessing || 0,
      });
    }
  }, [refreshMetrics]);

  // Determine system health based on metrics
  useEffect(() => {
    if (!processingMetrics || !cacheStats) return;

    let healthScore = 100;
    
    // Processing rate impact
    if (processingMetrics.processingRate < 0.8) healthScore -= 20;
    else if (processingMetrics.processingRate < 0.9) healthScore -= 10;
    
    // Cache hit rate impact
    if (cacheStats.hitRate < 0.5) healthScore -= 15;
    else if (cacheStats.hitRate < 0.7) healthScore -= 10;
    
    // Response time impact
    if (liveMetrics.processingSpeed > 5000) healthScore -= 15;
    else if (liveMetrics.processingSpeed > 2000) healthScore -= 10;
    
    // Queue depth impact
    if (liveMetrics.queueDepth > 50) healthScore -= 10;
    
    if (healthScore >= 90) setSystemHealth('excellent');
    else if (healthScore >= 75) setSystemHealth('good');
    else if (healthScore >= 60) setSystemHealth('warning');
    else setSystemHealth('error');
  }, [processingMetrics, cacheStats, liveMetrics]);

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent': return 'text-green-400 border-green-500/50';
      case 'good': return 'text-blue-400 border-blue-500/50';
      case 'warning': return 'text-yellow-400 border-yellow-500/50';
      case 'error': return 'text-red-400 border-red-500/50';
      default: return 'text-gray-400 border-gray-500/50';
    }
  };

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'excellent': return <CheckCircle className="h-4 w-4" />;
      case 'good': return <Activity className="h-4 w-4" />;
      case 'warning': return <AlertTriangle className="h-4 w-4" />;
      case 'error': return <AlertTriangle className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-4">
      {/* System Health Overview - Debug Mode Only */}
      <DebugWrapper category="performance">
        <Card className={cn(
          "bg-[#000000]/70 border backdrop-blur-sm transition-all duration-300",
          getHealthColor(systemHealth)
        )}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA] flex items-center gap-2">
              {getHealthIcon(systemHealth)}
              System Health
            </CardTitle>
            <Badge 
              variant="outline" 
              className={cn(
                "text-xs",
                systemHealth === 'excellent' && "border-green-500/50 text-green-400",
                systemHealth === 'good' && "border-blue-500/50 text-blue-400",
                systemHealth === 'warning' && "border-yellow-500/50 text-yellow-400",
                systemHealth === 'error' && "border-red-500/50 text-red-400"
              )}
            >
              {systemHealth.toUpperCase()}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
              <div className="text-center">
                <div className="text-[#F5F7FA] font-medium">
                  {processingMetrics ? `${Math.round(processingMetrics.processingRate * 100)}%` : '---'}
                </div>
                <div className="text-[#6E7A8C]">Processing Rate</div>
              </div>
              <div className="text-center">
                <div className="text-[#F5F7FA] font-medium">
                  {cacheStats ? `${Math.round(cacheStats.hitRate * 100)}%` : '---'}
                </div>
                <div className="text-[#6E7A8C]">Cache Hit Rate</div>
              </div>
              <div className="text-center">
                <div className="text-[#F5F7FA] font-medium">
                  {formatDuration(liveMetrics.processingSpeed)}
                </div>
                <div className="text-[#6E7A8C]">Avg Response Time</div>
              </div>
              <div className="text-center">
                <div className="text-[#F5F7FA] font-medium">
                  {liveMetrics.queueDepth}
                </div>
                <div className="text-[#6E7A8C]">Queue Depth</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </DebugWrapper>

      {/* Live Processing Status */}
      {isRefreshing && (
        <Card className="bg-[#000000]/70 border-[#316FE3]/50 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA] flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse" />
              Live Processing
            </CardTitle>
            <Badge variant="outline" className="border-blue-500/50 text-blue-400 text-xs">
              ACTIVE
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between text-xs">
                <span className="text-[#6E7A8C]">Processing mentions...</span>
                <span className="text-[#F5F7FA]">
                  {refreshMetrics?.mentionsFound || 0} found
                </span>
              </div>
              <Progress 
                value={refreshMetrics?.accountsProcessed && refreshMetrics?.totalAccounts ?
                  (refreshMetrics.accountsProcessed / refreshMetrics.totalAccounts) * 100 : 30
                } 
                className="h-2 bg-[#202631]"
              />
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="text-center">
                  <div className="text-[#F5F7FA] font-medium">
                    {refreshMetrics?.apiCalls || 0}
                  </div>
                  <div className="text-[#6E7A8C]">API Calls</div>
                </div>
                <div className="text-center">
                  <div className="text-[#F5F7FA] font-medium">
                    {refreshMetrics?.cacheHits || 0}
                  </div>
                  <div className="text-[#6E7A8C]">Cache Hits</div>
                </div>
                <div className="text-center">
                  <div className="text-[#F5F7FA] font-medium">
                    {refreshMetrics?.duplicatesSkipped || 0}
                  </div>
                  <div className="text-[#6E7A8C]">Duplicates</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Metrics Grid - Debug Mode Only */}
      <DebugWrapper category="performance">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Database Performance */}
          <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:border-purple-500/50 transition-colors">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-lg font-bold text-[#F5F7FA]">
                    {cacheStats ? formatNumber(cacheStats.totalEntries) : '---'}
                  </div>
                  <div className="text-xs text-[#6E7A8C] flex items-center gap-1">
                    <Database className="h-3 w-3" />
                    Cache Entries
                  </div>
                </div>
                <div className="text-purple-400">
                  <BarChart3 className="h-5 w-5" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Processing Speed */}
          <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:border-green-500/50 transition-colors">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-lg font-bold text-[#F5F7FA]">
                    {processingMetrics ? `${Math.round(processingMetrics.avgProcessingTimeMs || 0)}ms` : '---'}
                  </div>
                  <div className="text-xs text-[#6E7A8C] flex items-center gap-1">
                    <Cpu className="h-3 w-3" />
                    Avg Processing
                  </div>
                </div>
                <div className="text-green-400">
                  <Zap className="h-5 w-5" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Viral Detection */}
          <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:border-orange-500/50 transition-colors">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-lg font-bold text-[#F5F7FA]">
                    {trendingAnalytics ? trendingAnalytics.viralMentions : '---'}
                  </div>
                  <div className="text-xs text-[#6E7A8C] flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    Viral Detected
                  </div>
                </div>
                <div className="text-orange-400">
                  <Activity className="h-5 w-5" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* System Uptime */}
          <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:border-blue-500/50 transition-colors">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-lg font-bold text-[#F5F7FA]">
                    {lastRefreshTime ? formatDuration(Date.now() - lastRefreshTime) : '---'}
                  </div>
                  <div className="text-xs text-[#6E7A8C] flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    Last Refresh
                  </div>
                </div>
                <div className="text-blue-400">
                  <Wifi className="h-5 w-5" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DebugWrapper>

      {/* Trending Topics */}
      {trendingAnalytics && trendingAnalytics.topTopics && trendingAnalytics.topTopics.length > 0 && (
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-sm font-medium text-[#F5F7FA] flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-orange-400" />
              Trending Topics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {trendingAnalytics.topTopics.slice(0, 8).map((topic: any, index: number) => (
                <Badge 
                  key={index} 
                  variant="outline" 
                  className="border-[#316FE3]/30 text-[#316FE3] text-xs"
                >
                  {topic.topic} ({topic.count})
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
