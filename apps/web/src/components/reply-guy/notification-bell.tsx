import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@BuddyChipPro/backend";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { 
  Bell, 
  X, 
  Eye, 
  Clock, 
  Zap, 
  MessageSquare,
  TrendingUp,
  Users,
  ExternalLink,
  CheckCircle,
  Sparkles
} from "lucide-react";
import { cn } from "../../lib/utils";
import { toast } from "sonner";
import { useNavigate } from "@tanstack/react-router";

interface NotificationBellProps {
  className?: string;
}

export function NotificationBell({ className }: NotificationBellProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [hasNewNotifications, setHasNewNotifications] = useState(false);
  const [lastCheckTime, setLastCheckTime] = useState(Date.now());
  const navigate = useNavigate();

  // Get unread mentions
  const unreadMentions = useQuery(api.mentions.mentionQueries.getUnreadMentions, {
    limit: 10,
  });

  // Get mention stats
  const mentionStats = useQuery(api.mentions.mentionQueries.getMentionStats, {});

  // Mutation to mark all mentions as read
  const markAllAsRead = useMutation(api.mentions.mentionMutations.markAllMentionsAsRead);

  // Check for new notifications
  useEffect(() => {
    if (unreadMentions?.data && unreadMentions.data.length > 0) {
      const hasNew = unreadMentions.data.some((mention: any) => 
        mention.discoveredAt > lastCheckTime
      );
      setHasNewNotifications(hasNew);
    }
  }, [unreadMentions, lastCheckTime]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setLastCheckTime(Date.now());
    }, 30000);
    return () => clearInterval(interval);
  }, []);

  const handleMarkAllAsRead = async () => {
    try {
      const result = await markAllAsRead({});
      setHasNewNotifications(false);
      setLastCheckTime(Date.now());
      toast.success(`Marked ${result.updated} mentions as read`);
    } catch (error) {
      console.error("Failed to mark all mentions as read:", error);
      toast.error("Failed to mark mentions as read");
    }
  };

  const formatTimeAgo = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return <Zap className="h-3 w-3 text-red-400" />;
      case 'medium': return <TrendingUp className="h-3 w-3 text-yellow-400" />;
      case 'low': return <MessageSquare className="h-3 w-3 text-gray-400" />;
      default: return <MessageSquare className="h-3 w-3 text-gray-400" />;
    }
  };

  const unreadCount = mentionStats?.unread || (unreadMentions?.data?.length || 0);

  return (
    <div className={cn("relative", className)}>
      {/* Notification Bell Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "relative border-0 hover:bg-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] transition-all duration-200",
          isOpen && "bg-[var(--buddychip-grey-stroke)]",
          hasNewNotifications && "animate-[gentle-bounce_2s_ease-in-out_infinite]"
        )}
      >
        <Bell className={cn(
          "h-4 w-4 transition-colors text-[var(--buddychip-white)]",
          hasNewNotifications && "text-blue-400"
        )} />
        {unreadCount > 0 && (
          <Badge 
            className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 bg-red-500 text-white text-xs font-bold animate-[gentle-glow_2s_ease-in-out_infinite]"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>

      {/* Notification Dropdown */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Notification Panel */}
          <div className="absolute right-0 top-full mt-2 w-80 max-h-96 overflow-hidden bg-[var(--buddychip-black)]/95 border border-[var(--buddychip-grey-stroke)] rounded-lg shadow-xl backdrop-blur-sm z-50 animate-in slide-in-from-top-2 duration-200">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm text-[var(--buddychip-white)]">
                  Notifications
                </CardTitle>
                <div className="flex items-center gap-2">
                  {unreadCount > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleMarkAllAsRead}
                      className="text-xs text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                    >
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Mark all read
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] p-1"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              {unreadCount > 0 && (
                <CardDescription className="text-xs text-[var(--buddychip-grey-text)]">
                  {unreadCount} new mentions awaiting your attention
                </CardDescription>
              )}
            </CardHeader>

            <CardContent className="p-0">
              {!unreadMentions?.data || unreadMentions.data.length === 0 ? (
                <div className="p-6 text-center">
                  <Bell className="h-8 w-8 text-[var(--buddychip-grey-text)] mx-auto mb-2" />
                  <p className="text-sm text-[var(--buddychip-grey-text)]">No new notifications</p>
                  <p className="text-xs text-[var(--buddychip-grey-text)] mt-1">
                    {unreadMentions === undefined ? 'Loading...' : "You're all caught up!"}
                  </p>
                </div>
              ) : (
                <div className="max-h-64 overflow-y-auto">
                  {unreadMentions.data.map((mention: any) => (
                    <div
                      key={mention._id}
                      className="p-3 border-b border-[var(--buddychip-grey-stroke)] hover:bg-[var(--buddychip-grey-stroke)]/50 transition-colors cursor-pointer group"
                      onClick={() => {
                        setIsOpen(false);
                        // Navigate to mentions page with specific mention hash
                        navigate({ to: '/mentions', hash: mention._id });
                      }}
                    >
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                          <span className="text-xs font-semibold text-white">
                            {mention.mentionAuthor.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium text-[var(--buddychip-white)] truncate">
                              {mention.mentionAuthor}
                            </span>
                            <span className="text-xs text-[var(--buddychip-grey-text)]">
                              @{mention.mentionAuthorHandle}
                            </span>
                            {getPriorityIcon(mention.priority)}
                          </div>
                          
                          <p className="text-xs text-[var(--buddychip-grey-text)] line-clamp-2 mb-2">
                            {mention.mentionContent}
                          </p>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3 text-xs text-[var(--buddychip-grey-text)]">
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>{formatTimeAgo(mention.createdAt)}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Eye className="h-3 w-3" />
                                <span>{mention.engagement.likes + mention.engagement.retweets}</span>
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <ExternalLink className="h-3 w-3 text-[var(--buddychip-grey-text)]" />
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      {/* New indicator */}
                      {mention.discoveredAt > lastCheckTime - 300000 && (
                        <div className="absolute left-0 top-0 w-1 h-full bg-blue-500" />
                      )}
                    </div>
                  ))}
                </div>
              )}
              
              {/* Footer */}
              <div className="p-3 border-t border-[var(--buddychip-grey-stroke)] bg-[var(--buddychip-black)]/40">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                  onClick={() => {
                    setIsOpen(false);
                    navigate({ to: '/mentions' });
                  }}
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  View All Mentions
                </Button>
              </div>
            </CardContent>
          </div>
        </>
      )}
    </div>
  );
}