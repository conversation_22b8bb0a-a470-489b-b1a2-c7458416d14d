import { useQuery } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { useAuth } from "@/components/auth/use-auth";
import { TwitterAccount, DashboardStats, ResponseStats, TweetData } from "@/types/responses";
import { AccountFilter } from "@/components/dashboard/account-filter";
import { AddAccountModal } from "@/components/dashboard/add-account-modal";
import { TweetUrlInput } from "@/components/tweet-assistant/tweet-url-input";
import { PageHeader } from "@/components/ui/page-header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useState, useEffect } from "react";
import { useMutation, useAction } from "convex/react";
import { 
  Copy, 
  Heart, 
  Repeat, 
  MessageCircle,
  ExternalLink,
  User,
  Calendar,
  TrendingUp,
  Sparkles,
  Trash2,
  Download,
  Search,
  Plus,
  Bell,
  RefreshCw,
  X,
  Check,
  Edit,
  Share2,
  Save,
  XCircle,
  ChevronDown,
  Eye
} from "lucide-react";
import { toast } from "sonner";

function DashboardPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const [selectedAccountIds, setSelectedAccountIds] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<'recent' | 'engagement' | 'score'>('recent');
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'generated' | 'posted'>('all');
  const [showAddAccountModal, setShowAddAccountModal] = useState(false);
  const [isCheckingMentions, setIsCheckingMentions] = useState(false);
  const [generatingResponses, setGeneratingResponses] = useState<Set<string>>(new Set());
  const [generatedResponse, setGeneratedResponse] = useState<{
    mention: any;
    response: string;
    style: string;
    characterCount: number;
    responseId?: string;
  } | null>(null);
  const [showResponseModal, setShowResponseModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedResponse, setEditedResponse] = useState("");
  const [isPosting, setIsPosting] = useState(false);
  const [showPostOptions, setShowPostOptions] = useState(false);

  // Queries
  const twitterAccountsQuery = useQuery(api.auth.walletDetection.getUserTwitterAccounts);
  const twitterAccounts: TwitterAccount[] = Array.isArray(twitterAccountsQuery) ? twitterAccountsQuery : [];
  const dashboardStats = useQuery(api.responseGeneration.getDashboardStats) as DashboardStats || null;
  const responseStats = useQuery(api.responseQueries.getUserResponseStats) as ResponseStats || null;
  
  // Mutations and Actions
  const deleteResponse = useMutation(api.responseMutations.deleteResponse);
  const markAsPosted = useMutation(api.responseMutations.markResponseAsPosted);
  const checkMentions = useAction(api.mentions.mentionMutations.quickMentionRefresh);
  const generateResponse = useAction(api.responseGeneration.generateSingleResponse);
  const storeResponse = useMutation(api.responseMutations.storeResponse);
  const updateResponseStatus = useMutation(api.responseMutations.updateResponseStatus);

  // Get latest mentions that need answers
  const pendingMentions = useQuery(api.mentions.mentionQueries.getRecentMentions, {
    limit: 5,
    timeRange: "7d"
  });

  // Filter mentions that need responses (high priority or unprocessed)
  const mentionsNeedingResponse = (pendingMentions?.data || []).filter((mention: any) => {
    // Show mentions that are high priority or haven't been processed yet
    return mention.priority === 'high' || !mention.isProcessed || 
           (mention.aiAnalysisResult?.shouldRespond && !mention.responses?.length);
  });

  const sortedMentions = [...mentionsNeedingResponse].sort((a, b) => {
    switch (sortBy) {
      case 'recent':
        return b.discoveredAt - a.discoveredAt;
      case 'engagement':
        const aTotal = a.engagement.likes + a.engagement.retweets + a.engagement.replies;
        const bTotal = b.engagement.likes + b.engagement.retweets + b.engagement.replies;
        return bTotal - aTotal;
      case 'score':
        return (b.aiAnalysisResult?.viralScore || 0) - (a.aiAnalysisResult?.viralScore || 0);
      default:
        return 0;
    }
  }).slice(0, 5); // Show only latest 5

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        if (showPostOptions) {
          setShowPostOptions(false);
        } else if (showResponseModal) {
          handleCloseModal();
        }
      }
    };

    const handleClickOutside = (e: MouseEvent) => {
      if (showPostOptions) {
        setShowPostOptions(false);
      }
    };

    if (showResponseModal) {
      document.addEventListener('keydown', handleEscape);
      document.addEventListener('click', handleClickOutside);
      return () => {
        document.removeEventListener('keydown', handleEscape);
        document.removeEventListener('click', handleClickOutside);
      };
    }
  }, [showResponseModal, showPostOptions]);

  const handleCloseModal = () => {
    setShowResponseModal(false);
    setIsEditing(false);
    setEditedResponse("");
    setGeneratedResponse(null);
    setShowPostOptions(false);
  };

  const handleCopyResponse = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      toast.success("Response copied to clipboard!");
    } catch (error) {
      console.error('Failed to copy:', error);
      toast.error("Failed to copy response");
    }
  };

  const handleEditResponse = () => {
    setIsEditing(true);
  };

  const handleSaveEdit = async () => {
    if (!generatedResponse?.responseId) return;
    
    try {
      // Update the response content (you might need to create an updateResponse mutation)
      setGeneratedResponse({
        ...generatedResponse,
        response: editedResponse,
        characterCount: editedResponse.length,
      });
      setIsEditing(false);
      toast.success("Response updated!");
    } catch (error) {
      console.error('Failed to update response:', error);
      toast.error("Failed to update response");
    }
  };

  const handleCancelEdit = () => {
    setEditedResponse(generatedResponse?.response || "");
    setIsEditing(false);
  };

  const handlePostAsReply = async () => {
    if (!generatedResponse?.responseId) return;
    
    setIsPosting(true);
    setShowPostOptions(false);
    
    try {
      // Get the response content (edited or original)
      const responseContent = isEditing ? editedResponse : generatedResponse.response;
      
      // Check if we have the original mention URL
      if (!generatedResponse.mention.url) {
        toast.error("Original tweet URL not available");
        return;
      }

      // Extract tweet ID from the original URL (same logic as mentions page)
      const tweetIdMatch = generatedResponse.mention.url.match(/status\/(\d+)/);
      if (!tweetIdMatch) {
        toast.error("Could not extract tweet ID from URL");
        return;
      }
      
      const tweetId = tweetIdMatch[1];
      
      // Create Twitter reply URL with pre-filled text (same format as mentions page)
      const tweetText = encodeURIComponent(responseContent);
      const twitterReplyUrl = `https://twitter.com/intent/tweet?in_reply_to=${tweetId}&text=${tweetText}`;
      
      // Open Twitter compose in a new tab
      window.open(twitterReplyUrl, '_blank');
      
      // Update response status to "approved"
      await updateResponseStatus({
        responseId: generatedResponse.responseId,
        status: "approved",
      });
      
      toast.success("Opening Twitter to reply!");
      handleCloseModal();
      
    } catch (error) {
      console.error('Failed to open Twitter compose:', error);
      toast.error("Failed to open Twitter compose");
    } finally {
      setIsPosting(false);
    }
  };

  const handlePostAsNewTweet = async () => {
    if (!generatedResponse?.responseId) return;
    
    setIsPosting(true);
    setShowPostOptions(false);
    
    try {
      // Get the response content (edited or original)
      const responseContent = isEditing ? editedResponse : generatedResponse.response;
      
      // Create Twitter compose URL with the response pre-filled as a new tweet
      const tweetText = encodeURIComponent(responseContent);
      const twitterComposeUrl = `https://twitter.com/intent/tweet?text=${tweetText}`;
      
      // Open Twitter compose in a new tab
      window.open(twitterComposeUrl, '_blank');
      
      // Update response status to "approved"
      await updateResponseStatus({
        responseId: generatedResponse.responseId,
        status: "approved",
      });
      
      toast.success("Opening Twitter to post as new tweet!");
      handleCloseModal();
      
    } catch (error) {
      console.error('Failed to open Twitter compose:', error);
      toast.error("Failed to open Twitter compose");
    } finally {
      setIsPosting(false);
    }
  };

  const handleCopyToClipboard = async () => {
    if (!generatedResponse) return;
    
    const responseContent = isEditing ? editedResponse : generatedResponse.response;
    
    try {
      await navigator.clipboard.writeText(responseContent);
      toast.success("Response copied! You can paste it on Twitter manually.");
      setShowPostOptions(false);
    } catch (error) {
      console.error('Failed to copy:', error);
      toast.error("Failed to copy response");
    }
  };

  const handleDeleteResponse = async (responseId: string) => {
    try {
      await deleteResponse({ responseId: responseId as any });
    } catch (error) {
      console.error('Failed to delete response:', error);
    }
  };

  const handleMarkAsPosted = async (responseId: string) => {
    try {
      await markAsPosted({ responseId: responseId as any });
    } catch (error) {
      console.error('Failed to mark as posted:', error);
    }
  };

  // Account filter handlers
  const handleAccountToggle = (accountId: string) => {
    setSelectedAccountIds(prev => 
      prev.includes(accountId) 
        ? prev.filter(id => id !== accountId)
        : [...prev, accountId]
    );
  };

  const handleSelectAllAccounts = () => {
    setSelectedAccountIds(twitterAccounts.map(account => account._id));
  };

  const handleClearAllAccounts = () => {
    setSelectedAccountIds([]);
  };

  const handleAccountAdded = () => {
    // Refresh the accounts list by refetching the query
    // The useQuery will automatically update when new data is available
  };

  const handleCheckMentions = async () => {
    console.log("🔥 Refresh button clicked!");
    toast.info("Refresh button clicked - checking mentions...");
    
    if (twitterAccounts.length === 0) {
      toast.error("Please add Twitter accounts to monitor for mentions");
      return;
    }

    setIsCheckingMentions(true);
    
    try {
      const accountHandles = twitterAccounts
        .filter(account => account.isMonitoringEnabled !== false)
        .map(account => account.handle);

      if (accountHandles.length === 0) {
        toast.error("No accounts enabled for monitoring. Please check your account settings.");
        return;
      }

      console.log("🔥 About to call checkMentions action...");
      const result = await checkMentions({
        // No parameters needed for quickMentionRefresh
      });

      console.log("🔥 checkMentions result:", result);
      const totalMentions = result.newMentions || 0;
      
      if (totalMentions > 0) {
        toast.success(`Found ${totalMentions} new mentions! Check the Mentions page to see them.`);
      } else {
        toast.info("No new mentions found in the last 24 hours.");
      }
    } catch (error) {
      console.error("Failed to check mentions:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to check for mentions";
      toast.error(errorMessage);
    } finally {
      setIsCheckingMentions(false);
    }
  };

  const handleGenerateResponse = async (mention: any) => {
    const mentionId = mention._id;
    
    setGeneratingResponses(prev => new Set([...prev, mentionId]));
    
    try {
      toast.info("Generating response...");
      
      const response = await generateResponse({
        content: mention.content || mention.mentionContent,
        responseType: "mention",
        style: "professional",
        authorInfo: {
          handle: mention.mentionAuthorHandle || mention.authorHandle,
          displayName: mention.mentionAuthor || mention.authorName,
          isVerified: mention.mentionAuthorVerified || false,
          followerCount: mention.mentionAuthorFollowers || 0,
        },
        maxLength: 280,
      });

      // Store the generated response
      const storedResponse = await storeResponse({
        targetType: "mention",
        targetId: mentionId,
        content: response.content,
        style: response.style,
        confidence: 0.8,
        generationModel: "AI Assistant",
        estimatedEngagement: {
          likes: Math.floor(Math.random() * 20) + 5,
          retweets: Math.floor(Math.random() * 10) + 2,
          replies: Math.floor(Math.random() * 8) + 1,
        },
      });

      // Show the generated response to the user
      setGeneratedResponse({
        mention,
        response: response.content,
        style: response.style,
        characterCount: response.content.length,
        responseId: storedResponse._id,
      });
      setEditedResponse(response.content);
      setIsEditing(false);
      setShowResponseModal(true);
      
      toast.success("Response generated successfully!");
      
    } catch (error) {
      console.error("Failed to generate response:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to generate response";
      toast.error(errorMessage);
    } finally {
      setGeneratingResponses(prev => {
        const updated = new Set(prev);
        updated.delete(mentionId);
        return updated;
      });
    }
  };

  const formatTimeAgo = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    return 'Just now';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'generated': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'posted': return 'bg-green-500/20 text-green-400 border-green-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[var(--buddychip-light-bg)] via-slate-900 to-[var(--buddychip-black)] text-[var(--buddychip-white)] flex items-center justify-center">
        <div>Loading...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[var(--buddychip-light-bg)] via-slate-900 to-[var(--buddychip-black)] text-[var(--buddychip-white)] flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Please sign in to access the dashboard</h1>
          <p className="text-[var(--buddychip-grey-text)]">You need to authenticate to view your data.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--buddychip-light-bg)] via-slate-900 to-[var(--buddychip-black)] text-[var(--buddychip-white)]">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <PageHeader
          title="Dashboard"
          subtitle="Monitor your AI-powered social media engagement"
          actions={
            <>
              {/* Add Accounts Button */}
              <Button
                onClick={() => setShowAddAccountModal(true)}
                className="bg-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/80 text-[var(--buddychip-white)]"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Accounts
              </Button>

              {/* Monitor Accounts Button */}
              <Button
                variant="ghost"
                onClick={() => {
                  // Navigate to mentions settings tab for monitoring configuration
                  window.location.href = "/mentions#settings";
                }}
                className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20 hover:text-[var(--buddychip-white)]"
              >
                <Eye className="h-4 w-4 mr-2" />
                Monitor Accounts
              </Button>

              {/* Check Mentions Button */}
              <Button
                onClick={() => window.location.href = "/mentions"}
                variant="ghost"
                className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20 hover:text-[var(--buddychip-white)]"
              >
                <Bell className="h-4 w-4 mr-2" />
                Check Mentions
              </Button>
            </>
          }
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-[var(--buddychip-light-bg)]/60 border-[var(--buddychip-grey-stroke)] backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-[var(--buddychip-grey-text)]">
                Total Responses
              </CardTitle>
              <MessageCircle className="h-4 w-4 text-[var(--buddychip-grey-text)]" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-[var(--buddychip-white)]">
                {dashboardStats?.totalResponses || 0}
              </div>
              <p className="text-xs text-[var(--buddychip-grey-text)]">
                Total generated responses
              </p>
            </CardContent>
          </Card>

          <Card className="bg-[var(--buddychip-light-bg)]/60 border-[var(--buddychip-grey-stroke)] backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-[var(--buddychip-grey-text)]">
                Avg Engagement
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-[var(--buddychip-grey-text)]" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-[var(--buddychip-white)]">
                {dashboardStats?.stats?.engagement?.averageEngagementRate?.toFixed(1) || '0.0'}
              </div>
              <p className="text-xs text-[var(--buddychip-grey-text)]">
                Average engagement rate
              </p>
            </CardContent>
          </Card>

          <Card className="bg-[var(--buddychip-light-bg)]/60 border-[var(--buddychip-grey-stroke)] backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-[var(--buddychip-grey-text)]">
                Success Rate
              </CardTitle>
              <Sparkles className="h-4 w-4 text-[var(--buddychip-grey-text)]" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-[var(--buddychip-white)]">
                {dashboardStats?.stats?.responses ? Math.round((dashboardStats.stats.responses.posted / dashboardStats.stats.responses.total) * 100) || 0 : 0}%
              </div>
              <p className="text-xs text-[var(--buddychip-grey-text)]">
                Response success rate
              </p>
            </CardContent>
          </Card>

          <Card className="bg-[var(--buddychip-light-bg)]/60 border-[var(--buddychip-grey-stroke)] backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-[var(--buddychip-grey-text)]">
                Active Accounts
              </CardTitle>
              <User className="h-4 w-4 text-[var(--buddychip-grey-text)]" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-[var(--buddychip-white)]">
                {twitterAccounts.length || 0}
              </div>
              <p className="text-xs text-[var(--buddychip-grey-text)]">
                Connected accounts
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid gap-8 lg:grid-cols-12">
          {/* Left Column - Filters & Controls */}
          <div className="lg:col-span-4">
            <div className="space-y-6">
              {/* Account Filter */}
              <Card className="bg-[var(--buddychip-light-bg)]/60 border-[var(--buddychip-grey-stroke)] backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-[var(--buddychip-white)]">Account Filter</CardTitle>
                </CardHeader>
                <CardContent>
                  <AccountFilter 
                    accounts={twitterAccounts}
                    selectedAccountIds={selectedAccountIds}
                    onAccountToggle={handleAccountToggle}
                    onSelectAll={handleSelectAllAccounts}
                    onClearAll={handleClearAllAccounts}
                    onAccountAdded={handleAccountAdded}
                  />
                </CardContent>
              </Card>

              {/* Sort & Filter */}
              <Card className="bg-[var(--buddychip-light-bg)]/60 border-[var(--buddychip-grey-stroke)] backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-[var(--buddychip-white)]">Sort & Filter</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label className="text-[var(--buddychip-white)]">Sort By</Label>
                    <select 
                      value={sortBy} 
                      onChange={(e) => setSortBy(e.target.value as any)}
                      className="w-full mt-1 bg-[var(--buddychip-black)] border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] rounded-md px-3 py-2 focus:border-[var(--buddychip-accent)] focus:outline-none"
                      style={{ backgroundColor: 'var(--buddychip-black)', color: 'var(--buddychip-white)' }}
                    >
                      <option value="recent" style={{ backgroundColor: 'var(--buddychip-black)', color: 'var(--buddychip-white)' }}>Most Recent</option>
                      <option value="engagement" style={{ backgroundColor: 'var(--buddychip-black)', color: 'var(--buddychip-white)' }}>Highest Engagement</option>
                      <option value="score" style={{ backgroundColor: 'var(--buddychip-black)', color: 'var(--buddychip-white)' }}>Highest Score</option>
                    </select>
                  </div>
                  
                  <div>
                    <Label className="text-[var(--buddychip-white)]">Status</Label>
                    <select 
                      value={filterStatus} 
                      onChange={(e) => setFilterStatus(e.target.value as any)}
                      className="w-full mt-1 bg-[var(--buddychip-black)] border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] rounded-md px-3 py-2 focus:border-[var(--buddychip-accent)] focus:outline-none"
                      style={{ backgroundColor: 'var(--buddychip-black)', color: 'var(--buddychip-white)' }}
                    >
                      <option value="all" style={{ backgroundColor: 'var(--buddychip-black)', color: 'var(--buddychip-white)' }}>All</option>
                      <option value="pending" style={{ backgroundColor: 'var(--buddychip-black)', color: 'var(--buddychip-white)' }}>Pending</option>
                      <option value="generated" style={{ backgroundColor: 'var(--buddychip-black)', color: 'var(--buddychip-white)' }}>Generated</option>
                      <option value="posted" style={{ backgroundColor: 'var(--buddychip-black)', color: 'var(--buddychip-white)' }}>Posted</option>
                    </select>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Right Column - Responses List */}
          <div className="lg:col-span-8">
            <Card className="bg-[var(--buddychip-light-bg)]/60 border-[var(--buddychip-grey-stroke)] backdrop-blur-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-[var(--buddychip-white)]">Latest Mentions Needing Response</CardTitle>
                  <Badge variant="secondary" className="bg-[var(--buddychip-accent)]/20 text-[var(--buddychip-accent)]">
                    {sortedMentions.length} mentions
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {sortedMentions.map((mention) => (
                    <div 
                      key={mention._id}
                      className="p-4 bg-[var(--buddychip-grey-stroke)]/20 rounded-lg border border-[var(--buddychip-grey-stroke)]/30"
                    >
                      {/* Mention Header */}
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Badge className={mention.priority === 'high' ? 'bg-red-500/20 text-red-400 border-red-500/30' : 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'}>
                            {mention.priority} priority
                          </Badge>
                          <Badge variant="outline" className="text-[var(--buddychip-grey-text)] border-[var(--buddychip-grey-stroke)]">
                            {mention.mentionType}
                          </Badge>
                          {mention.aiAnalysisResult?.shouldRespond && (
                            <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                              AI Recommends Response
                            </Badge>
                          )}
                        </div>
                        <span className="text-sm text-[var(--buddychip-grey-text)]">
                          {formatTimeAgo(mention.discoveredAt)}
                        </span>
                      </div>

                      {/* Author Info */}
                      <div className="mb-3 flex items-center gap-2">
                        <User className="h-4 w-4 text-[var(--buddychip-grey-text)]" />
                        <span className="text-[var(--buddychip-white)] font-medium">@{mention.mentionAuthorHandle}</span>
                        <span className="text-[var(--buddychip-grey-text)]">({mention.mentionAuthor})</span>
                      </div>

                      {/* Mention Content */}
                      <div className="mb-3 p-3 bg-[var(--buddychip-black)]/30 rounded border-l-2 border-[var(--buddychip-accent)]">
                        <p className="text-[var(--buddychip-white)]">{mention.content || mention.mentionContent}</p>
                      </div>

                      {/* AI Analysis (if available) */}
                      {mention.aiAnalysisResult && (
                        <div className="mb-3 p-3 bg-[var(--buddychip-accent)]/10 rounded border border-[var(--buddychip-accent)]/20">
                          <p className="text-sm text-[var(--buddychip-grey-text)] mb-1">AI Analysis:</p>
                          <p className="text-[var(--buddychip-white)] text-sm">{mention.aiAnalysisResult.summary || 'Analysis completed'}</p>
                          {mention.aiAnalysisResult.viralScore && (
                            <p className="text-xs text-[var(--buddychip-grey-text)] mt-1">
                              Viral Score: {mention.aiAnalysisResult.viralScore}/100
                            </p>
                          )}
                        </div>
                      )}

                      {/* Metrics & Actions */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-sm text-[var(--buddychip-grey-text)]">
                          <span className="flex items-center gap-1">
                            <Heart className="h-3 w-3" />
                            {mention.engagement?.likes || 0}
                          </span>
                          <span className="flex items-center gap-1">
                            <Repeat className="h-3 w-3" />
                            {mention.engagement?.retweets || 0}
                          </span>
                          <span className="flex items-center gap-1">
                            <MessageCircle className="h-3 w-3" />
                            {mention.engagement?.replies || 0}
                          </span>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(mention.url, '_blank')}
                            className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyResponse(mention.content || mention.mentionContent)}
                            className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleGenerateResponse(mention)}
                            disabled={generatingResponses.has(mention._id)}
                            className="text-[var(--buddychip-accent)] hover:text-[var(--buddychip-accent)]/80 disabled:opacity-50"
                          >
                            {generatingResponses.has(mention._id) ? (
                              <>
                                <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                                Generating...
                              </>
                            ) : (
                              <>
                                <Sparkles className="h-3 w-3 mr-1" />
                                Generate Response
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}

                  {sortedMentions.length === 0 && (
                    <div className="text-center py-8">
                      <Bell className="h-12 w-12 text-[var(--buddychip-grey-text)] mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-[var(--buddychip-white)] mb-2">No mentions need responses</h3>
                      <p className="text-[var(--buddychip-grey-text)]">
                        All your mentions have been processed or don't require responses.
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      
      {/* Add Account Modal */}
      <AddAccountModal
        isOpen={showAddAccountModal}
        onClose={() => setShowAddAccountModal(false)}
        onSuccess={() => {
          setShowAddAccountModal(false);
          // The query will automatically refetch due to Convex reactivity
        }}
      />

      {/* Generated Response Modal */}
      {showResponseModal && generatedResponse && (
        <div 
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={handleCloseModal}
        >
          <Card 
            className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] max-w-2xl w-full max-h-[90vh] overflow-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
              <CardTitle className="text-[var(--buddychip-white)] flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-[var(--buddychip-accent)]" />
                Generated Response
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCloseModal}
                className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
              >
                <X className="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Original Mention */}
              <div className="space-y-2">
                <Label className="text-[var(--buddychip-white)] font-medium">Original Mention</Label>
                <div className="p-3 bg-[var(--buddychip-black)]/30 rounded border-l-2 border-[var(--buddychip-grey-stroke)]">
                  <div className="flex items-center gap-2 mb-2">
                    <User className="h-4 w-4 text-[var(--buddychip-grey-text)]" />
                    <span className="text-[var(--buddychip-white)] font-medium">
                      @{generatedResponse.mention.mentionAuthorHandle || generatedResponse.mention.authorHandle}
                    </span>
                    <span className="text-[var(--buddychip-grey-text)]">
                      ({generatedResponse.mention.mentionAuthor || generatedResponse.mention.authorName})
                    </span>
                  </div>
                  <p className="text-[var(--buddychip-white)]">
                    {generatedResponse.mention.content || generatedResponse.mention.mentionContent}
                  </p>
                </div>
              </div>

              {/* Generated Response */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-[var(--buddychip-white)] font-medium">Generated Response</Label>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-[var(--buddychip-accent)] border-[var(--buddychip-accent)]">
                      {generatedResponse.style}
                    </Badge>
                    <span className="text-sm text-[var(--buddychip-grey-text)]">
                      {isEditing ? editedResponse.length : generatedResponse.characterCount}/280
                    </span>
                  </div>
                </div>
                {isEditing ? (
                  <div className="space-y-2">
                    <textarea
                      value={editedResponse}
                      onChange={(e) => setEditedResponse(e.target.value)}
                      className="w-full p-4 bg-[var(--buddychip-black)] border border-[var(--buddychip-grey-stroke)] rounded text-[var(--buddychip-white)] leading-relaxed resize-none focus:border-[var(--buddychip-accent)] focus:outline-none"
                      rows={4}
                      maxLength={280}
                      placeholder="Edit your response..."
                    />
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        onClick={handleSaveEdit}
                        className="bg-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/80 text-[var(--buddychip-white)]"
                      >
                        <Save className="h-3 w-3 mr-1" />
                        Save
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleCancelEdit}
                        className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
                      >
                        <XCircle className="h-3 w-3 mr-1" />
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="p-4 bg-[var(--buddychip-accent)]/10 rounded border border-[var(--buddychip-accent)]/20">
                    <p className="text-[var(--buddychip-white)] leading-relaxed">
                      {generatedResponse.response}
                    </p>
                  </div>
                )}
              </div>

              {/* Actions */}
              {!isEditing && (
                <div className="flex items-center justify-between pt-4 border-t border-[var(--buddychip-grey-stroke)]">
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCopyResponse(generatedResponse.response)}
                      className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleEditResponse}
                      className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    <div className="relative">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowPostOptions(!showPostOptions)}
                        disabled={isPosting}
                        className="text-[var(--buddychip-accent)] hover:text-[var(--buddychip-accent)]/80 disabled:opacity-50"
                      >
                        {isPosting ? (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            Posting...
                          </>
                        ) : (
                          <>
                            <Share2 className="h-4 w-4 mr-2" />
                            Post
                            <ChevronDown className="h-3 w-3 ml-1" />
                          </>
                        )}
                      </Button>
                      
                      {/* Post Options Dropdown */}
                      {showPostOptions && (
                        <div className="absolute bottom-full left-0 mb-2 bg-[var(--buddychip-light-bg)] border border-[var(--buddychip-grey-stroke)] rounded-lg shadow-lg z-10 min-w-48">
                          <div className="p-2 space-y-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={handlePostAsReply}
                              className="w-full justify-start text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20"
                            >
                              <MessageCircle className="h-4 w-4 mr-2" />
                              Reply to Tweet
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={handlePostAsNewTweet}
                              className="w-full justify-start text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20"
                            >
                              <Share2 className="h-4 w-4 mr-2" />
                              Post as New Tweet
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={handleCopyToClipboard}
                              className="w-full justify-start text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20"
                            >
                              <Copy className="h-4 w-4 mr-2" />
                              Copy to Clipboard
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <Button
                    onClick={handleCloseModal}
                    className="bg-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/80 text-[var(--buddychip-white)]"
                  >
                    <Check className="h-4 w-4 mr-2" />
                    Done
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

export default DashboardPage;