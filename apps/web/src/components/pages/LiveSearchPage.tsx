import { useState, useEffect } from 'react'
import { useQuery, useMutation, useAction } from 'convex/react'
import { api } from '@BuddyChipAI/backend'
import { useAuth } from '../auth/use-auth'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Separator } from '../ui/separator'
import { Skeleton } from '../ui/skeleton'
import { PageHeader } from '../ui/page-header'
import { Search, TrendingUp, Brain, BarChart3, Download, RefreshCw, Settings, Bookmark, X, Calendar, Filter, ExternalLink, Share, <PERSON><PERSON>, Heart, MessageCircle, Repeat2, Eye } from 'lucide-react'
import { cn } from '../../lib/utils'

// Types for Live Search
interface SearchResult {
  id: string
  content: string
  author: string
  authorHandle: string
  authorProfileImage?: string
  createdAt: number
  engagement: {
    likes: number
    retweets: number
    replies: number
    views?: number
  }
  url: string
  isRetweet: boolean
  source: 'xai' | 'tweetio'
  sentiment?: 'positive' | 'negative' | 'neutral'
  worthinessScore?: number
  citations?: string[]
}

interface xAIInsight {
  type: 'trend' | 'sentiment' | 'opportunity' | 'risk'
  title: string
  description: string
  confidence: number
  relatedTopics: string[]
}

interface SearchAnalytics {
  totalSearches: number
  successRate: number
  avgResponseTime: number
  topQueries: string[]
  sourcesUsed: { xai: number; tweetio: number }
  trendsDetected: number
}

function LiveSearchPage() {
  const { isAuthenticated, isLoading } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [searchSource, setSearchSource] = useState<'xai' | 'tweetio' | 'hybrid'>('hybrid')
  const [isSearching, setIsSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [xaiInsights, setXAIInsights] = useState<xAIInsight[]>([])
  const [savedSearches, setSavedSearches] = useState<string[]>([])
  const [selectedDateRange, setSelectedDateRange] = useState('24h')
  const [engagementFilter, setEngagementFilter] = useState<number>(0)
  const [verifiedOnly, setVerifiedOnly] = useState(false)

  // Convex actions and mutations for search
  const performLiveSearch = useMutation(api.twitterScraper.liveSearch)
  const performXAISearch = useAction(api.xaiLiveSearch.xaiRealTimeContentAnalysis)
  const performXAITrendAnalysis = useAction(api.xaiLiveSearch.xaiSearchTrendingTopics)

  // Analytics data
  const searchAnalytics = useQuery(api.helpers.searchAnalytics.getSearchAnalytics) as SearchAnalytics | undefined

  const handleSearch = async () => {
    if (!searchQuery.trim()) return

    setIsSearching(true)
    try {
      let results: SearchResult[] = []
      let insights: xAIInsight[] = []

      if (searchSource === 'xai' || searchSource === 'hybrid') {
        // xAI Live Search
        const xaiResult = await performXAISearch({
          query: searchQuery,
          searchSources: ['x', 'web'],
          maxResults: 20,
          analysisType: 'engagement_opportunities'
        })

        if (xaiResult.success) {
          // Parse xAI response into search results format
          results.push({
            id: `xai-${Date.now()}`,
            content: xaiResult.content,
            author: 'xAI Analysis',
            authorHandle: 'grok',
            createdAt: Date.now(),
            engagement: { likes: 0, retweets: 0, replies: 0 },
            url: '',
            isRetweet: false,
            source: 'xai',
            citations: xaiResult.citations
          })

          // Generate insights from xAI response
          insights.push({
            type: 'trend',
            title: 'Real-time Analysis',
            description: 'AI-powered insights from live data',
            confidence: 0.85,
            relatedTopics: [searchQuery]
          })
        }

        // Get trending topics analysis
        const trendResult = await performXAITrendAnalysis({
          topics: [searchQuery],
          sources: ['x', 'web'],
          hoursBack: 24
        })

        if (trendResult.success) {
          insights.push({
            type: 'trend',
            title: 'Trending Context',
            description: 'Current trends related to your search',
            confidence: 0.9,
            relatedTopics: [searchQuery]
          })
        }
      }

      if (searchSource === 'tweetio' || searchSource === 'hybrid') {
        // TweetIO Search as fallback or additional data
        const tweetioResult = await performLiveSearch({
          query: searchQuery,
          maxResults: 20,
          useXAI: false
        })

        if (tweetioResult.success && tweetioResult.results) {
          const tweetioResults: SearchResult[] = tweetioResult.results.map((tweet: any) => ({
            id: tweet.id,
            content: tweet.content,
            author: tweet.author,
            authorHandle: tweet.authorHandle,
            authorProfileImage: tweet.authorProfileImage,
            createdAt: tweet.createdAt,
            engagement: tweet.engagement,
            url: tweet.url,
            isRetweet: tweet.isRetweet,
            source: 'tweetio' as const,
            sentiment: Math.random() > 0.6 ? 'positive' : Math.random() > 0.3 ? 'neutral' : 'negative',
            worthinessScore: Math.random() * 100
          }))
          results.push(...tweetioResults)
        }
      }

      setSearchResults(results)
      setXAIInsights(insights)
      
      // Add to search history
      if (!savedSearches.includes(searchQuery)) {
        setSavedSearches(prev => [searchQuery, ...prev.slice(0, 9)])
      }
    } catch (error) {
      console.error('Search failed:', error)
    } finally {
      setIsSearching(false)
    }
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  const formatTimeAgo = (timestamp: number) => {
    const diff = Date.now() - timestamp
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)
    
    if (minutes < 1) return 'just now'
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    return `${days}d ago`
  }

  const getSentimentColor = (sentiment?: string) => {
    switch (sentiment) {
      case 'positive': return 'text-green-500'
      case 'negative': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  const getSentimentIcon = (sentiment?: string) => {
    switch (sentiment) {
      case 'positive': return '📈'
      case 'negative': return '📉'
      default: return '⚖️'
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#0E1117] text-white flex items-center justify-center">
        <div>Loading...</div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-[#0E1117] text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Please sign in to access Live Search</h1>
          <p className="text-[#6E7A8C]">You need to authenticate to use this feature.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#0E1117] text-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 max-w-7xl">
        <PageHeader
          title="Live Twitter Search"
          subtitle="Search and analyze real-time Twitter content"
        />

        {/* Search Interface */}
        <Card className="mb-8 bg-[#202631] border-[#202631]">
          <CardHeader>
            <CardTitle className="text-white">Search Interface</CardTitle>
            <CardDescription className="text-[#6E7A8C]">
              Search Twitter/X in real-time with AI analysis
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Main Search */}
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search for tweets, topics, or mentions..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="bg-[#0E1117] border-[#202631] text-white placeholder:text-[#6E7A8C]"
                />
              </div>
              <Select value={searchSource} onValueChange={(value: any) => setSearchSource(value)}>
                <SelectTrigger className="w-32 bg-[#0E1117] border-[#202631] text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-[#202631] border-[#202631]">
                  <SelectItem value="hybrid" className="text-white">Hybrid</SelectItem>
                  <SelectItem value="xai" className="text-white">xAI Only</SelectItem>
                  <SelectItem value="tweetio" className="text-white">TweetIO Only</SelectItem>
                </SelectContent>
              </Select>
              <Button 
                onClick={handleSearch} 
                disabled={isSearching || !searchQuery.trim()}
                className="bg-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/90"
              >
                {isSearching ? <RefreshCw className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                Search
              </Button>
            </div>

            {/* Filters */}
            <div className="flex gap-4 flex-wrap">
              <Select value={selectedDateRange} onValueChange={setSelectedDateRange}>
                <SelectTrigger className="w-32 bg-[#0E1117] border-[#202631] text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-[#202631] border-[#202631]">
                  <SelectItem value="1h" className="text-white">Last Hour</SelectItem>
                  <SelectItem value="24h" className="text-white">Last 24h</SelectItem>
                  <SelectItem value="7d" className="text-white">Last Week</SelectItem>
                  <SelectItem value="30d" className="text-white">Last Month</SelectItem>
                </SelectContent>
              </Select>
              
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-[#6E7A8C]" />
                <span className="text-sm text-[#6E7A8C]">Min Engagement:</span>
                <Input
                  type="number"
                  placeholder="0"
                  value={engagementFilter}
                  onChange={(e) => setEngagementFilter(Number(e.target.value))}
                  className="w-20 bg-[#0E1117] border-[#202631] text-white"
                />
              </div>

              <label className="flex items-center gap-2 text-[#6E7A8C]">
                <input
                  type="checkbox"
                  checked={verifiedOnly}
                  onChange={(e) => setVerifiedOnly(e.target.checked)}
                  className="text-[#316FE3]"
                />
                Verified only
              </label>
            </div>

            {/* Saved Searches */}
            {savedSearches.length > 0 && (
              <div>
                <p className="text-sm text-[#6E7A8C] mb-2">Recent Searches:</p>
                <div className="flex gap-2 flex-wrap">
                  {savedSearches.slice(0, 5).map((search, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="cursor-pointer bg-[var(--buddychip-light-bg)] text-[var(--buddychip-grey-text)] hover:bg-[var(--buddychip-accent)] hover:text-[var(--buddychip-white)]"
                      onClick={() => setSearchQuery(search)}
                    >
                      {search}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Results & Analytics */}
        <Tabs defaultValue="results" className="space-y-6">
          <TabsList className="bg-[#202631] border-[#202631]">
            <TabsTrigger value="results" className="text-white data-[state=active]:bg-[#316FE3]">
              Search Results ({searchResults.length})
            </TabsTrigger>
            <TabsTrigger value="insights" className="text-white data-[state=active]:bg-[#316FE3]">
              xAI Insights ({xaiInsights.length})
            </TabsTrigger>
            <TabsTrigger value="analytics" className="text-white data-[state=active]:bg-[#316FE3]">
              Analytics
            </TabsTrigger>
          </TabsList>

          {/* Search Results */}
          <TabsContent value="results" className="space-y-4">
            {isSearching ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <Card key={i} className="bg-[#202631] border-[#202631]">
                    <CardContent className="p-6">
                      <div className="flex gap-4">
                        <Skeleton className="h-12 w-12 rounded-full bg-[#0E1117]" />
                        <div className="flex-1 space-y-2">
                          <Skeleton className="h-4 w-3/4 bg-[#0E1117]" />
                          <Skeleton className="h-4 w-1/2 bg-[#0E1117]" />
                          <Skeleton className="h-16 w-full bg-[#0E1117]" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : searchResults.length > 0 ? (
              <div className="space-y-4">
                {searchResults.map((result) => (
                  <Card key={result.id} className="bg-[#202631] border-[#202631] hover:border-[#316FE3] transition-colors">
                    <CardContent className="p-6">
                      <div className="flex gap-4">
                        {result.authorProfileImage ? (
                          <img
                            src={result.authorProfileImage}
                            alt={result.author}
                            className="h-12 w-12 rounded-full"
                          />
                        ) : (
                          <div className="h-12 w-12 rounded-full bg-[#316FE3] flex items-center justify-center">
                            <span className="text-white font-semibold">
                              {result.source === 'xai' ? '🤖' : result.author[0]?.toUpperCase()}
                            </span>
                          </div>
                        )}
                        
                        <div className="flex-1 space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <span className="font-semibold text-white">{result.author}</span>
                              <span className="text-[#6E7A8C]">@{result.authorHandle}</span>
                              <Badge variant="secondary" className={cn(
                                "text-xs",
                                result.source === 'xai' ? 'bg-[#316FE3] text-white' : 'bg-[#0E1117] text-[#6E7A8C]'
                              )}>
                                {result.source === 'xai' ? 'xAI' : 'TweetIO'}
                              </Badge>
                              {result.sentiment && (
                                <Badge variant="outline" className={cn("text-xs", getSentimentColor(result.sentiment))}>
                                  {getSentimentIcon(result.sentiment)} {result.sentiment}
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-[#6E7A8C]">{formatTimeAgo(result.createdAt)}</span>
                              {result.url && (
                                <Button variant="ghost" size="sm" asChild>
                                  <a href={result.url} target="_blank" rel="noopener noreferrer">
                                    <ExternalLink className="h-4 w-4" />
                                  </a>
                                </Button>
                              )}
                            </div>
                          </div>

                          <p className="text-white leading-relaxed">{result.content}</p>

                          {result.worthinessScore && (
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-[#6E7A8C]">Worthiness Score:</span>
                              <Badge variant="outline" className={cn(
                                "text-xs",
                                result.worthinessScore > 70 ? 'border-green-500 text-green-500' :
                                result.worthinessScore > 40 ? 'border-yellow-500 text-yellow-500' :
                                'border-red-500 text-red-500'
                              )}>
                                {Math.round(result.worthinessScore)}/100
                              </Badge>
                            </div>
                          )}

                          {result.source !== 'xai' && (
                            <div className="flex items-center gap-6 text-[#6E7A8C]">
                              <div className="flex items-center gap-1">
                                <Heart className="h-4 w-4" />
                                <span className="text-sm">{formatNumber(result.engagement.likes)}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Repeat2 className="h-4 w-4" />
                                <span className="text-sm">{formatNumber(result.engagement.retweets)}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <MessageCircle className="h-4 w-4" />
                                <span className="text-sm">{formatNumber(result.engagement.replies)}</span>
                              </div>
                              {result.engagement.views && (
                                <div className="flex items-center gap-1">
                                  <Eye className="h-4 w-4" />
                                  <span className="text-sm">{formatNumber(result.engagement.views)}</span>
                                </div>
                              )}
                            </div>
                          )}

                          {result.citations && result.citations.length > 0 && (
                            <div className="mt-3 p-3 bg-[#0E1117] rounded-lg">
                              <p className="text-sm text-[#6E7A8C] mb-2">Sources:</p>
                              <div className="space-y-1">
                                {result.citations.slice(0, 3).map((citation, index) => (
                                  <a
                                    key={index}
                                    href={citation}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="block text-xs text-[#316FE3] hover:underline truncate"
                                  >
                                    {citation}
                                  </a>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card className="bg-[#202631] border-[#202631]">
                <CardContent className="p-12 text-center">
                  <Search className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-white mb-2">No search results yet</h3>
                  <p className="text-[#6E7A8C]">Enter a search query above to get started with live Twitter search</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* xAI Insights */}
          <TabsContent value="insights" className="space-y-4">
            {xaiInsights.length > 0 ? (
              <div className="grid gap-4 md:grid-cols-2">
                {xaiInsights.map((insight, index) => (
                  <Card key={index} className="bg-[#202631] border-[#202631]">
                    <CardHeader>
                      <div className="flex items-center gap-2">
                        <Brain className="h-5 w-5 text-[#316FE3]" />
                        <CardTitle className="text-white">{insight.title}</CardTitle>
                        <Badge variant="outline" className="text-xs">
                          {Math.round(insight.confidence * 100)}% confidence
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-[#6E7A8C] mb-4">{insight.description}</p>
                      {insight.relatedTopics.length > 0 && (
                        <div>
                          <p className="text-sm text-[#6E7A8C] mb-2">Related Topics:</p>
                          <div className="flex gap-2 flex-wrap">
                            {insight.relatedTopics.map((topic, i) => (
                              <Badge key={i} variant="secondary" className="bg-[#0E1117] text-[#6E7A8C]">
                                {topic}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card className="bg-[#202631] border-[#202631]">
                <CardContent className="p-12 text-center">
                  <Brain className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-white mb-2">No AI insights yet</h3>
                  <p className="text-[#6E7A8C]">Perform a search with xAI enabled to get intelligent insights</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Analytics */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              <Card className="bg-[#202631] border-[#202631]">
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-2">
                    <BarChart3 className="h-5 w-5 text-[#316FE3]" />
                    <span className="text-sm text-[#6E7A8C]">Total Searches</span>
                  </div>
                  <p className="text-2xl font-semibold text-white">
                    {searchAnalytics?.totalSearches || 0}
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-[#202631] border-[#202631]">
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingUp className="h-5 w-5 text-green-500" />
                    <span className="text-sm text-[#6E7A8C]">Success Rate</span>
                  </div>
                  <p className="text-2xl font-semibold text-white">
                    {searchAnalytics?.successRate || 0}%
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-[#202631] border-[#202631]">
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-2">
                    <RefreshCw className="h-5 w-5 text-yellow-500" />
                    <span className="text-sm text-[#6E7A8C]">Avg Response</span>
                  </div>
                  <p className="text-2xl font-semibold text-white">
                    {searchAnalytics?.avgResponseTime || 0}ms
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-[#202631] border-[#202631]">
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-2">
                    <Brain className="h-5 w-5 text-purple-500" />
                    <span className="text-sm text-[#6E7A8C]">Trends Found</span>
                  </div>
                  <p className="text-2xl font-semibold text-white">
                    {searchAnalytics?.trendsDetected || 0}
                  </p>
                </CardContent>
              </Card>
            </div>

            {searchAnalytics?.topQueries && searchAnalytics.topQueries.length > 0 && (
              <Card className="bg-[#202631] border-[#202631]">
                <CardHeader>
                  <CardTitle className="text-white">Popular Search Queries</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {searchAnalytics.topQueries.slice(0, 5).map((query, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-[#0E1117] rounded">
                        <span className="text-white">{query}</span>
                        <Badge variant="secondary" className="bg-[#316FE3] text-white">
                          #{index + 1}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

export default LiveSearchPage