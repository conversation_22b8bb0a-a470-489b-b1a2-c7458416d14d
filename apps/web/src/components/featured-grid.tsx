import React, { useEffect, useState } from 'react';
import resizeIcon from '@/assets/icon/resize.png';
import prioritizeAnimation from '@/assets/lottie/Prioritize.json';
import createAnimation from '@/assets/lottie/Create.json';
import generateAnimation from '@/assets/lottie/Generate.json';
import understandAnimation from '@/assets/lottie/Understand.json';
import Lottie from 'lottie-react';
import { AnimationItem } from 'lottie-web';

// Smart font sizing based on container width and content
const getResponsiveFontSize = (index: number, activeBox: number, type: 'title' | 'description') => {
  // const isActive = activeBox === index;
  
  // Define base sizes for different card positions and content
  const fontSizes = {
    title: {
      narrow: 'clamp(0.75rem, 2.5vw, 1.2rem)',    // For narrow cards (35-40% width)
      medium: 'clamp(0.875rem, 3vw, 1.5rem)',     // For medium cards (60% width) 
      wide: 'clamp(1rem, 3.5vw, 1.8rem)',         // For wide cards (65% width)
    },
    description: {
      narrow: 'clamp(0.625rem, 1.8vw, 0.875rem)',
      medium: 'clamp(0.75rem, 2vw, 1rem)',
      wide: 'clamp(0.8rem, 2.2vw, 1.125rem)',
    }
  };

  // Determine card width category based on layout
  const getWidthCategory = (cardIndex: number, activeIndex: number) => {
    if (activeIndex === 3 && cardIndex === 3) return 'wide';  // "Understand Your Persona" when active
    if (activeIndex === 3 && cardIndex !== 3) return 'narrow';
    
    // For other layouts, determine based on typical widths
    const layouts = [
      [60, 40, 60, 40], // Layout 0
      [40, 60, 40, 60], // Layout 1  
      [60, 40, 60, 40], // Layout 2
      [35, 65, 35, 65], // Layout 3
    ];
    
    const currentLayout = layouts[activeIndex];
    const cardWidth = currentLayout[cardIndex];
    
    if (cardWidth >= 60) return 'wide';
    if (cardWidth >= 40) return 'medium';
    return 'narrow';
  };

  const widthCategory = getWidthCategory(index, activeBox);
  return fontSizes[type][widthCategory];
};

const FeaturedGrid = () => {
  const content = [
    {
      id: 0,
      title: 'Monitor Mentions in Real-Time',
      description: 'AI-powered monitoring tracks and prioritizes important mentions across Twitter.',
      animation: prioritizeAnimation,
    },
    {
      id: 1,
      title: 'Generate Smart AI Replies',
      description: 'Multi-model AI ensemble (GPT-4, Claude, Gemini) creates perfect responses every time.',
      animation: generateAnimation,
    },
    {
      id: 2,
      title: 'Create Viral Content',
      description:
        'Advanced AI analyzes trends and generates high-engagement tweets with built-in virality prediction.',
      animation: createAnimation,
    },
    {
      id: 3,
      title: 'Analyze Your Twitter Persona',
      description:
        'Deep personality analysis helps optimize your tone, timing, and content strategy.',
      animation: understandAnimation,
    },
  ];

  const boxes2 = [
    {
      id: 0,
      layout: [
        { width: 60, height: 60, color: '#FF4D4D' },
        { width: 40, height: 60, color: '#4CAF50' },
        { width: 60, height: 40, color: '#FF9800' },
        { width: 40, height: 40, color: '#9C27B0' },
      ],
    },
    {
      id: 1,
      layout: [
        { width: 40, height: 60, color: '#FF4D4D' },
        { width: 60, height: 60, color: '#4CAF50' },
        { width: 40, height: 40, color: '#FF9800' },
        { width: 60, height: 40, color: '#9C27B0' },
      ],
    },
    {
      id: 2,
      layout: [
        { width: 60, height: 40, color: '#FF4D4D' },
        { width: 40, height: 40, color: '#4CAF50' },
        { width: 60, height: 60, color: '#FF9800' },
        { width: 40, height: 60, color: '#9C27B0' },
      ],
    },
    {
      id: 3,
      layout: [
        { width: 35, height: 40, color: '#FF4D4D' },
        { width: 65, height: 40, color: '#4CAF50' },
        { width: 35, height: 60, color: '#FF9800' },
        { width: 65, height: 60, color: '#9C27B0' },
      ],
    },
  ];

  const [activeBox, setActiveBox] = useState<number>(0);

  const [isPhone, setIsPhone] = useState(false);

  useEffect(() => {
    const checkIfPhone = () => {
      setIsPhone(window.innerWidth <= 768);
    };

    checkIfPhone();
    window.addEventListener('resize', checkIfPhone);

    return () => {
      window.removeEventListener('resize', checkIfPhone);
    };
  }, []);

  return isPhone ? (
    <div className="w-full h-full flex flex-col gap-1">
      {content.map((item, index) => (
        <div
          key={index}
          className="w-full flex flex-col border border-black relative justify-between items-end p-4 rounded-[8px]"
          style={{ 
            height: activeBox === index ? '45%' : '18%',  
            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)', 
            backgroundColor: activeBox == index ? 'black' : 'white',
            minHeight: activeBox === index ? '180px' : '70px'
          }}
        >
          <Content
            index={index}
            activeBox={activeBox}
            setActiveBox={setActiveBox}
            content={content as unknown as {
              id: number;
              title: string;
              description: string;
              animation: AnimationItem;
            }[]}
          />
        </div>
      ))}
    </div>
  ) : (
    <div className="w-full h-full flex flex-row flex-wrap">
      {boxes2[activeBox]?.layout.map((box, index) => (
        <div
          key={index}
          style={{
            width: `${box.width}%`,
            height: `${box.height}%`,
            backgroundColor: activeBox == index ? 'black' : 'white',
            transition: 'all 0.3s ease',
          }}
          className={`flex flex-col items-end justify-between p-2 sm:p-3 md:p-4 lg:p-6 border border-black relative overflow-hidden`}
        >
          <Content
            index={index}
            activeBox={activeBox}
            setActiveBox={setActiveBox}
            content={content as unknown as {
              id: number;
              title: string;
              description: string;
              animation: AnimationItem;
            }[]}
          />
        </div>
      ))}
    </div>
  );
};

export default FeaturedGrid;

const Content = ({
  index,
  activeBox,
  setActiveBox,
  content,
}: {
  index: number;
  activeBox: number;
  setActiveBox: (index: number) => void;
  content: {
    id: number;
    title: string;
    description: string;
    animation: AnimationItem;
  }[];
}) => {
  return (
    <>
      <img
        src={resizeIcon}
        alt={content[index].title}
        onClick={() => setActiveBox(index)}
        className="cursor-pointer z-20 relative w-6 h-6"
      />
      {activeBox == index && (
        <div className="w-full h-full absolute top-0 left-0 z-0">
          <Lottie animationData={content[index].animation} loop={true} className='w-full h-full object-cover' />
        </div>
      )}
      <div className={`z-10 relative w-full flex flex-col gap-1 sm:gap-2 md:gap-3 ${
            activeBox == index ? 'text-white' : 'text-black'
          }`}>
        <h1 
          className="font-semibold leading-[1.1] w-[90%] break-words hyphens-auto"
          style={{
            fontSize: getResponsiveFontSize(index, activeBox, 'title'),
          }}
        >
          {content[index].title}
        </h1>
        {activeBox == index && (
          <p 
            className="opacity-90 leading-[1.2] w-[92%] break-words hyphens-auto"
            style={{
              fontSize: getResponsiveFontSize(index, activeBox, 'description'),
            }}
          >
            {content[index].description}
          </p>
        )}
      </div>
    </>
  );
};