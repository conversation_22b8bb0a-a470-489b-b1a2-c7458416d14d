import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  className?: string;
  children?: ReactNode;
  actions?: ReactNode;
}

export function PageHeader({ 
  title, 
  subtitle, 
  className, 
  children, 
  actions 
}: PageHeaderProps) {
  return (
    <div className={cn("mb-8", className)}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-[var(--buddychip-accent)] to-purple-400 bg-clip-text text-transparent mb-3">
            {title}
          </h1>
          {subtitle && (
            <p className="text-lg text-[var(--buddychip-white)]/80">
              {subtitle}
            </p>
          )}
          {children}
        </div>
        {actions && (
          <div className="flex gap-3 mt-2 flex-shrink-0">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
}

// Standard page container component for consistent spacing
export function PageContainer({ 
  children, 
  className, 
  maxWidth = "default" 
}: { 
  children: ReactNode;
  className?: string;
  maxWidth?: "default" | "wide" | "full";
}) {
  const maxWidthClass = {
    "default": "",
    "wide": "max-w-7xl",
    "full": "max-w-none"
  }[maxWidth];

  return (
    <div className={cn(
      "container mx-auto px-4 sm:px-6 lg:px-8 py-8",
      maxWidthClass,
      className
    )}>
      {children}
    </div>
  );
}