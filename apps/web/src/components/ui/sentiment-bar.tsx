import { cn } from "../../lib/utils";

interface SentimentBarProps {
  sentiment: "bullish" | "bearish" | "neutral";
  score: number; // 1-100
  confidence?: number; // 0-1
  compact?: boolean;
  showLabel?: boolean;
  showScore?: boolean;
  className?: string;
}

export function SentimentBar({
  sentiment,
  score,
  confidence = 1,
  compact = false,
  showLabel = true,
  showScore = true,
  className,
}: SentimentBarProps) {
  // Determine colors and styling based on sentiment and score
  const getSentimentStyle = () => {
    const opacity = confidence * 0.8 + 0.2; // Min 20% opacity
    
    if (sentiment === "bullish") {
      if (score >= 76) {
        return {
          bg: "bg-gradient-to-r from-green-500 to-emerald-400",
          border: "border-green-400",
          text: "text-green-300",
          icon: "📈",
          intensity: "Very Bullish"
        };
      } else if (score >= 56) {
        return {
          bg: "bg-gradient-to-r from-green-600 to-green-500",
          border: "border-green-500",
          text: "text-green-400",
          icon: "📊",
          intensity: "Bullish"
        };
      }
    } else if (sentiment === "bearish") {
      if (score <= 25) {
        return {
          bg: "bg-gradient-to-r from-red-500 to-pink-500",
          border: "border-red-400",
          text: "text-red-300",
          icon: "📉",
          intensity: "Very Bearish"
        };
      } else if (score <= 45) {
        return {
          bg: "bg-gradient-to-r from-red-600 to-red-500",
          border: "border-red-500",
          text: "text-red-400",
          icon: "📊",
          intensity: "Bearish"
        };
      }
    }
    
    // Neutral or edge cases
    return {
      bg: "bg-gradient-to-r from-gray-600 to-slate-500",
      border: "border-gray-500",
      text: "text-gray-400",
      icon: "⚖️",
      intensity: "Neutral"
    };
  };

  const style = getSentimentStyle();
  const barWidth = Math.max(10, Math.min(100, score)); // Ensure minimum visibility

  if (compact) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className="relative w-16 h-2 bg-gray-800 rounded-full overflow-hidden">
          <div 
            className={cn(style.bg, "h-full transition-all duration-300")}
            style={{ 
              width: `${barWidth}%`,
              opacity: confidence 
            }}
          />
        </div>
        {showScore && (
          <span className={cn("text-xs font-medium", style.text)}>
            {score}
          </span>
        )}
      </div>
    );
  }

  return (
    <div className={cn("space-y-2", className)}>
      {/* Header with icon and label */}
      {showLabel && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm">{style.icon}</span>
            <span className={cn("text-sm font-medium", style.text)}>
              {style.intensity}
            </span>
            {confidence < 0.7 && (
              <span className="text-xs text-gray-500 bg-gray-800 px-2 py-1 rounded">
                Low Confidence
              </span>
            )}
          </div>
          {showScore && (
            <div className="flex items-center gap-1">
              <span className={cn("text-lg font-bold", style.text)}>
                {score}
              </span>
              <span className="text-xs text-gray-500">/100</span>
            </div>
          )}
        </div>
      )}

      {/* Progress Bar */}
      <div className="relative">
        <div className="w-full h-3 bg-gray-800 rounded-full overflow-hidden">
          <div 
            className={cn(
              style.bg,
              "h-full transition-all duration-500 ease-out",
              "shadow-lg"
            )}
            style={{ 
              width: `${barWidth}%`,
              opacity: confidence * 0.9 + 0.1
            }}
          />
        </div>
        
        {/* Confidence indicator border */}
        <div 
          className={cn(
            "absolute inset-0 rounded-full border-2 transition-all duration-300",
            style.border
          )}
          style={{ opacity: confidence }}
        />

        {/* Score markers */}
        <div className="absolute inset-0 flex justify-between items-center px-1">
          <div className="w-px h-2 bg-gray-600" /> {/* 0 marker */}
          <div className="w-px h-2 bg-gray-600" /> {/* 25 marker */}
          <div className="w-px h-2 bg-gray-600" /> {/* 50 marker */}
          <div className="w-px h-2 bg-gray-600" /> {/* 75 marker */}
          <div className="w-px h-2 bg-gray-600" /> {/* 100 marker */}
        </div>
      </div>

      {/* Scale labels */}
      <div className="flex justify-between text-xs text-gray-500">
        <span>Very Bearish</span>
        <span>Neutral</span>
        <span>Very Bullish</span>
      </div>
    </div>
  );
}

interface SentimentTooltipProps {
  sentimentAnalysis: {
    sentiment: "bullish" | "bearish" | "neutral";
    sentimentScore: number;
    confidence: number;
    reasoning: string;
    keyWords: string[];
    marketSentiment: {
      bullishScore: number;
      bearishScore: number;
      neutralScore: number;
    };
  };
}

export function SentimentTooltip({ sentimentAnalysis }: SentimentTooltipProps) {
  return (
    <div className="bg-black border border-gray-700 rounded-lg p-3 max-w-sm space-y-2">
      <div className="flex items-center gap-2">
        <span className="font-semibold text-white">Sentiment Analysis</span>
        <span className="text-xs text-gray-400">
          {Math.round(sentimentAnalysis.confidence * 100)}% confidence
        </span>
      </div>
      
      <SentimentBar 
        sentiment={sentimentAnalysis.sentiment}
        score={sentimentAnalysis.sentimentScore}
        confidence={sentimentAnalysis.confidence}
        compact={false}
        showLabel={true}
        showScore={true}
      />
      
      <div className="space-y-1">
        <p className="text-xs text-gray-300">
          <strong>Reasoning:</strong> {sentimentAnalysis.reasoning}
        </p>
        
        {sentimentAnalysis.keyWords.length > 0 && (
          <div className="flex flex-wrap gap-1">
            <span className="text-xs text-gray-400">Key words:</span>
            {sentimentAnalysis.keyWords.map((word, idx) => (
              <span key={idx} className="text-xs bg-gray-800 px-2 py-1 rounded">
                {word}
              </span>
            ))}
          </div>
        )}
        
        <div className="grid grid-cols-3 gap-2 text-xs">
          <div className="text-center">
            <div className="text-green-400 font-medium">
              {sentimentAnalysis.marketSentiment.bullishScore}%
            </div>
            <div className="text-gray-500">Bullish</div>
          </div>
          <div className="text-center">
            <div className="text-gray-400 font-medium">
              {sentimentAnalysis.marketSentiment.neutralScore}%
            </div>
            <div className="text-gray-500">Neutral</div>
          </div>
          <div className="text-center">
            <div className="text-red-400 font-medium">
              {sentimentAnalysis.marketSentiment.bearishScore}%
            </div>
            <div className="text-gray-500">Bearish</div>
          </div>
        </div>
      </div>
    </div>
  );
}

interface SentimentIndicatorProps {
  sentiment: "bullish" | "bearish" | "neutral";
  score: number;
  size?: "sm" | "md" | "lg";
  showScore?: boolean;
}

export function SentimentIndicator({ 
  sentiment, 
  score, 
  size = "md", 
  showScore = true 
}: SentimentIndicatorProps) {
  const getIndicatorStyle = () => {
    if (sentiment === "bullish") {
      return {
        bg: score >= 76 ? "bg-green-500" : "bg-green-600",
        text: score >= 76 ? "text-green-300" : "text-green-400",
        icon: score >= 76 ? "🚀" : "📈",
      };
    } else if (sentiment === "bearish") {
      return {
        bg: score <= 25 ? "bg-red-500" : "bg-red-600", 
        text: score <= 25 ? "text-red-300" : "text-red-400",
        icon: score <= 25 ? "💥" : "📉",
      };
    }
    
    return {
      bg: "bg-gray-600",
      text: "text-gray-400", 
      icon: "⚖️",
    };
  };

  const style = getIndicatorStyle();
  const sizeClasses = {
    sm: "px-2 py-1 text-xs",
    md: "px-3 py-1 text-sm", 
    lg: "px-4 py-2 text-base",
  };

  return (
    <div className={cn(
      "inline-flex items-center gap-1 rounded-full border border-gray-700",
      style.bg,
      sizeClasses[size]
    )}>
      <span>{style.icon}</span>
      {showScore && (
        <span className={cn("font-medium", style.text)}>
          {score}
        </span>
      )}
    </div>
  );
}