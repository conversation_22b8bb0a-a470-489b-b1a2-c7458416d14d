import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { X, Trash2 } from "lucide-react";

interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  showDontShowToday?: boolean;
  storageKey?: string;
}

export function ConfirmDialog({
  isOpen,
  onClose,
  onConfirm,
  title = "Confirm Action",
  message = "Are you sure you want to continue?",
  confirmText = "Confirm",
  cancelText = "Cancel",
  showDontShowToday = false,
  storageKey = "confirm-dialog-dismissed"
}: ConfirmDialogProps) {
  const [dontShowToday, setDontShowToday] = useState(false);

  // Check if dialog was dismissed today
  useEffect(() => {
    if (showDontShowToday && storageKey) {
      const dismissed = localStorage.getItem(storageKey);
      if (dismissed) {
        const dismissedDate = new Date(dismissed);
        const today = new Date();
        
        // Check if it's the same day
        if (dismissedDate.toDateString() === today.toDateString()) {
          // Auto-dismiss if user chose "don't show today"
          onClose();
          return;
        }
      }
    }
  }, [isOpen, showDontShowToday, storageKey, onClose]);

  const handleConfirm = () => {
    // Save "don't show today" preference
    if (dontShowToday && storageKey) {
      localStorage.setItem(storageKey, new Date().toISOString());
    }
    
    onConfirm();
    onClose();
  };

  const handleCancel = () => {
    // Save "don't show today" preference even on cancel
    if (dontShowToday && storageKey) {
      localStorage.setItem(storageKey, new Date().toISOString());
    }
    
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] w-full max-w-md mx-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-400" />
              <CardTitle className="text-[var(--buddychip-white)]">{title}</CardTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Message */}
          <p className="text-[var(--buddychip-white)] text-center">
            {message}
          </p>

          {/* Don't show today checkbox */}
          {showDontShowToday && (
            <div className="flex items-center space-x-2 justify-center">
              <Checkbox
                id="dontShowToday"
                checked={dontShowToday}
                onCheckedChange={(checked) => setDontShowToday(checked === true)}
                className="border-[var(--buddychip-grey-stroke)] data-[state=checked]:bg-[var(--buddychip-accent)] data-[state=checked]:border-[var(--buddychip-accent)]"
              />
              <label
                htmlFor="dontShowToday"
                className="text-sm text-[var(--buddychip-grey-text)] cursor-pointer"
              >
                Don't show this today
              </label>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="flex-1 border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20"
            >
              {cancelText}
            </Button>
            <Button
              onClick={handleConfirm}
              className="flex-1 bg-red-500 hover:bg-red-600 text-white"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {confirmText}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}