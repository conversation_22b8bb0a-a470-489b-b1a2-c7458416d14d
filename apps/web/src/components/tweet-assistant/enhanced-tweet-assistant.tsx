/**
 * Enhanced Tweet Assistant with Real-time Preview and Modern UI/UX
 * 
 * Features:
 * - Real-time tweet preview as you type
 * - Modern, mobile-first responsive design
 * - Intelligent URL validation and suggestions
 * - Smooth animations and micro-interactions
 * - Enhanced error handling and user feedback
 * - Progressive enhancement workflow
 */

import { useState, useEffect, useCallback, useMemo } from "react";
import { useAction } from "convex/react";
import { api } from "@BuddyChipPro/backend";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Progress } from "@/components/ui/progress";
import { 
  Search,
  ExternalLink, 
  MessageSquare, 
  RefreshCw, 
  Sparkles,
  Heart,
  Repeat,
  MessageCircle,
  Eye,
  Calendar,
  User,
  CheckCircle,
  AlertCircle,
  Clock,
  Zap,
  ArrowR<PERSON>,
  <PERSON><PERSON>,
  <PERSON>hare,
  Wand2
} from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

interface TweetData {
  id: string;
  url: string;
  content: string;
  author: {
    name: string;
    username: string;
    profileImage?: string;
    verified?: boolean;
    followers?: number;
  };
  engagement: {
    likes: number;
    retweets: number;
    replies: number;
    views?: number;
  };
  createdAt: string;
  isRetweet?: boolean;
  isReply?: boolean;
  metadata?: {
    hasMedia?: boolean;
    hasUrls?: boolean;
    hasHashtags?: boolean;
    hasMentions?: boolean;
  };
}

interface PreviewState {
  isLoading: boolean;
  data: TweetData | null;
  error: string | null;
  progress: number;
}

export function EnhancedTweetAssistant() {
  const [url, setUrl] = useState("");
  const [mode, setMode] = useState<'reply' | 'remake'>('reply');
  const [preview, setPreview] = useState<PreviewState>({
    isLoading: false,
    data: null,
    error: null,
    progress: 0
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [generatedResponses, setGeneratedResponses] = useState<any[]>([]);

  // Actions
  const fetchTweet = useAction(api.twitter.fetchTweetFromUrl.fetchTweetFromUrl);
  const validateUrl = useAction(api.twitter.fetchTweetFromUrl.validateTwitterUrl);
  const generateResponses = useAction(api.responseGeneration.generateResponses);

  // URL validation with regex
  const urlValidation = useMemo(() => {
    if (!url.trim()) return { isValid: false, message: "" };
    
    const twitterUrlPatterns = [
      /^https?:\/\/(www\.)?(twitter\.com|x\.com)\/[^\/\?]+\/status\/\d+/,
      /^https?:\/\/(www\.)?(twitter\.com|x\.com)\/i\/web\/status\/\d+/,
    ];
    
    const isValid = twitterUrlPatterns.some(pattern => pattern.test(url));
    
    if (!isValid && (url.includes('twitter.com') || url.includes('x.com'))) {
      return { isValid: false, message: "URL format looks incorrect. Try copying the full tweet URL." };
    }
    
    if (!isValid && url.length > 10) {
      return { isValid: false, message: "Please enter a valid Twitter/X tweet URL" };
    }
    
    return { isValid, message: isValid ? "Valid Twitter URL detected!" : "" };
  }, [url]);

  // Real-time preview with debouncing
  useEffect(() => {
    if (!urlValidation.isValid || !url.trim()) {
      setPreview({ isLoading: false, data: null, error: null, progress: 0 });
      return;
    }

    const debounceTimer = setTimeout(async () => {
      setPreview(prev => ({ ...prev, isLoading: true, error: null, progress: 10 }));
      
      try {
        // Simulate progress for better UX
        const progressInterval = setInterval(() => {
          setPreview(prev => ({ 
            ...prev, 
            progress: Math.min(prev.progress + 15, 80) 
          }));
        }, 200);

        const result = await fetchTweet({ url: url.trim() });
        
        clearInterval(progressInterval);
        setPreview({ progress: 100, isLoading: false, error: null, data: null });
        
        if (result.success && result.tweet) {
          const tweetData: TweetData = {
            id: result.tweet.id,
            url: result.tweet.url,
            content: result.tweet.content,
            author: {
              name: result.tweet.author.name,
              username: result.tweet.author.username,
              profileImage: result.tweet.author.profileImage,
              verified: result.tweet.author.verified,
              followers: result.tweet.author.followers,
            },
            engagement: {
              likes: result.tweet.engagement.likes,
              retweets: result.tweet.engagement.retweets,
              replies: result.tweet.engagement.replies,
              views: result.tweet.engagement.views,
            },
            createdAt: result.tweet.createdAt, // Use the timestamp string directly
            isRetweet: result.tweet.isRetweet,
            isReply: result.tweet.isReply,
            metadata: result.tweet.metadata,
          };
          
          setPreview({ 
            isLoading: false, 
            data: tweetData, 
            error: null, 
            progress: 100 
          });
          
          // Auto-clear progress after success
          setTimeout(() => {
            setPreview(prev => ({ ...prev, progress: 0 }));
          }, 1000);
        } else {
          setPreview({ 
            isLoading: false, 
            data: null, 
            error: result.error || "Failed to fetch tweet", 
            progress: 0 
          });
        }
      } catch (error) {
        setPreview({ 
          isLoading: false, 
          data: null, 
          error: "Network error. Please try again.", 
          progress: 0 
        });
      }
    }, 1000); // 1 second debounce

    return () => clearTimeout(debounceTimer);
  }, [url, urlValidation.isValid, fetchTweet]);

  const handleUrlChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setUrl(e.target.value);
  }, []);

  const handleGenerate = useCallback(async () => {
    if (!preview.data) {
      toast.error("Please load a valid tweet first");
      return;
    }
    
    setIsGenerating(true);
    setGeneratedResponses([]);
    
    try {
      toast(`Generating ${mode === 'reply' ? 'replies' : 'remakes'} for this tweet...`);
      
      const result = await generateResponses({
        tweetUrl: preview.data.url,
        tweetContent: preview.data.content,
        mode: mode,
        authorHandle: preview.data.author.username,
        authorDisplayName: preview.data.author.name,
        authorIsVerified: preview.data.author.verified,
        authorFollowerCount: preview.data.author.followers,
        engagement: {
          likes: preview.data.engagement.likes,
          retweets: preview.data.engagement.retweets,
          replies: preview.data.engagement.replies,
        },
        responseStyles: ['professional', 'casual', 'humorous', 'engaging'],
      });
      
      setGeneratedResponses(result.responses);
      toast.success(`Generated ${result.responses.length} ${mode === 'reply' ? 'replies' : 'remakes'}!`);
      
    } catch (error) {
      console.error("Error generating responses:", error);
      toast.error("Failed to generate responses. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  }, [preview.data, mode, generateResponses]);

  const handleCopyUrl = useCallback(() => {
    if (preview.data?.url) {
      navigator.clipboard.writeText(preview.data.url);
      toast.success("Tweet URL copied to clipboard!");
    }
  }, [preview.data]);

  const formatEngagementNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getEngagementTotal = (engagement: TweetData['engagement']): number => {
    return engagement.likes + engagement.retweets + engagement.replies;
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">

      {/* Main Input Section */}
      <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm shadow-2xl">
        <CardHeader>
          <CardTitle className="text-xl text-[#F5F7FA] flex items-center gap-3">
            <Search className="h-5 w-5 text-[#316FE3]" />
            Tweet URL Input
            {urlValidation.isValid && (
              <CheckCircle className="h-5 w-5 text-green-400" />
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* URL Input with Real-time Validation */}
          <div className="space-y-3">
            <div className="relative">
              <Input
                type="url"
                placeholder="https://twitter.com/username/status/1234567890 or https://x.com/username/status/1234567890"
                value={url}
                onChange={handleUrlChange}
                className={cn(
                  "bg-[#0F1419] border-[#202631] text-[#F5F7FA] placeholder-[#6E7A8C] h-12 pr-12 transition-all duration-200",
                  urlValidation.isValid && "border-green-500/50 focus:border-green-500",
                  preview.error && "border-red-500/50 focus:border-red-500"
                )}
              />
              {urlValidation.isValid && (
                <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-green-400" />
              )}
              {preview.error && (
                <AlertCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-red-400" />
              )}
            </div>
            
            {/* Progress Bar */}
            {preview.progress > 0 && preview.progress < 100 && (
              <Progress value={preview.progress} className="h-1" />
            )}
            
            {/* Validation Message */}
            {urlValidation.message && (
              <div className={cn(
                "flex items-center gap-2 text-sm",
                urlValidation.isValid ? "text-green-400" : "text-red-400"
              )}>
                {urlValidation.isValid ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <AlertCircle className="h-4 w-4" />
                )}
                {urlValidation.message}
              </div>
            )}

            {/* Error Message */}
            {preview.error && (
              <div className="flex items-center gap-2 text-red-400 text-sm bg-red-500/10 p-3 rounded-lg border border-red-500/20">
                <AlertCircle className="h-4 w-4" />
                {preview.error}
              </div>
            )}
          </div>

          {/* Mode Selection */}
          <div className="space-y-3">
            <h3 className="text-[#F5F7FA] font-medium">Choose Action</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <button
                onClick={() => setMode('reply')}
                className={cn(
                  "p-4 rounded-xl border transition-all duration-200 text-left group",
                  mode === 'reply'
                    ? "border-[#316FE3] bg-[#316FE3]/10"
                    : "border-[#202631] bg-[#000000]/50 hover:border-[#316FE3]/50"
                )}
              >
                <div className="flex items-center gap-3 mb-2">
                  <MessageSquare className={cn(
                    "h-5 w-5 transition-colors",
                    mode === 'reply' ? "text-[#316FE3]" : "text-[#6E7A8C] group-hover:text-[#316FE3]"
                  )} />
                  <span className={cn(
                    "font-medium transition-colors",
                    mode === 'reply' ? "text-[#316FE3]" : "text-[#F5F7FA] group-hover:text-[#316FE3]"
                  )}>
                    Reply Mode
                  </span>
                </div>
                <p className="text-sm text-[#6E7A8C]">
                  Generate thoughtful responses to engage with the tweet
                </p>
              </button>
              
              <button
                onClick={() => setMode('remake')}
                className={cn(
                  "p-4 rounded-xl border transition-all duration-200 text-left group",
                  mode === 'remake'
                    ? "border-[#316FE3] bg-[#316FE3]/10"
                    : "border-[#202631] bg-[#000000]/50 hover:border-[#316FE3]/50"
                )}
              >
                <div className="flex items-center gap-3 mb-2">
                  <RefreshCw className={cn(
                    "h-5 w-5 transition-colors",
                    mode === 'remake' ? "text-[#316FE3]" : "text-[#6E7A8C] group-hover:text-[#316FE3]"
                  )} />
                  <span className={cn(
                    "font-medium transition-colors",
                    mode === 'remake' ? "text-[#316FE3]" : "text-[#F5F7FA] group-hover:text-[#316FE3]"
                  )}>
                    Remake Mode
                  </span>
                </div>
                <p className="text-sm text-[#6E7A8C]">
                  Rewrite the tweet in different styles and tones
                </p>
              </button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Real-time Tweet Preview */}
      {(preview.isLoading || preview.data) && (
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm shadow-xl">
          <CardHeader>
            <CardTitle className="text-lg text-[#F5F7FA] flex items-center gap-3">
              <Eye className="h-5 w-5 text-[#316FE3]" />
              Tweet Preview
              {preview.isLoading && (
                <div className="flex items-center gap-2 text-[#6E7A8C]">
                  <div className="w-4 h-4 border-2 border-[#316FE3] border-t-transparent rounded-full animate-spin" />
                  <span className="text-sm">Loading...</span>
                </div>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {preview.isLoading ? (
              <div className="space-y-4 animate-pulse">
                <div className="flex items-center gap-3">
                  <Skeleton className="w-12 h-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="w-32 h-4" />
                    <Skeleton className="w-24 h-3" />
                  </div>
                </div>
                <Skeleton className="w-full h-20" />
                <div className="flex gap-4">
                  <Skeleton className="w-16 h-4" />
                  <Skeleton className="w-16 h-4" />
                  <Skeleton className="w-16 h-4" />
                </div>
              </div>
            ) : preview.data ? (
              <div className="space-y-4">
                {/* Author Info */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-[#316FE3] to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                      {preview.data.author.profileImage ? (
                        <img 
                          src={preview.data.author.profileImage} 
                          alt={preview.data.author.name}
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        preview.data.author.name.charAt(0)
                      )}
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-semibold text-[#F5F7FA]">
                          {preview.data.author.name}
                        </span>
                        {preview.data.author.verified && (
                          <CheckCircle className="h-4 w-4 text-blue-400 fill-current" />
                        )}
                      </div>
                      <div className="flex items-center gap-2 text-sm text-[#6E7A8C]">
                        <span>@{preview.data.author.username}</span>
                        {preview.data.author.followers && (
                          <>
                            <span>•</span>
                            <span>{formatEngagementNumber(preview.data.author.followers)} followers</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={handleCopyUrl}
                      className="text-[#6E7A8C] hover:text-[#F5F7FA]"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      asChild
                      className="text-[#6E7A8C] hover:text-[#F5F7FA]"
                    >
                      <a 
                        href={preview.data.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </a>
                    </Button>
                  </div>
                </div>

                {/* Tweet Content */}
                <div className="bg-[#0F1419] p-4 rounded-xl border border-[#202631]">
                  <p className="text-[#F5F7FA] leading-relaxed mb-3">
                    {preview.data.content}
                  </p>
                  
                  {/* Metadata Badges */}
                  {preview.data.metadata && (
                    <div className="flex flex-wrap gap-2 mb-3">
                      {preview.data.metadata.hasMedia && (
                        <Badge variant="secondary" className="bg-purple-500/20 text-purple-400">
                          📷 Media
                        </Badge>
                      )}
                      {preview.data.metadata.hasUrls && (
                        <Badge variant="secondary" className="bg-blue-500/20 text-blue-400">
                          🔗 Links
                        </Badge>
                      )}
                      {preview.data.metadata.hasHashtags && (
                        <Badge variant="secondary" className="bg-green-500/20 text-green-400">
                          # Hashtags
                        </Badge>
                      )}
                      {preview.data.metadata.hasMentions && (
                        <Badge variant="secondary" className="bg-orange-500/20 text-orange-400">
                          @ Mentions
                        </Badge>
                      )}
                      {preview.data.isRetweet && (
                        <Badge variant="secondary" className="bg-cyan-500/20 text-cyan-400">
                          🔁 Retweet
                        </Badge>
                      )}
                      {preview.data.isReply && (
                        <Badge variant="secondary" className="bg-pink-500/20 text-pink-400">
                          💬 Reply
                        </Badge>
                      )}
                    </div>
                  )}
                  
                  {/* Engagement Stats */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-6">
                      <div className="flex items-center gap-1 text-pink-400">
                        <Heart className="h-4 w-4" />
                        <span className="text-sm font-medium">
                          {formatEngagementNumber(preview.data.engagement.likes)}
                        </span>
                      </div>
                      <div className="flex items-center gap-1 text-green-400">
                        <Repeat className="h-4 w-4" />
                        <span className="text-sm font-medium">
                          {formatEngagementNumber(preview.data.engagement.retweets)}
                        </span>
                      </div>
                      <div className="flex items-center gap-1 text-blue-400">
                        <MessageCircle className="h-4 w-4" />
                        <span className="text-sm font-medium">
                          {formatEngagementNumber(preview.data.engagement.replies)}
                        </span>
                      </div>
                      {preview.data.engagement.views && (
                        <div className="flex items-center gap-1 text-[#6E7A8C]">
                          <Eye className="h-4 w-4" />
                          <span className="text-sm font-medium">
                            {formatEngagementNumber(preview.data.engagement.views)}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="text-xs text-[#6E7A8C]">
                      {preview.data.createdAt ? new Date(preview.data.createdAt).toLocaleDateString() : 'Recent'}
                    </div>
                  </div>
                </div>

                {/* Engagement Analysis */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-[#0F1419] rounded-lg border border-[#202631]">
                    <div className="text-lg font-semibold text-[#F5F7FA] mb-1">
                      {formatEngagementNumber(getEngagementTotal(preview.data.engagement))}
                    </div>
                    <div className="text-xs text-[#6E7A8C]">Total Engagement</div>
                  </div>
                  <div className="text-center p-3 bg-[#0F1419] rounded-lg border border-[#202631]">
                    <div className="text-lg font-semibold text-[#316FE3] mb-1">
                      {preview.data.engagement.likes > 100 ? "High" : 
                       preview.data.engagement.likes > 10 ? "Medium" : "Low"}
                    </div>
                    <div className="text-xs text-[#6E7A8C]">Engagement Level</div>
                  </div>
                  <div className="text-center p-3 bg-[#0F1419] rounded-lg border border-[#202631]">
                    <div className="text-lg font-semibold text-green-400 mb-1">
                      {preview.data.content.length}
                    </div>
                    <div className="text-xs text-[#6E7A8C]">Characters</div>
                  </div>
                </div>

                {/* Generate Button */}
                <Button
                  onClick={handleGenerate}
                  disabled={isGenerating}
                  className="w-full bg-gradient-to-r from-[#316FE3] to-purple-600 text-white hover:from-[#316FE3]/90 hover:to-purple-600/90 h-12 text-base font-medium"
                >
                  {isGenerating ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Generating...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Sparkles className="h-5 w-5" />
                      Generate {mode === 'reply' ? 'Replies' : 'Remakes'}
                      <ArrowRight className="h-4 w-4" />
                    </div>
                  )}
                </Button>
              </div>
            ) : null}
          </CardContent>
        </Card>
      )}

      {/* Generated Responses Section */}
      {generatedResponses.length > 0 && (
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm shadow-xl">
          <CardHeader>
            <CardTitle className="text-lg text-[#F5F7FA] flex items-center gap-3">
              <Sparkles className="h-5 w-5 text-[#316FE3]" />
              Generated {mode === 'reply' ? 'Replies' : 'Remakes'}
              <Badge variant="secondary" className="bg-[#316FE3]/20 text-[#316FE3]">
                {generatedResponses.length} responses
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {generatedResponses.map((response, index) => (
              <div key={index} className="bg-[#0F1419] p-4 rounded-xl border border-[#202631]">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant="outline" 
                      className={cn(
                        "text-xs",
                        response.style === 'professional' && "border-blue-500/50 text-blue-400",
                        response.style === 'casual' && "border-green-500/50 text-green-400", 
                        response.style === 'humorous' && "border-yellow-500/50 text-yellow-400",
                        response.style === 'engaging' && "border-purple-500/50 text-purple-400"
                      )}
                    >
                      {response.style}
                    </Badge>
                    <span className="text-xs text-[#6E7A8C]">
                      {response.characterCount}/280 characters
                    </span>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => {
                        navigator.clipboard.writeText(response.content);
                        toast.success("Response copied to clipboard!");
                      }}
                      className="text-[#6E7A8C] hover:text-[#F5F7FA] h-8 w-8 p-0"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => {
                        const tweetText = encodeURIComponent(response.content);
                        const tweetUrl = mode === 'reply' 
                          ? `https://twitter.com/intent/tweet?in_reply_to=${preview.data?.id}&text=${tweetText}`
                          : `https://twitter.com/intent/tweet?text=${tweetText}`;
                        window.open(tweetUrl, '_blank');
                      }}
                      className="text-[#6E7A8C] hover:text-[#F5F7FA] h-8 w-8 p-0"
                    >
                      <Share className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <p className="text-[#F5F7FA] leading-relaxed mb-3">
                  {response.content}
                </p>
                
                <div className="flex items-center justify-between text-xs text-[#6E7A8C]">
                  <div className="flex items-center gap-4">
                    <span>❤️ ~{response.estimatedEngagement.likes}</span>
                    <span>🔁 ~{response.estimatedEngagement.retweets}</span>
                    <span>💬 ~{response.estimatedEngagement.replies}</span>
                  </div>
                  <span>Generated by {response.model}</span>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Example URLs */}
      <Card className="bg-[#000000]/50 border-[#202631]/50 backdrop-blur-sm">
        <CardContent className="pt-6">
          <h3 className="text-[#F5F7FA] font-medium mb-3">Supported URL formats:</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-[#6E7A8C]">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <code>https://twitter.com/username/status/1234567890</code>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <code>https://x.com/username/status/1234567890</code>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <code>https://twitter.com/i/web/status/1234567890</code>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <code>https://x.com/i/web/status/1234567890</code>
            </div>
          </div>
          <div className="mt-4 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
            <p className="text-blue-300 text-sm">
              <strong>✅ x.com links are fully supported!</strong> Copy any tweet URL from Twitter/X and paste it above.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}