import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, ExternalLink, MessageSquare, RefreshCw } from "lucide-react";

export interface TweetUrlInputProps {
  onUrlSubmit: (url: string, mode: 'reply' | 'remake') => void;
  isLoading?: boolean;
}

export function TweetUrlInput({ onUrlSubmit, isLoading = false }: TweetUrlInputProps) {
  const [url, setUrl] = useState("");
  const [selectedMode, setSelectedMode] = useState<'reply' | 'remake'>('reply');
  const [error, setError] = useState("");

  const validateTwitterUrl = (inputUrl: string): boolean => {
    const twitterUrlPatterns = [
      /^https?:\/\/(www\.)?(twitter\.com|x\.com)\/\w+\/status\/\d+/,
      /^https?:\/\/(www\.)?(twitter\.com|x\.com)\/i\/web\/status\/\d+/,
    ];
    
    return twitterUrlPatterns.some(pattern => pattern.test(inputUrl));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    
    if (!url.trim()) {
      setError("Please enter a Twitter/X URL");
      return;
    }
    
    if (!validateTwitterUrl(url.trim())) {
      setError("Please enter a valid Twitter/X tweet URL");
      return;
    }
    
    onUrlSubmit(url.trim(), selectedMode);
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUrl(e.target.value);
    if (error) setError("");
  };

  return (
    <Card className="bg-[var(--buddychip-black)]/80 backdrop-blur-sm border border-[var(--buddychip-grey-stroke)]/50 shadow-2xl">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-[var(--buddychip-white)] flex items-center">
          <ExternalLink className="h-5 w-5 mr-3 text-[var(--buddychip-accent)]" />
          Tweet Assistant
        </CardTitle>
        <p className="text-sm text-[var(--buddychip-grey-text)]">
          Paste a Twitter/X URL to generate AI responses or remake the tweet
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* URL Input */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="tweet-url" className="text-[var(--buddychip-white)] text-sm font-medium mb-2 block">
              Tweet URL
            </Label>
            <Input
              id="tweet-url"
              type="url"
              placeholder="https://twitter.com/username/status/1234567890 or https://x.com/username/status/1234567890"
              value={url}
              onChange={handleUrlChange}
              className={`bg-[var(--buddychip-grey-stroke)]/50 border-[var(--buddychip-grey-stroke)]/50 text-[var(--buddychip-white)] focus:border-[var(--buddychip-accent)] focus:ring-[var(--buddychip-accent)]/20 transition-all duration-200 ${
                error ? 'border-red-500/50 focus:border-red-500' : ''
              }`}
              disabled={isLoading}
            />
            {error && (
              <div className="flex items-center mt-2 text-red-400 text-sm">
                <AlertCircle className="h-4 w-4 mr-2" />
                {error}
              </div>
            )}
          </div>

          {/* Mode Selection */}
          <div>
            <Label className="text-[var(--buddychip-white)] text-sm font-medium mb-3 block">
              Choose Action
            </Label>
            <div className="grid grid-cols-2 gap-3">
              <button
                type="button"
                onClick={() => setSelectedMode('reply')}
                className={`p-4 rounded-xl border transition-all duration-200 text-left ${
                  selectedMode === 'reply'
                    ? 'border-[var(--buddychip-accent)] bg-[var(--buddychip-accent)]/10'
                    : 'border-[var(--buddychip-grey-stroke)]/50 bg-[var(--buddychip-grey-stroke)]/20 hover:border-[var(--buddychip-accent)]/50'
                }`}
              >
                <div className="flex items-center mb-2">
                  <MessageSquare className={`h-5 w-5 mr-2 ${
                    selectedMode === 'reply' ? 'text-[var(--buddychip-accent)]' : 'text-[var(--buddychip-grey-text)]'
                  }`} />
                  <span className={`font-medium ${
                    selectedMode === 'reply' ? 'text-[var(--buddychip-accent)]' : 'text-[var(--buddychip-white)]'
                  }`}>
                    Reply Mode
                  </span>
                </div>
                <p className="text-xs text-[var(--buddychip-grey-text)]">
                  Generate thoughtful responses to engage with the tweet
                </p>
              </button>
              
              <button
                type="button"
                onClick={() => setSelectedMode('remake')}
                className={`p-4 rounded-xl border transition-all duration-200 text-left ${
                  selectedMode === 'remake'
                    ? 'border-[var(--buddychip-accent)] bg-[var(--buddychip-accent)]/10'
                    : 'border-[var(--buddychip-grey-stroke)]/50 bg-[var(--buddychip-grey-stroke)]/20 hover:border-[var(--buddychip-accent)]/50'
                }`}
              >
                <div className="flex items-center mb-2">
                  <RefreshCw className={`h-5 w-5 mr-2 ${
                    selectedMode === 'remake' ? 'text-[var(--buddychip-accent)]' : 'text-[var(--buddychip-grey-text)]'
                  }`} />
                  <span className={`font-medium ${
                    selectedMode === 'remake' ? 'text-[var(--buddychip-accent)]' : 'text-[var(--buddychip-white)]'
                  }`}>
                    Remake Mode
                  </span>
                </div>
                <p className="text-xs text-[var(--buddychip-grey-text)]">
                  Rewrite the tweet in different styles and tones
                </p>
              </button>
            </div>
          </div>

          {/* Submit Button */}
          <Button 
            type="submit" 
            disabled={isLoading || !url.trim()}
            className="w-full bg-gradient-to-r from-[var(--buddychip-accent)] to-blue-500 text-[var(--buddychip-white)] hover:from-[var(--buddychip-accent)]/80 hover:to-blue-500/80 font-medium py-3 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Processing Tweet...
              </div>
            ) : (
              `Generate ${selectedMode === 'reply' ? 'Replies' : 'Remakes'}`
            )}
          </Button>
        </form>

        {/* Example URLs */}
        <div className="pt-4 border-t border-[var(--buddychip-grey-stroke)]/30">
          <p className="text-xs text-[var(--buddychip-grey-text)] mb-2">Supported formats:</p>
          <div className="space-y-1 text-xs text-[var(--buddychip-grey-text)]/70">
            <div>• twitter.com/username/status/1234567890</div>
            <div>• x.com/username/status/1234567890</div>
            <div>• twitter.com/i/web/status/1234567890</div>
            <div>• x.com/i/web/status/1234567890</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}