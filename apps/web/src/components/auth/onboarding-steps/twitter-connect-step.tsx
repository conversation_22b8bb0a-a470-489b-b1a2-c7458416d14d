import { <PERSON><PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { Twitter, Shield, Eye, MessageCircle, ArrowLeft, ArrowRight, CheckCircle, AlertCircle } from "lucide-react";
import { useState } from "react";

interface TwitterConnectStepProps {
  onNext: () => void;
  onBack: () => void;
}

export function TwitterConnectStep({ onNext, onBack }: TwitterConnectStepProps) {
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  console.log("🐦 Twitter connect step rendered");

  const handleTwitterConnect = async () => {
    console.log("🔗 Initiating Twitter connection");
    setIsConnecting(true);
    setConnectionError(null);

    try {
      // TODO: Implement real Twitter OAuth
      // const authUrl = await initiateTwitterOAuth();
      // window.location.href = authUrl;

      // Simulate connection process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulate success (in real implementation, this would be handled by OAuth callback)
      setIsConnected(true);
      console.log("✅ Twitter connected successfully");

      // Auto-advance after successful connection
      setTimeout(() => {
        onNext();
      }, 1500);

    } catch (error) {
      console.error("❌ Twitter connection failed:", error);
      setConnectionError("Failed to connect to Twitter. Please try again.");
    } finally {
      setIsConnecting(false);
    }
  };

  const handleSkip = () => {
    console.log("⏭️ User skipped Twitter connection");
    onNext();
  };

  const features = [
    {
      icon: Eye,
      title: "Mention Monitoring",
      description: "Track when people mention you or your brand"
    },
    {
      icon: MessageCircle,
      title: "Smart Replies",
      description: "Get AI-powered suggestions for your responses"
    },
    {
      icon: Shield,
      title: "Privacy Protected",
      description: "We only access what you explicitly allow"
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-8 p-6"
    >
      {/* Header */}
      <div className="text-center space-y-3">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
          className="w-16 h-16 bg-gradient-to-br from-[#1DA1F2] to-[#0d8bd9] rounded-full flex items-center justify-center mx-auto shadow-lg"
        >
          <Twitter className="w-8 h-8 text-white" />
        </motion.div>

        <h2 className="text-2xl font-bold text-[#F5F7FA]">Connect your Twitter</h2>
        <p className="text-[#6E7A8C] text-sm leading-relaxed max-w-md mx-auto">
          Connect your Twitter account to unlock powerful AI-assisted features and mention monitoring.
        </p>
      </div>

      {/* Features showcase */}
      <div className="space-y-4">
        {features.map((feature, index) => (
          <motion.div
            key={feature.title}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 + index * 0.1 }}
            className="flex items-center space-x-4 p-4 bg-[#0E1117] border border-[#202631] rounded-lg"
          >
            <div className="w-10 h-10 bg-[#1DA1F2]/10 rounded-lg flex items-center justify-center">
              <feature.icon className="w-5 h-5 text-[#1DA1F2]" />
            </div>
            <div className="text-left">
              <h3 className="font-semibold text-[#F5F7FA] text-sm">
                {feature.title}
              </h3>
              <p className="text-[#6E7A8C] text-xs">
                {feature.description}
              </p>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Connection status */}
      {connectionError && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center space-x-2 p-3 bg-[#f85149]/10 border border-[#f85149]/20 rounded-lg"
        >
          <AlertCircle className="w-5 h-5 text-[#f85149]" />
          <p className="text-[#f85149] text-sm">{connectionError}</p>
        </motion.div>
      )}

      {isConnected && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex items-center space-x-2 p-3 bg-[#3fb950]/10 border border-[#3fb950]/20 rounded-lg"
        >
          <CheckCircle className="w-5 h-5 text-[#3fb950]" />
          <p className="text-[#3fb950] text-sm">Twitter connected successfully!</p>
        </motion.div>
      )}

      {/* Action buttons */}
      <div className="space-y-3">
        {!isConnected ? (
          <Button
            onClick={handleTwitterConnect}
            disabled={isConnecting}
            className="w-full bg-gradient-to-r from-[#1DA1F2] to-[#0d8bd9] hover:from-[#0d8bd9] hover:to-[#0c7abf] text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
          >
            {isConnecting ? (
              <>
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Connecting to Twitter...
              </>
            ) : (
              <>
                <Twitter className="w-5 h-5 mr-2" />
                Connect Twitter Account
              </>
            )}
          </Button>
        ) : (
          <Button
            onClick={onNext}
            className="w-full bg-gradient-to-r from-[#316FE3] to-[#2563eb] hover:from-[#2563eb] hover:to-[#1d4ed8] text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105"
          >
            Continue
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        )}

        {/* Skip option */}
        <Button
          variant="ghost"
          onClick={handleSkip}
          disabled={isConnecting}
          className="w-full text-[#6E7A8C] hover:text-[#F5F7FA] hover:bg-[#0E1117]"
        >
          Skip for now (you can connect later)
        </Button>

        {/* Back button */}
        <div className="flex gap-3 pt-2">
          <Button
            type="button"
            variant="ghost"
            onClick={onBack}
            disabled={isConnecting}
            className="flex-1 border border-[#202631] hover:border-[#484f58] hover:bg-[#0E1117]"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
        </div>
      </div>

      {/* Privacy note */}
      <div className="bg-[#316FE3]/5 border border-[#316FE3]/20 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Shield className="w-5 h-5 text-[#316FE3] mt-0.5" />
          <div>
            <h4 className="text-[#316FE3] font-semibold text-sm mb-1">
              Your Privacy Matters
            </h4>
            <p className="text-[#6E7A8C] text-xs leading-relaxed">
              We only request read access to your public tweets and mentions. We never post on your behalf without explicit permission.
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
}