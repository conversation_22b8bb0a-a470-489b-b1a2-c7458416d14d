import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { motion } from "framer-motion";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { preferencesSchema, type PreferencesFormData } from "@/lib/auth-schemas";
import { Settings, Bell, Shield, Zap, ArrowLeft, ArrowRight, Mail, Smartphone, MessageSquare } from "lucide-react";

interface PreferencesStepProps {
  onNext: () => void;
  onBack: () => void;
}

export function PreferencesStep({ onNext, onBack }: PreferencesStepProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  console.log("⚙️ Preferences step rendered");

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<PreferencesFormData>({
    resolver: zod<PERSON><PERSON><PERSON>ver(preferencesSchema),
    defaultValues: {
      notifications: {
        email: true,
        push: true,
        sms: false,
        marketing: false,
      },
      privacy: {
        profileVisibility: "public",
        showOnlineStatus: true,
        allowSearchEngineIndexing: true,
      },
      aiPreferences: {
        responseStyle: "casual",
        autoReply: false,
        contentSuggestions: true,
        sentimentAnalysis: true,
      },
      contentPreferences: {
        topics: [],
        languages: ["en"],
        contentTypes: ["text"],
      },
    },
  });

  const notifications = watch("notifications");
  const privacy = watch("privacy");
  const aiPreferences = watch("aiPreferences");

  const onSubmit = async (data: PreferencesFormData) => {
    console.log("💾 Saving preferences:", data);
    setIsSubmitting(true);

    try {
      // TODO: Call mutation to save preferences
      // await saveUserPreferences(data);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log("✅ Preferences saved successfully");
      onNext();
    } catch (error) {
      console.error("❌ Failed to save preferences:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const preferenceCategories = [
    {
      icon: Bell,
      title: "Notifications",
      description: "Choose how you want to be notified",
      items: [
        {
          key: "notifications.email" as const,
          label: "Email notifications",
          description: "Get updates via email",
          icon: Mail,
        },
        {
          key: "notifications.push" as const,
          label: "Push notifications",
          description: "Browser and mobile notifications",
          icon: Smartphone,
        },
        {
          key: "notifications.marketing" as const,
          label: "Marketing emails",
          description: "Product updates and tips",
          icon: MessageSquare,
        },
      ],
    },
    {
      icon: Shield,
      title: "Privacy",
      description: "Control your privacy settings",
      items: [
        {
          key: "privacy.showOnlineStatus" as const,
          label: "Show online status",
          description: "Let others see when you're active",
          icon: Shield,
        },
        {
          key: "privacy.allowSearchEngineIndexing" as const,
          label: "Search engine indexing",
          description: "Allow search engines to find your profile",
          icon: Shield,
        },
      ],
    },
    {
      icon: Zap,
      title: "AI Assistant",
      description: "Customize your AI experience",
      items: [
        {
          key: "aiPreferences.contentSuggestions" as const,
          label: "Content suggestions",
          description: "Get AI-powered content ideas",
          icon: Zap,
        },
        {
          key: "aiPreferences.sentimentAnalysis" as const,
          label: "Sentiment analysis",
          description: "Analyze the tone of mentions",
          icon: Zap,
        },
        {
          key: "aiPreferences.autoReply" as const,
          label: "Auto-reply suggestions",
          description: "Get automatic reply suggestions",
          icon: Zap,
        },
      ],
    },
  ];

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-8 p-6"
    >
      {/* Header */}
      <div className="text-center space-y-3">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
          className="w-16 h-16 bg-gradient-to-br from-[#316FE3] to-[#2563eb] rounded-full flex items-center justify-center mx-auto shadow-lg"
        >
          <Settings className="w-8 h-8 text-white" />
        </motion.div>

        <h2 className="text-2xl font-bold text-[#F5F7FA]">Customize your experience</h2>
        <p className="text-[#6E7A8C] text-sm leading-relaxed max-w-md mx-auto">
          Set up your preferences to get the most out of BuddyChip. You can change these anytime.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Preference Categories */}
        {preferenceCategories.map((category, categoryIndex) => (
          <motion.div
            key={category.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 + categoryIndex * 0.1 }}
            className="bg-[#0E1117] border border-[#202631] rounded-xl p-6"
          >
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-[#316FE3]/10 rounded-lg flex items-center justify-center">
                <category.icon className="w-5 h-5 text-[#316FE3]" />
              </div>
              <div>
                <h3 className="font-semibold text-[#F5F7FA] text-sm">
                  {category.title}
                </h3>
                <p className="text-[#6E7A8C] text-xs">
                  {category.description}
                </p>
              </div>
            </div>

            <div className="space-y-4">
              {category.items.map((item, itemIndex) => (
                <motion.div
                  key={item.key}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 + categoryIndex * 0.1 + itemIndex * 0.05 }}
                  className="flex items-center justify-between p-3 bg-[#161b22] rounded-lg border border-[#202631]/50"
                >
                  <div className="flex items-center space-x-3">
                    <item.icon className="w-4 h-4 text-[#6E7A8C]" />
                    <div>
                      <p className="text-[#F5F7FA] text-sm font-medium">
                        {item.label}
                      </p>
                      <p className="text-[#6E7A8C] text-xs">
                        {item.description}
                      </p>
                    </div>
                  </div>

                  <Switch
                    {...register(item.key)}
                    onCheckedChange={(checked) => setValue(item.key, checked)}
                  />
                </motion.div>
              ))}
            </div>
          </motion.div>
        ))}

        {/* Response Style Selection */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-[#0E1117] border border-[#202631] rounded-xl p-6"
        >
          <h3 className="font-semibold text-[#F5F7FA] text-sm mb-4">
            AI Response Style
          </h3>

          <div className="grid grid-cols-2 gap-3">
            {["professional", "casual", "witty", "formal"].map((style) => (
              <label
                key={style}
                className={`
                  flex items-center justify-center p-3 rounded-lg border cursor-pointer transition-all duration-200
                  ${aiPreferences.responseStyle === style
                    ? "border-[#316FE3] bg-[#316FE3]/10 text-[#316FE3]"
                    : "border-[#202631] bg-[#161b22] text-[#6E7A8C] hover:border-[#484f58]"
                  }
                `}
              >
                <input
                  type="radio"
                  value={style}
                  {...register("aiPreferences.responseStyle")}
                  className="sr-only"
                />
                <span className="text-sm font-medium capitalize">{style}</span>
              </label>
            ))}
          </div>
        </motion.div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <Button
            type="button"
            variant="ghost"
            onClick={onBack}
            disabled={isSubmitting}
            className="flex-1 border border-[#202631] hover:border-[#484f58] hover:bg-[#0E1117]"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>

          <Button
            type="submit"
            disabled={isSubmitting}
            className="flex-1 bg-gradient-to-r from-[#316FE3] to-[#2563eb] hover:from-[#2563eb] hover:to-[#1d4ed8] text-white font-semibold transition-all duration-300 transform hover:scale-105"
          >
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Saving...
              </>
            ) : (
              <>
                Complete Setup
                <ArrowRight className="w-4 h-4 ml-2" />
              </>
            )}
          </Button>
        </div>
      </form>
    </motion.div>
  );
}