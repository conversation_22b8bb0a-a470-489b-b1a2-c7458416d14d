import { useUser, useAuth as useClerkAuth } from "@clerk/clerk-react";
import { useConvexAuth } from "convex/react";
import { useQuery } from "convex/react";
import { api } from "@BuddyChipPro/backend";
import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Card } from "../ui/card";
import { AuthStatusDashboard } from "./auth-status-dashboard";
import { AuthErrorBoundary } from "./auth-error-boundary";
import { AuthTestPanel } from "./auth-test-panel";
import { DEBUG_CONFIG, debugLog, debugTimer } from "../../lib/debug-config";

export function AuthDebugComponent() {
  const { user, isLoaded: clerkLoaded, isSignedIn } = useUser();
  const clerkAuth = useClerkAuth();
  const { isAuthenticated: convexAuth, isLoading: convexLoading } = useConvexAuth();
  const [jwtToken, setJwtToken] = useState<string | null>(null);
  const [tokenClaims, setTokenClaims] = useState<any>(null);
  const [authLogs, setAuthLogs] = useState<string[]>([]);
  
  // Test authenticated query
  const testQuery = useQuery(api.mentions.mentionQueries.getUserMentionStats, {});

  const addLog = (message: string) => {
    // Only log if auth debugging is enabled
    if (!DEBUG_CONFIG.auth.logging) return;
    
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const logMessage = `[${timestamp}] ${message}`;
    setAuthLogs(prev => [logMessage, ...prev.slice(0, 9)]);
    
    // Also log to console for development
    debugLog.debug('AuthDebug', message);
  };

  const getJWTToken = async () => {
    try {
      addLog("🔍 Attempting to get JWT token...");
      const token = await clerkAuth.getToken({ template: "convex" });
      
      if (token) {
        setJwtToken(token);
        addLog(`✅ JWT Token received: ${token.substring(0, 50)}...`);
        
        // Decode JWT claims (basic base64 decode, not secure validation)
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          setTokenClaims(payload);
          addLog(`📋 Token claims: ${JSON.stringify(payload, null, 2)}`);
        } catch (e) {
          addLog(`❌ Failed to decode token: ${e}`);
        }
      } else {
        addLog("❌ No JWT token received");
        setJwtToken(null);
      }
    } catch (error) {
      addLog(`❌ JWT Token Error: ${error}`);
      setJwtToken(null);
    }
  };

  const testConvexAuth = async () => {
    addLog("🧪 Testing Convex authentication...");
    if (testQuery) {
      addLog(`✅ Convex query result: ${JSON.stringify(testQuery)}`);
    } else {
      addLog("❌ Convex query failed or returned null");
    }
  };

  useEffect(() => {
    if (clerkLoaded) {
      addLog(`👤 Clerk loaded: ${isSignedIn ? 'Signed In' : 'Not Signed In'}`);
    }
    if (!convexLoading) {
      addLog(`🔗 Convex auth: ${convexAuth ? 'Authenticated' : 'Not Authenticated'}`);
    }
  }, [clerkLoaded, isSignedIn, convexAuth, convexLoading]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    addLog("📋 Copied to clipboard");
  };

  return (
    <AuthErrorBoundary>
      <div className="space-y-4">
        <AuthStatusDashboard showDetails={true} />
        <AuthTestPanel />
        
        <Card className="p-6 space-y-4 bg-[#000000]/40 border-[#202631]">
          <h2 className="text-xl font-semibold text-[#F5F7FA]">🔍 Authentication Debug Center</h2>
      
      {/* Auth Status */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <h3 className="font-medium text-[#F5F7FA]">Clerk Status</h3>
          <div className="text-sm space-y-1">
            <div className={`${clerkLoaded ? 'text-green-400' : 'text-yellow-400'}`}>
              Loaded: {clerkLoaded ? '✅' : '⏳'}
            </div>
            <div className={`${isSignedIn ? 'text-green-400' : 'text-red-400'}`}>
              Signed In: {isSignedIn ? '✅' : '❌'}
            </div>
            <div className="text-gray-300">
              User ID: {user?.id ? user.id.substring(0, 20) + '...' : 'None'}
            </div>
          </div>
        </div>
        
        <div className="space-y-2">
          <h3 className="font-medium text-[#F5F7FA]">Convex Status</h3>
          <div className="text-sm space-y-1">
            <div className={`${!convexLoading ? 'text-green-400' : 'text-yellow-400'}`}>
              Loaded: {!convexLoading ? '✅' : '⏳'}
            </div>
            <div className={`${convexAuth ? 'text-green-400' : 'text-red-400'}`}>
              Authenticated: {convexAuth ? '✅' : '❌'}
            </div>
            <div className="text-gray-300">
              Query Result: {testQuery ? 'Success' : 'Failed'}
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2 flex-wrap">
        <Button 
          onClick={getJWTToken} 
          disabled={!isSignedIn}
          className="bg-[#316FE3] hover:bg-[#2563eb]"
        >
          Get JWT Token
        </Button>
        <Button 
          onClick={testConvexAuth}
          className="bg-[#059669] hover:bg-[#047857]"
        >
          Test Convex Auth
        </Button>
        <Button 
          onClick={() => setAuthLogs([])}
          variant="outline"
          className="border-[#202631] text-[#F5F7FA]"
        >
          Clear Logs
        </Button>
      </div>

      {/* JWT Token Display */}
      {jwtToken && (
        <div className="space-y-2">
          <h4 className="font-medium text-[#F5F7FA]">JWT Token</h4>
          <div className="bg-[#202631] p-3 rounded border text-xs font-mono">
            <div className="flex justify-between items-start mb-2">
              <span className="text-green-400">Raw Token:</span>
              <Button 
                size="sm" 
                variant="ghost" 
                onClick={() => copyToClipboard(jwtToken)}
              >
                Copy
              </Button>
            </div>
            <div className="text-gray-300 break-all">{jwtToken}</div>
          </div>
          
          {tokenClaims && (
            <div className="bg-[#202631] p-3 rounded border">
              <div className="flex justify-between items-start mb-2">
                <span className="text-blue-400 text-sm">Token Claims:</span>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  onClick={() => copyToClipboard(JSON.stringify(tokenClaims, null, 2))}
                >
                  Copy
                </Button>
              </div>
              <pre className="text-xs text-gray-300 whitespace-pre-wrap">
                {JSON.stringify(tokenClaims, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}

      {/* Debug Logs */}
      <div className="space-y-2">
        <h4 className="font-medium text-[#F5F7FA]">Debug Logs</h4>
        <div className="bg-[#202631] p-3 rounded border h-48 overflow-y-auto">
          {authLogs.length === 0 ? (
            <div className="text-gray-500 text-sm">No logs yet...</div>
          ) : (
            <div className="space-y-1">
              {authLogs.map((log, index) => (
                <div key={index} className="text-xs font-mono text-gray-300">
                  {log}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Environment Info */}
      <div className="text-xs text-gray-400 border-t border-[#202631] pt-3">
        <div>Clerk Domain: ethical-redbird-87.clerk.accounts.dev</div>
        <div>Convex URL: {import.meta.env.VITE_CONVEX_URL}</div>
        <div>Publishable Key: {import.meta.env.VITE_CLERK_PUBLISHABLE_KEY?.substring(0, 30)}...</div>
      </div>
    </Card>
      </div>
    </AuthErrorBoundary>
  );
}