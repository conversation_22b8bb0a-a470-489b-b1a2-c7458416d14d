import { motion } from "framer-motion";
import { Check, Circle } from "lucide-react";
import { cn } from "@/lib/utils";

interface ProgressIndicatorProps {
  current: number;
  total: number;
  variant?: "bar" | "steps" | "dots";
  showLabels?: boolean;
  labels?: string[];
  className?: string;
}

/**
 * Enhanced ProgressIndicator – shows progress for multi-step flows with multiple variants.
 */
export function ProgressIndicator({
  current,
  total,
  variant = "bar",
  showLabels = false,
  labels = [],
  className
}: ProgressIndicatorProps) {
  const percent = Math.max(0, Math.min(100, (current / total) * 100));

  console.log(`📊 Progress indicator - Current: ${current}/${total} (${percent.toFixed(1)}%), Variant: ${variant}`);

  if (variant === "steps") {
    return <StepsProgress current={current} total={total} labels={labels} showLabels={showLabels} className={className} />;
  }

  if (variant === "dots") {
    return <DotsProgress current={current} total={total} className={className} />;
  }

  // Default bar variant
  return (
    <div className={cn("w-full space-y-2", className)}>
      {showLabels && (
        <div className="flex justify-between text-sm text-[#6E7A8C]">
          <span>Step {current} of {total}</span>
          <span>{Math.round(percent)}% complete</span>
        </div>
      )}

      <div className="relative w-full h-3 bg-[#202631] rounded-full overflow-hidden shadow-inner">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#202631] to-[#2d3748]" />

        {/* Progress bar with gradient */}
        <motion.div
          className="h-full bg-gradient-to-r from-[#316FE3] to-[#2563eb] relative overflow-hidden"
          initial={{ width: 0 }}
          animate={{ width: `${percent}%` }}
          transition={{ ease: "easeOut", duration: 0.6 }}
        >
          {/* Animated shine effect */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
            initial={{ x: "-100%" }}
            animate={{ x: "100%" }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              repeatDelay: 2,
              ease: "easeInOut"
            }}
          />
        </motion.div>

        {/* Completion indicator */}
        {percent === 100 && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
            className="absolute right-1 top-1/2 transform -translate-y-1/2"
          >
            <Check className="w-4 h-4 text-white" />
          </motion.div>
        )}
      </div>
    </div>
  );
}

function StepsProgress({
  current,
  total,
  labels,
  showLabels,
  className
}: {
  current: number;
  total: number;
  labels: string[];
  showLabels: boolean;
  className?: string;
}) {
  const steps = Array.from({ length: total }, (_, i) => i + 1);

  return (
    <div className={cn("w-full", className)}>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const isCompleted = step < current;
          const isCurrent = step === current;
          const isUpcoming = step > current;

          return (
            <div key={step} className="flex items-center">
              {/* Step circle */}
              <motion.div
                className={cn(
                  "relative flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300",
                  isCompleted && "bg-[#3fb950] border-[#3fb950] text-white",
                  isCurrent && "bg-[#316FE3] border-[#316FE3] text-white shadow-lg shadow-[#316FE3]/25",
                  isUpcoming && "bg-[#202631] border-[#484f58] text-[#6E7A8C]"
                )}
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: index * 0.1 }}
              >
                {isCompleted ? (
                  <Check className="w-5 h-5" />
                ) : (
                  <span className="text-sm font-semibold">{step}</span>
                )}

                {/* Current step pulse effect */}
                {isCurrent && (
                  <motion.div
                    className="absolute inset-0 rounded-full border-2 border-[#316FE3]"
                    animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0, 0.5] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                )}
              </motion.div>

              {/* Step label */}
              {showLabels && labels[index] && (
                <div className="absolute top-12 left-1/2 transform -translate-x-1/2 text-xs text-center">
                  <span className={cn(
                    "font-medium",
                    isCompleted && "text-[#3fb950]",
                    isCurrent && "text-[#316FE3]",
                    isUpcoming && "text-[#6E7A8C]"
                  )}>
                    {labels[index]}
                  </span>
                </div>
              )}

              {/* Connector line */}
              {index < steps.length - 1 && (
                <motion.div
                  className={cn(
                    "flex-1 h-0.5 mx-2 transition-all duration-500",
                    step < current ? "bg-[#3fb950]" : "bg-[#202631]"
                  )}
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: 1 }}
                  transition={{ delay: index * 0.1 + 0.2 }}
                />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}

function DotsProgress({
  current,
  total,
  className
}: {
  current: number;
  total: number;
  className?: string;
}) {
  const dots = Array.from({ length: total }, (_, i) => i + 1);

  return (
    <div className={cn("flex items-center justify-center space-x-2", className)}>
      {dots.map((dot, index) => {
        const isActive = dot <= current;

        return (
          <motion.div
            key={dot}
            className={cn(
              "w-2 h-2 rounded-full transition-all duration-300",
              isActive ? "bg-[#316FE3] scale-125" : "bg-[#202631]"
            )}
            initial={{ scale: 0 }}
            animate={{ scale: isActive ? 1.25 : 1 }}
            transition={{ delay: index * 0.1 }}
          />
        );
      })}
    </div>
  );
}