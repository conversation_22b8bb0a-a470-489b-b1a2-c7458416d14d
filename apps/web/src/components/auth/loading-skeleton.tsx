import React from "react";
import { cn } from "@/lib/utils";

interface LoadingSkeletonProps {
  className?: string;
  variant?: "default" | "circular" | "rectangular" | "text";
  width?: string | number;
  height?: string | number;
  animation?: "pulse" | "wave" | "none";
}

export function LoadingSkeleton({
  className,
  variant = "default",
  width,
  height,
  animation = "pulse",
}: LoadingSkeletonProps) {
  console.log(`💀 Loading skeleton rendered - Variant: ${variant}, Animation: ${animation}`);

  const getVariantClasses = () => {
    switch (variant) {
      case "circular":
        return "rounded-full";
      case "rectangular":
        return "rounded-md";
      case "text":
        return "rounded h-4";
      default:
        return "rounded-lg";
    }
  };

  const getAnimationClasses = () => {
    switch (animation) {
      case "wave":
        return "animate-wave";
      case "pulse":
        return "animate-pulse";
      default:
        return "";
    }
  };

  const style = {
    width: width || (variant === "text" ? "100%" : "auto"),
    height: height || (variant === "circular" ? "40px" : variant === "text" ? "16px" : "auto"),
  };

  return (
    <div
      className={cn(
        "bg-[#202631] bg-gradient-to-r from-[#202631] via-[#2d3748] to-[#202631]",
        getVariantClasses(),
        getAnimationClasses(),
        className
      )}
      style={style}
    />
  );
}

// Predefined skeleton components for common use cases
export function AuthFormSkeleton() {
  console.log("🔐 Auth form skeleton rendered");
  
  return (
    <div className="space-y-6 p-6">
      {/* Header skeleton */}
      <div className="text-center space-y-3">
        <LoadingSkeleton variant="text" height={32} width="60%" className="mx-auto" />
        <LoadingSkeleton variant="text" height={16} width="80%" className="mx-auto" />
      </div>
      
      {/* Form fields skeleton */}
      <div className="space-y-4">
        <div className="space-y-2">
          <LoadingSkeleton variant="text" height={16} width="25%" />
          <LoadingSkeleton variant="rectangular" height={40} />
        </div>
        <div className="space-y-2">
          <LoadingSkeleton variant="text" height={16} width="30%" />
          <LoadingSkeleton variant="rectangular" height={40} />
        </div>
      </div>
      
      {/* Button skeleton */}
      <LoadingSkeleton variant="rectangular" height={44} />
      
      {/* Divider skeleton */}
      <div className="flex items-center space-x-4">
        <LoadingSkeleton variant="rectangular" height={1} className="flex-1" />
        <LoadingSkeleton variant="text" height={16} width="20%" />
        <LoadingSkeleton variant="rectangular" height={1} className="flex-1" />
      </div>
      
      {/* Social buttons skeleton */}
      <div className="space-y-3">
        <LoadingSkeleton variant="rectangular" height={44} />
        <LoadingSkeleton variant="rectangular" height={44} />
      </div>
    </div>
  );
}

export function OnboardingStepSkeleton() {
  console.log("🚀 Onboarding step skeleton rendered");
  
  return (
    <div className="space-y-6 p-6">
      {/* Progress bar skeleton */}
      <LoadingSkeleton variant="rectangular" height={8} />
      
      {/* Step content skeleton */}
      <div className="space-y-4">
        <div className="text-center space-y-3">
          <LoadingSkeleton variant="text" height={28} width="70%" className="mx-auto" />
          <LoadingSkeleton variant="text" height={16} width="90%" className="mx-auto" />
          <LoadingSkeleton variant="text" height={16} width="60%" className="mx-auto" />
        </div>
        
        {/* Form elements skeleton */}
        <div className="space-y-4 mt-8">
          <div className="space-y-2">
            <LoadingSkeleton variant="text" height={16} width="30%" />
            <LoadingSkeleton variant="rectangular" height={40} />
          </div>
          <div className="space-y-2">
            <LoadingSkeleton variant="text" height={16} width="25%" />
            <LoadingSkeleton variant="rectangular" height={40} />
          </div>
        </div>
        
        {/* Action buttons skeleton */}
        <div className="flex gap-3 mt-8">
          <LoadingSkeleton variant="rectangular" height={44} className="flex-1" />
          <LoadingSkeleton variant="rectangular" height={44} className="flex-1" />
        </div>
      </div>
    </div>
  );
}

export function ProfileCardSkeleton() {
  console.log("👤 Profile card skeleton rendered");
  
  return (
    <div className="flex items-center space-x-4 p-4">
      <LoadingSkeleton variant="circular" width={48} height={48} />
      <div className="space-y-2 flex-1">
        <LoadingSkeleton variant="text" height={16} width="60%" />
        <LoadingSkeleton variant="text" height={14} width="40%" />
      </div>
    </div>
  );
}

// Custom wave animation keyframes (add to your CSS)
export const waveAnimationCSS = `
@keyframes wave {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-wave {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200px 100%;
  animation: wave 1.5s infinite;
}
`;
