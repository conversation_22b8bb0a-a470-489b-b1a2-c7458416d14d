import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Eye, EyeOff, Check, X, AlertCircle, Info } from "lucide-react";
import { cn } from "@/lib/utils";

interface EnhancedFormFieldProps {
  id: string;
  label: string;
  type?: "text" | "email" | "password" | "tel";
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
  success?: string;
  hint?: string;
  required?: boolean;
  disabled?: boolean;
  autoComplete?: string;
  validation?: {
    pattern?: RegExp;
    minLength?: number;
    maxLength?: number;
    customValidator?: (value: string) => string | null;
  };
  showPasswordToggle?: boolean;
  className?: string;
}

export function EnhancedFormField({
  id,
  label,
  type = "text",
  placeholder,
  value,
  onChange,
  error,
  success,
  hint,
  required = false,
  disabled = false,
  autoComplete,
  validation,
  showPasswordToggle = false,
  className,
}: EnhancedFormFieldProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [hasBeenTouched, setHasBeenTouched] = useState(false);

  console.log(`🔍 Form field ${id} - Value: ${value}, Error: ${error}, Success: ${success}`);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    
    if (!hasBeenTouched) {
      setHasBeenTouched(true);
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
    setHasBeenTouched(true);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  // Determine validation state
  const getValidationState = () => {
    if (!hasBeenTouched && !error) return "default";
    if (error) return "error";
    if (success) return "success";
    if (value && !error) return "valid";
    return "default";
  };

  const validationState = getValidationState();
  const inputType = type === "password" && showPasswordToggle 
    ? (showPassword ? "text" : "password") 
    : type;

  const getValidationIcon = () => {
    switch (validationState) {
      case "success":
      case "valid":
        return <Check className="h-4 w-4 text-[#3fb950]" />;
      case "error":
        return <X className="h-4 w-4 text-[#f85149]" />;
      default:
        return null;
    }
  };

  const getValidationMessage = () => {
    if (error) {
      return (
        <div className="flex items-center mt-2 text-[#f85149] text-sm">
          <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
          {error}
        </div>
      );
    }
    
    if (success) {
      return (
        <div className="flex items-center mt-2 text-[#3fb950] text-sm">
          <Check className="h-4 w-4 mr-2 flex-shrink-0" />
          {success}
        </div>
      );
    }
    
    if (hint) {
      return (
        <div className="flex items-center mt-2 text-[#6E7A8C] text-sm">
          <Info className="h-4 w-4 mr-2 flex-shrink-0" />
          {hint}
        </div>
      );
    }
    
    return null;
  };

  return (
    <div className={cn("space-y-2", className)}>
      <Label 
        htmlFor={id} 
        className={cn(
          "text-[#F5F7FA] font-medium text-sm transition-colors duration-200",
          isFocused && "text-[#316FE3]",
          error && "text-[#f85149]",
          success && "text-[#3fb950]"
        )}
      >
        {label}
        {required && <span className="text-[#f85149] ml-1">*</span>}
      </Label>
      
      <div className="relative">
        <Input
          id={id}
          type={inputType}
          placeholder={placeholder}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={disabled}
          autoComplete={autoComplete}
          className={cn(
            "bg-[#0E1117] border-[#202631] text-[#F5F7FA] placeholder:text-[#6E7A8C]",
            "transition-all duration-200 ease-out",
            "focus:border-[#316FE3] focus:ring-2 focus:ring-[#316FE3]/20",
            "hover:border-[#484f58] hover:bg-[#161b22]",
            "focus:bg-[#161b22] focus:shadow-lg",
            showPasswordToggle && "pr-12",
            (validationState === "error") && "border-[#f85149] focus:border-[#f85149] focus:ring-[#f85149]/20",
            (validationState === "success" || validationState === "valid") && "border-[#3fb950] focus:border-[#3fb950] focus:ring-[#3fb950]/20",
            disabled && "opacity-50 cursor-not-allowed"
          )}
        />
        
        {/* Validation icon */}
        {getValidationIcon() && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {getValidationIcon()}
          </div>
        )}
        
        {/* Password toggle button */}
        {showPasswordToggle && type === "password" && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 text-[#6E7A8C] hover:text-[#F5F7FA] hover:bg-[#202631]"
            onClick={() => setShowPassword(!showPassword)}
            disabled={disabled}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </Button>
        )}
      </div>
      
      {/* Validation message */}
      {getValidationMessage()}
    </div>
  );
}
