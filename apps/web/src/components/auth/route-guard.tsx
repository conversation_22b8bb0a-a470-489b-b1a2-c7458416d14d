/**
 * Frontend Route Authentication Guards
 * 🔐 SECURITY: Prevents unauthorized access to protected routes
 */

import React, { useEffect } from 'react';
import { Navigate, useLocation } from '@tanstack/react-router';
import { useAuth } from './use-auth';
import Loader from '@/components/loader';

interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
  requireAuth?: boolean;
}

/**
 * Main route protection component
 */
export function ProtectedRoute({ 
  children, 
  fallback,
  redirectTo = '/',
  requireAuth = true 
}: ProtectedRouteProps) {
  const { isLoading, isAuthenticated, user } = useAuth();
  const location = useLocation();

  // Show loader while authentication state is loading
  if (isLoading) {
    return fallback || <AuthLoadingScreen />;
  }

  // If authentication is required but user is not signed in
  if (requireAuth && !isAuthenticated) {
    // Store the attempted URL for redirect after login
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('redirectAfterAuth', location.pathname + location.search);
    }
    
    return <Navigate to={redirectTo} replace />;
  }

  // If user should not be authenticated (like login page) but is signed in
  if (!requireAuth && isAuthenticated) {
    const redirectPath = getPostAuthRedirect();
    return <Navigate to={redirectPath} replace />;
  }

  // Render children if authentication requirements are met
  return <>{children}</>;
}

/**
 * Higher-order component for protecting routes
 */
export function withAuthGuard<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: Partial<ProtectedRouteProps> = {}
) {
  return function AuthGuardedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <WrappedComponent {...props} />
      </ProtectedRoute>
    );
  };
}

/**
 * Hook for checking authentication status in components
 */
export function useAuthGuard(redirectOnFailure = true) {
  const { isLoading, isAuthenticated, user } = useAuth();
  const location = useLocation();

  useEffect(() => {
    if (!isLoading && !isAuthenticated && redirectOnFailure) {
      // Store current location for redirect after auth
      if (typeof window !== 'undefined') {
        sessionStorage.setItem('redirectAfterAuth', location.pathname + location.search);
      }
    }
  }, [isLoading, isAuthenticated, location.pathname, location.search, redirectOnFailure]);

  return {
    isAuthenticated,
    isLoading,
    user,
    requiresAuth: isLoading || !isAuthenticated,
  };
}

/**
 * Component for routes that require specific user roles or permissions
 */
interface RoleGuardProps extends ProtectedRouteProps {
  requiredRole?: string;
  requiredPermissions?: string[];
  checkUserRole?: (user: any) => boolean;
}

export function RoleGuard({
  children,
  requiredRole,
  requiredPermissions,
  checkUserRole,
  ...protectedRouteProps
}: RoleGuardProps) {
  const { user } = useAuth();

  return (
    <ProtectedRoute {...protectedRouteProps}>
      <RoleCheckComponent
        user={user}
        requiredRole={requiredRole}
        requiredPermissions={requiredPermissions}
        checkUserRole={checkUserRole}
      >
        {children}
      </RoleCheckComponent>
    </ProtectedRoute>
  );
}

/**
 * Internal component for role checking
 */
function RoleCheckComponent({
  children,
  user,
  requiredRole,
  requiredPermissions,
  checkUserRole,
}: {
  children: React.ReactNode;
  user: any;
  requiredRole?: string;
  requiredPermissions?: string[];
  checkUserRole?: (user: any) => boolean;
}) {
  // Custom role check function
  if (checkUserRole && !checkUserRole(user)) {
    return <UnauthorizedAccess />;
  }

  // Check required role
  if (requiredRole && !hasRole(user, requiredRole)) {
    return <UnauthorizedAccess />;
  }

  // Check required permissions
  if (requiredPermissions && !hasPermissions(user, requiredPermissions)) {
    return <UnauthorizedAccess />;
  }

  return <>{children}</>;
}

/**
 * Loading screen component for authentication state
 */
function AuthLoadingScreen() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--buddychip-light-bg)] via-slate-900 to-[var(--buddychip-black)] flex items-center justify-center">
      <div className="text-center">
        <Loader />
        <p className="text-[var(--buddychip-white)] mt-4">Checking authentication...</p>
      </div>
    </div>
  );
}

/**
 * Unauthorized access component
 */
function UnauthorizedAccess() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--buddychip-light-bg)] via-slate-900 to-[var(--buddychip-black)] flex items-center justify-center">
      <div className="text-center max-w-md mx-auto p-6">
        <div className="text-6xl mb-4">🔒</div>
        <h1 className="text-2xl font-bold text-[var(--buddychip-white)] mb-2">
          Access Denied
        </h1>
        <p className="text-[var(--buddychip-grey-text)] mb-6">
          You don't have permission to access this page. Please contact an administrator if you believe this is an error.
        </p>
        <button
          onClick={() => window.history.back()}
          className="bg-[var(--buddychip-red)] text-white px-6 py-2 rounded-lg hover:bg-opacity-80 transition-colors"
        >
          Go Back
        </button>
      </div>
    </div>
  );
}

/**
 * Utility functions for role and permission checking
 */
function hasRole(user: any, role: string): boolean {
  // Implement based on your role system
  // This is a placeholder implementation
  return user?.publicMetadata?.role === role || user?.organizationMemberships?.some((m: any) => m.role === role);
}

function hasPermissions(user: any, permissions: string[]): boolean {
  // Implement based on your permission system
  // This is a placeholder implementation
  const userPermissions = user?.publicMetadata?.permissions || [];
  return permissions.every(permission => userPermissions.includes(permission));
}

/**
 * Get redirect path after authentication
 */
function getPostAuthRedirect(): string {
  if (typeof window === 'undefined') return '/dashboard';
  
  const stored = sessionStorage.getItem('redirectAfterAuth');
  if (stored) {
    sessionStorage.removeItem('redirectAfterAuth');
    return stored;
  }
  
  return '/dashboard';
}

/**
 * Clear stored redirect path
 */
export function clearAuthRedirect(): void {
  if (typeof window !== 'undefined') {
    sessionStorage.removeItem('redirectAfterAuth');
  }
}

/**
 * Store redirect path manually
 */
export function setAuthRedirect(path: string): void {
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('redirectAfterAuth', path);
  }
}

/**
 * Pre-built guard components for common use cases
 */
export const AuthGuards = {
  /**
   * Dashboard access guard
   */
  Dashboard: ({ children }: { children: React.ReactNode }) => (
    <ProtectedRoute redirectTo="/">
      {children}
    </ProtectedRoute>
  ),

  /**
   * Admin access guard
   */
  Admin: ({ children }: { children: React.ReactNode }) => (
    <RoleGuard 
      requiredRole="admin"
      redirectTo="/dashboard"
    >
      {children}
    </RoleGuard>
  ),

  /**
   * Premium features guard
   */
  Premium: ({ children }: { children: React.ReactNode }) => (
    <RoleGuard 
      checkUserRole={(user) => user?.publicMetadata?.plan === 'premium' || user?.publicMetadata?.plan === 'pro'}
      redirectTo="/dashboard"
    >
      {children}
    </RoleGuard>
  ),

  /**
   * Public route guard (redirects authenticated users)
   */
  Public: ({ children }: { children: React.ReactNode }) => (
    <ProtectedRoute 
      requireAuth={false}
      redirectTo="/dashboard"
    >
      {children}
    </ProtectedRoute>
  ),
};