import { useAuth as useClerkAuth } from "@clerk/clerk-react";
import { ConvexProviderWithClerk } from "convex/react-clerk";
import { ConvexReactClient } from "convex/react";
import { ReactNode, useEffect } from "react";

// Enhanced auth adapter with debugging and error handling
const useAuth = () => {
  const clerkAuth = useClerkAuth();
  
  // Debug JWT token issues
  useEffect(() => {
    if (clerkAuth.isLoaded && clerkAuth.isSignedIn) {
      // Test JWT token generation on auth state change
      clerkAuth.getToken({ template: "convex" })
        .then(token => {
          if (token) {
            console.log("✅ JWT Token successfully generated:", token.substring(0, 50) + "...");
          } else {
            console.error("❌ JWT Token generation returned null");
          }
        })
        .catch(error => {
          console.error("❌ JWT Token generation failed:", error);
        });
    }
  }, [clerkAuth.isLoaded, clerkAuth.isSignedIn]);
  
  return {
    isLoaded: clerkAuth.isLoaded,
    isSignedIn: clerkAuth.isSignedIn,
    getToken: async (options?: { template?: "convex"; skipCache?: boolean }) => {
      try {
        console.log("🔍 Requesting JWT token with options:", options);
        const token = await clerkAuth.getToken(options);
        
        if (token) {
          console.log("✅ JWT Token received successfully");
          // Decode JWT for debugging (client-side only)
          try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            console.log("📋 JWT Token payload:", {
              issuer: payload.iss,
              subject: payload.sub,
              audience: payload.aud,
              expiresAt: new Date(payload.exp * 1000).toISOString(),
              issuedAt: new Date(payload.iat * 1000).toISOString(),
            });
          } catch (e) {
            console.warn("⚠️ Could not decode JWT payload:", e);
          }
        } else {
          console.error("❌ JWT Token is null - authentication may have failed");
        }
        
        return token;
      } catch (error) {
        console.error("❌ JWT Token request failed:", error);
        throw error;
      }
    },
    orgId: clerkAuth.orgId,
    orgRole: clerkAuth.orgRole,
  };
};

interface Props {
  children: ReactNode;
  client: ConvexReactClient;
}

export function ClerkConvexWrapper({ children, client }: Props) {
  return (
    <ConvexProviderWithClerk client={client} useAuth={useAuth}>
      {children}
    </ConvexProviderWithClerk>
  );
}