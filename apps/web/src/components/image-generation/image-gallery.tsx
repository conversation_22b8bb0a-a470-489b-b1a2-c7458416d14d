import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { 
  Search, 
  Filter,
  Download, 
  Copy, 
  Heart,
  ExternalLink,
  Calendar,
  Tag,
  TrendingUp
} from "lucide-react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@BuddyChipPro/backend";
import { toast } from "sonner";

export interface ImageGalleryProps {
  userId?: string;
  className?: string;
}

interface ImageData {
  _id: string;
  imageUrl: string;
  originalPrompt: string;
  revisedPrompt?: string;
  model: string;
  style?: string;
  platform?: string;
  size?: string;
  tags: string[];
  customName?: string;
  isPublic: boolean;
  downloadCount: number;
  createdAt: number;
  updatedAt: number;
}

export function ImageGallery({ userId, className }: ImageGalleryProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedPlatform, setSelectedPlatform] = useState<string>("all");
  const [selectedStyle, setSelectedStyle] = useState<string>("all");
  const [viewMode, setViewMode] = useState<"recent" | "popular">("recent");

  // Fetch user images
  const userImages = useQuery(api.storage.imageStorage.getUserImages, { 
    userId: userId as any,
    limit: 50,
    platform: selectedPlatform !== "all" ? selectedPlatform : undefined,
    style: selectedStyle !== "all" ? selectedStyle : undefined,
  });

  // Fetch popular images
  const popularImages = useQuery(api.storage.imageStorage.getPopularImages, {
    timeframe: "week",
    platform: selectedPlatform !== "all" ? selectedPlatform : undefined,
    limit: 20,
  });

  // Search images
  const searchResults = useQuery(api.storage.imageStorage.searchImages, 
    searchTerm.trim() ? {
      searchTerm: searchTerm.trim(),
      userId: userId as any,
      platform: selectedPlatform !== "all" ? selectedPlatform : undefined,
      style: selectedStyle !== "all" ? selectedStyle : undefined,
      limit: 30,
    } : "skip"
  );

  const trackImageUsage = useMutation(api.storage.imageStorage.trackImageUsage);
  const updateImageMetadata = useMutation(api.storage.imageStorage.updateImageMetadata);

  const displayImages = searchTerm.trim() 
    ? searchResults 
    : viewMode === "popular" 
      ? popularImages 
      : userImages;

  const handleCopyImageUrl = (imageUrl: string, imageId: string) => {
    navigator.clipboard.writeText(imageUrl);
    trackImageUsage({ imageId: imageId as any });
    toast.success("Image URL copied to clipboard!");
  };

  const handleDownloadImage = async (imageUrl: string, imageId: string, prompt: string) => {
    try {
      trackImageUsage({ imageId: imageId as any });
      
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `generated-image-${imageId}-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success("Image downloaded!");
    } catch (error) {
      console.error('Download failed:', error);
      toast.error("Failed to download image");
    }
  };

  const handleToggleFavorite = async (imageId: string, currentFavorite: boolean) => {
    try {
      await updateImageMetadata({
        imageId: imageId as any,
        // Note: this would need to be added to the schema and mutation
        // isFavorite: !currentFavorite,
      });
      toast.success(currentFavorite ? "Removed from favorites" : "Added to favorites");
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
      toast.error("Failed to update favorite status");
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <div className={className}>
      <Card className="bg-[var(--buddychip-black)]/80 backdrop-blur-sm border border-[var(--buddychip-grey-stroke)]/50">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-[var(--buddychip-white)] flex items-center">
            <Tag className="h-5 w-5 mr-3 text-[var(--buddychip-accent)]" />
            Image Gallery
          </CardTitle>
          
          {/* Search and Filters */}
          <div className="space-y-4">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--buddychip-grey-text)]" />
              <Input
                placeholder="Search images by prompt or tags..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-[var(--buddychip-grey-stroke)]/50 border-[var(--buddychip-grey-stroke)]/50 text-[var(--buddychip-white)] focus:border-[var(--buddychip-accent)]"
              />
            </div>

            {/* Filters */}
            <div className="flex flex-wrap gap-4">
              {/* View Mode */}
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant={viewMode === "recent" ? "default" : "ghost"}
                  onClick={() => setViewMode("recent")}
                  className={viewMode === "recent" ? "bg-[var(--buddychip-accent)]" : "border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]"}
                >
                  <Calendar className="h-4 w-4 mr-1" />
                  Recent
                </Button>
                <Button
                  size="sm"
                  variant={viewMode === "popular" ? "default" : "ghost"}
                  onClick={() => setViewMode("popular")}
                  className={viewMode === "popular" ? "bg-[var(--buddychip-accent)]" : "border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]"}
                >
                  <TrendingUp className="h-4 w-4 mr-1" />
                  Popular
                </Button>
              </div>

              {/* Platform Filter */}
              <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
                <SelectTrigger className="w-[140px] bg-[var(--buddychip-grey-stroke)]/50 border-[var(--buddychip-grey-stroke)]/50 text-[var(--buddychip-white)]">
                  <SelectValue placeholder="Platform" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Platforms</SelectItem>
                  <SelectItem value="twitter">Twitter</SelectItem>
                  <SelectItem value="instagram">Instagram</SelectItem>
                  <SelectItem value="linkedin">LinkedIn</SelectItem>
                </SelectContent>
              </Select>

              {/* Style Filter */}
              <Select value={selectedStyle} onValueChange={setSelectedStyle}>
                <SelectTrigger className="w-[140px] bg-[var(--buddychip-grey-stroke)]/50 border-[var(--buddychip-grey-stroke)]/50 text-[var(--buddychip-white)]">
                  <SelectValue placeholder="Style" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Styles</SelectItem>
                  <SelectItem value="minimal">Minimal</SelectItem>
                  <SelectItem value="vibrant">Vibrant</SelectItem>
                  <SelectItem value="professional">Professional</SelectItem>
                  <SelectItem value="artistic">Artistic</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {!displayImages ? (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--buddychip-accent)]"></div>
            </div>
          ) : displayImages.length === 0 ? (
            <div className="text-center py-12">
              <Tag className="h-12 w-12 text-[var(--buddychip-grey-text)] mx-auto mb-4" />
              <h3 className="text-lg font-medium text-[var(--buddychip-white)] mb-2">
                {searchTerm ? "No images found" : "No images yet"}
              </h3>
              <p className="text-sm text-[var(--buddychip-grey-text)]">
                {searchTerm 
                  ? "Try adjusting your search terms or filters"
                  : "Generate your first image to see it appear here"
                }
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {displayImages.map((image: ImageData) => (
                <Card key={image._id} className="bg-[var(--buddychip-grey-stroke)]/20 border border-[var(--buddychip-grey-stroke)]/30 overflow-hidden hover:border-[var(--buddychip-accent)]/50 transition-colors">
                  {/* Image */}
                  <div className="aspect-square relative overflow-hidden">
                    <img 
                      src={image.imageUrl}
                      alt={image.originalPrompt}
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                    />
                    
                    {/* Overlay with actions */}
                    <div className="absolute inset-0 bg-black/60 opacity-0 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => handleCopyImageUrl(image.imageUrl, image._id)}
                          className="h-8 w-8 p-0 bg-secondary/20 hover:bg-secondary/30"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => handleDownloadImage(image.imageUrl, image._id, image.originalPrompt)}
                          className="h-8 w-8 p-0 bg-secondary/20 hover:bg-secondary/30"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => window.open(image.imageUrl, '_blank')}
                          className="h-8 w-8 p-0 bg-secondary/20 hover:bg-secondary/30"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Badges */}
                    <div className="absolute top-2 left-2 flex flex-wrap gap-1">
                      {image.platform && (
                        <Badge variant="secondary" className="bg-[var(--buddychip-black)]/80 text-[var(--buddychip-white)] text-xs">
                          {image.platform}
                        </Badge>
                      )}
                      {image.style && (
                        <Badge variant="secondary" className="bg-[var(--buddychip-black)]/80 text-[var(--buddychip-white)] text-xs">
                          {image.style}
                        </Badge>
                      )}
                    </div>

                    {/* Download count */}
                    {image.downloadCount > 0 && (
                      <div className="absolute top-2 right-2">
                        <Badge variant="secondary" className="bg-[var(--buddychip-black)]/80 text-[var(--buddychip-white)] text-xs">
                          {image.downloadCount} downloads
                        </Badge>
                      </div>
                    )}
                  </div>

                  {/* Content */}
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      {/* Title or prompt preview */}
                      <h4 className="text-sm font-medium text-[var(--buddychip-white)] line-clamp-2">
                        {image.customName || image.originalPrompt}
                      </h4>
                      
                      {/* Metadata */}
                      <div className="flex items-center justify-between text-xs text-[var(--buddychip-grey-text)]">
                        <span>{formatDate(image.createdAt)}</span>
                        <span>{image.model}</span>
                      </div>

                      {/* Tags */}
                      {image.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {image.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {image.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{image.tags.length - 3}
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}