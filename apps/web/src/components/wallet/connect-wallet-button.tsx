import React from 'react';
import { Button } from '../ui/button';
import { Wallet } from 'lucide-react';

/**
 * Disabled Connect Wallet Button
 * 
 * Wallet functionality is temporarily disabled to prevent module conflicts.
 * This button shows a disabled state and informs users about the status.
 */
export function ConnectWalletButton() {
  const handleConnectWallet = () => {
    alert('Wallet functionality is temporarily disabled. This feature will be available in a future update.');
  };

  return (
    <Button 
      variant="outline" 
      size="sm"
      onClick={handleConnectWallet}
      className="border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)] hover:bg-[var(--buddychip-accent)]/10 hover:text-[var(--buddychip-accent)] hover:border-[var(--buddychip-accent)]"
    >
      <Wallet className="h-4 w-4 mr-2" />
      Connect Wallet (Coming Soon)
    </Button>
  );
}