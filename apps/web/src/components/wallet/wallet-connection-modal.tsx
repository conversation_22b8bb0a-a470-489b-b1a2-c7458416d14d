import React from 'react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

interface WalletConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  blockchain?: 'ethereum' | 'solana' | 'polygon' | 'base';
}

/**
 * Disabled Wallet Connection Modal
 * 
 * Wallet functionality is temporarily disabled to prevent module conflicts.
 * This modal shows a message about the feature being unavailable.
 */
export function WalletConnectionModal({ 
  isOpen, 
  onClose, 
  blockchain = 'solana' 
}: WalletConnectionModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-md mx-4 bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center justify-between text-[var(--buddychip-white)]">
            Connect Wallet
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onClose}
              className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]"
            >
              ✕
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 pt-0">
          <div className="text-center space-y-4">
            <div className="text-[var(--buddychip-accent)] text-4xl">🚧</div>
            <div>
              <p className="font-medium text-[var(--buddychip-white)]">Coming Soon</p>
              <p className="text-sm text-[var(--buddychip-grey-text)]">
                Wallet connectivity is currently being optimized and will be available in a future update.
              </p>
            </div>
            <Button 
              onClick={onClose}
              className="w-full bg-[var(--buddychip-accent)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent)]/80"
            >
              Got it
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}