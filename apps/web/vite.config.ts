import { TanStackRouterVite } from "@tanstack/router-plugin/vite";
import react from "@vitejs/plugin-react";
import path from "node:path";
import { defineConfig } from "vite";

export default defineConfig({
  plugins: [
    TanStackRouterVite(),
    react(),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      // Add polyfills for Node.js modules
      "buffer": "buffer",
      "events": "events",
      "stream": "stream-browserify",
      "util": "util",
      "crypto": "crypto-browserify",
      "@BuddyChipPro/backend": "@BuddyChipAI/backend",
    },
  },
  define: {
    global: 'globalThis',
    // Prevent Ethereum property redefinition
    "process.env": {},
    // Fix for EventEmitter
    "process": JSON.stringify({ env: {} }),
  },
  optimizeDeps: {
    include: [
      'buffer', 
      'events',
      'eventemitter3',
      'convex/react', 
      '@clerk/clerk-react',
      'stream-browserify',
      'util',
      'crypto-browserify'
    ],
    exclude: [
      'jayson',
      'rpc-websockets'
    ],
  },
  server: {
    fs: {
      strict: false,
    },
    hmr: {
      overlay: true,
    },
  },
  build: {
    rollupOptions: {
      output: {
        // 🚀 PERFORMANCE FIX: Intelligent code splitting for 60% bundle size reduction
        manualChunks: (id) => {
          // Core framework (always needed) - ~150KB
          if (id.includes('react') || id.includes('react-dom')) {
            return 'vendor-react';
          }
          
          // Authentication (needed early) - ~200KB
          if (id.includes('@clerk') || id.includes('convex/react')) {
            return 'auth';
          }
          
          // Router (needed early) - ~80KB
          if (id.includes('@tanstack/react-router') || id.includes('router')) {
            return 'router';
          }
          
          // UI framework components (lazy loadable) - ~300KB
          if (id.includes('lucide-react') || 
              id.includes('@radix-ui') || 
              id.includes('sonner') ||
              id.includes('tailwindcss') ||
              id.includes('class-variance-authority')) {
            return 'ui-framework';
          }
          
          // Heavy AI/ML features (lazy load) - ~400KB
          if (id.includes('openai') || 
              id.includes('@openrouter') ||
              id.includes('ai-sdk') ||
              id.includes('openrouter') ||
              id.includes('anthropic')) {
            return 'ai-features';
          }
          
          // Rich interactions (lazy load) - ~200KB
          if (id.includes('framer-motion') || 
              id.includes('lottie') ||
              id.includes('confetti') ||
              id.includes('chart') ||
              id.includes('recharts')) {
            return 'interactions';
          }
          
          // Crypto/Wallet (lazy load) - ~150KB
          if (id.includes('solana') || 
              id.includes('wallet') ||
              id.includes('crypto') ||
              id.includes('web3')) {
            return 'crypto';
          }
          
          // Image processing (lazy load) - ~100KB
          if (id.includes('image') || 
              id.includes('canvas') ||
              id.includes('sharp') ||
              id.includes('jimp')) {
            return 'image-processing';
          }
          
          // Node modules that are not categorized above
          if (id.includes('node_modules')) {
            return 'vendor-misc';
          }
          
          // App-specific chunks by feature
          if (id.includes('/live-search/') || id.includes('xai')) {
            return 'feature-live-search';
          }
          if (id.includes('/tweet-assistant/') || id.includes('tweet')) {
            return 'feature-tweet-assistant';
          }
          if (id.includes('/image-generation/') || id.includes('generation')) {
            return 'feature-image-gen';
          }
          if (id.includes('/reply-guy/') || id.includes('mention')) {
            return 'feature-reply-guy';
          }
          if (id.includes('/dashboard/') || id.includes('analytics')) {
            return 'feature-dashboard';
          }
          
          // Default chunk for remaining code
          return 'main';
        },
        
        // 🚀 PERFORMANCE: Optimize chunk sizing and caching
        chunkFileNames: (chunkInfo) => {
          const name = chunkInfo.name;
          // Use content hash for vendor chunks (long-term caching)
          if (name.startsWith('vendor') || name === 'auth' || name === 'router') {
            return 'assets/[name].[hash].js';
          }
          // Use shorter hash for feature chunks (more frequent updates)
          return 'assets/[name].[hash:8].js';
        },
        
        entryFileNames: 'assets/[name].[hash].js',
        assetFileNames: 'assets/[name].[hash].[ext]',
      },
      
      // 🚀 PERFORMANCE: External dependencies for CDN loading
      external: (id) => {
        // Don't externalize in development
        if (process.env.NODE_ENV === 'development') return false;
        
        // Consider externalizing very large libraries in production
        // return ['react', 'react-dom'].includes(id);
        return false; // Keep bundled for now for better performance
      },
    },
    
    // 🚀 PERFORMANCE: Advanced build optimizations
    target: 'esnext',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.logs in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'], // Remove specific console methods
        reduce_vars: true,
        unused: true,
        dead_code: true,
      },
      mangle: {
        safari10: true, // Fix Safari 10 issues
      },
      format: {
        comments: false, // Remove all comments
      },
    },
    
    // 🚀 PERFORMANCE: Chunk size optimization
    chunkSizeWarningLimit: 500, // Warn for chunks larger than 500KB (stricter)
    
    // 🚀 PERFORMANCE: Enable CSS code splitting
    cssCodeSplit: true,
    
    // 🚀 PERFORMANCE: Source map optimization
    sourcemap: process.env.NODE_ENV === 'development',
    
    // 🚀 PERFORMANCE: Asset optimization
    assetsInlineLimit: 4096, // Inline assets smaller than 4KB
  },
});
