# Chatbot Implementation Plan

This document outlines the basic design for the new BuddyChip Pro Chatbot.

## Goals
- Conversational assistant available from any page.
- Uses the **ai** SDK with OpenRouter provider.
- Can trigger Convex actions as tools (image generation and xAI search).
- <PERSON><PERSON> appears as a floating widget in the bottom-right corner and can expand to fullscreen.
- Toggle via `Cmd/Ctrl + K`.

## Architecture
1. **Convex Action** `chatbot.chatWithTools`
   - Accepts an array of chat messages.
   - Uses `AIFallbackClient` to get a response.
   - Detects `image:` and `search:` commands in the AI response and runs the corresponding Convex actions:
     - `ai/unifiedImageGeneration:generateUnifiedImage`
     - `ai/xaiLiveSearch:xaiRealTimeContentAnalysis`
   - Returns the final assistant message.

2. **React Component** `Chatbot`
   - Keeps local chat history.
   - Calls `chatWithTools` when the user sends a message.
   - Provides open/close and fullscreen controls.
   - Listens for `Cmd/Ctrl + K` to toggle visibility.

3. **Integration**
   - Added to `__root.tsx` so it is available globally.

This implementation focuses on a lightweight demo of the chatbot using existing backend capabilities.
