# Optimization Testing and Monitoring Guide

This document outlines testing considerations and monitoring suggestions for the recently implemented performance optimizations.

## I. Testing Considerations

### A. Pagination

1.  **Verify cursor-based pagination in `tweets.ts` and `mentionQueries.ts`:**
    *   Test fetching multiple pages of data sequentially. Ensure the `nextCursor` from one response correctly fetches the subsequent set of items.
    *   Verify the `hasMore` flag accuracy:
        *   It should be `true` when more items are available.
        *   It should be `false` on the last page of results.
    *   Test empty state (no items returned) and single-page results (number of items less than or equal to the limit).
    *   For `mentionQueries.ts`, specifically test the behavior when `monitoredAccountId` is provided (pagination per account) versus when it's not (pagination over combined results from multiple accounts, where `nextCursor` might be `null`).
    *   Confirm that any client-side hooks (e.g., a `usePaginatedQuery` hook if it exists) integrate correctly with these backend pagination changes, properly using the `cursor` and `hasMore` fields.

2.  **Test `searchTweets` and `searchMentions` "load more" style pagination:**
    *   Verify that subsequent requests fetch additional sets of results.
    *   Confirm the `hasMore` flag correctly indicates if more search results are available.

### B. Field Selection (Projections)

1.  **Verify Lightweight List Views:**
    *   For list views that use queries from `tweets.ts` (e.g., `getTweetsByAccount`, `getRecentTweets`), `mentionQueries.ts` (e.g., `getRecentMentions`), and any relevant queries in `optimizedQueries.ts`.
    *   Inspect network requests (e.g., using browser developer tools) to confirm that the JSON payload for these list queries contains only the defined lightweight fields.
    *   Alternatively, log the received data structure on the client-side to verify its shape.
2.  **Verify Full Data in Detail Views:**
    *   For queries intended to fetch full object details (e.g., `getTweetById`, `getMentionById`).
    *   Confirm that these queries return all necessary fields for a detailed view of an item.
3.  **UI Integrity:**
    *   Thoroughly test all UI components that display data from the modified list queries.
    *   Ensure no components are broken or missing information due to fields being removed from the lightweight objects. Pay close attention to conditional rendering or formatting based on previously available fields.

### C. Image Handling (Base64 Caching)

1.  **Cache Population:**
    *   Trigger the `downloadImageAsBase64` action for an image that has an `imageId`.
    *   After the action completes, inspect the `generatedImages` table in the database (e.g., via a Convex query or dashboard) to verify that the `imageBase64` field for that specific `imageId` is populated with a valid Base64 data URI.
2.  **Cache Retrieval:**
    *   After confirming the cache is populated, call `getImageMetadata` for the same `imageId`.
    *   Verify that the returned document includes the `imageBase64` string that was cached.
3.  **Client-Side Logic:**
    *   Test any client-side components or logic responsible for displaying images.
    *   Ensure they correctly prioritize using the direct `imageBase64` field from `getImageMetadata` if it's available.
    *   Verify that if `imageBase64` is not available, the client correctly falls back to calling the `downloadImageAsBase64` action.

### D. Client-Side Subscription Optimization (`useCachedQuery` with `enabled` flag)

1.  **Query Skipping:**
    *   In a component using `useCachedQuery`, pass the option `{ enabled: false }`.
    *   Monitor network requests or use Convex dashboard to confirm that the corresponding `useQuery` does not execute (no request is sent to the backend).
2.  **Cached Data Return:**
    *   Ensure data is already in the client-side cache for a specific query.
    *   Render the component using `useCachedQuery` with `{ enabled: false }`.
    *   Verify that the component still displays the cached data, even though the live query is skipped.
3.  **Query Resumption:**
    *   Start with `{ enabled: false }`.
    *   Dynamically change the `enabled` flag to `true` (e.g., based on a button click or state change).
    *   Verify that `useQuery` now executes and fetches fresh data from the backend.
4.  **Visibility Integration:**
    *   If a `usePageVisibility` hook or similar mechanism is used to control the `enabled` flag.
    *   Test by switching browser tabs or minimizing/restoring the window.
    *   Confirm that queries are paused (not fetching updates) when the page is hidden and resume (fetch updates if stale) when the page becomes visible again.

### E. Server-Side Caching (`getTweetStats`)

1.  **Cache Hit Verification:**
    *   Call `getTweetStats` (with or without `twitterAccountId`).
    *   Immediately call it again with the same arguments.
    *   The second call should return data much faster and ideally should not result in extensive database operations for recalculating stats (monitor query logs or add temporary logging if needed). This indicates a cache hit.
2.  **TTL Expiration:**
    *   Call `getTweetStats`.
    *   Wait for the TTL (e.g., 5 minutes) to expire.
    *   Call `getTweetStats` again with the same arguments.
    *   This call should trigger a re-computation of the stats (similar to the first call's performance profile) and then store the new result in the cache.
3.  **Cache Key Specificity:**
    *   Test `getTweetStats` without `twitterAccountId` (global stats). Verify caching.
    *   Test `getTweetStats` with a specific `twitterAccountId`. Verify caching for this account.
    *   Test with a *different* `twitterAccountId`. This should fetch/compute fresh stats for this second account and cache them separately. Ensure it doesn't incorrectly return stats for the first account.

### F. Analytics Batching

1.  **Event Buffering:**
    *   Call `AnalyticsBatcher.track()` multiple times with different events.
    *   If possible, inspect the `buffer` property of the `AnalyticsBatcher` instance (e.g., via browser console if exposed for debugging) to see if events are being added.
2.  **Automatic Flushing (Batch Size & Interval):**
    *   **Batch Size:** Configure a small `batchSize` (e.g., 3 events). Call `track()` 3 times. Verify that a request to `ingestAnalyticsBatch` is made shortly after the 3rd event.
    *   **Time Interval:** Configure a short `flushIntervalMs` (e.g., 5 seconds). Call `track()` once. Verify that a request is made after approximately 5 seconds, even if the batch size isn't met.
3.  **Visibility/Unload Flushing:**
    *   Add a few events to the batcher without reaching the batch size or immediate flush interval.
    *   Switch to a different browser tab (triggers `visibilitychange` to hidden). Verify a flush occurs.
    *   (Harder to test reliably) Close the tab/browser. Check server logs or database if a flush was attempted for `beforeunload`.
4.  **Database Verification:**
    *   After events have been flushed, query the `analyticsEvents` table in the database.
    *   Confirm that the events are stored correctly, including `eventName`, `timestamp`, `userId`, `clerkUserId`, `sessionId`, `path`, and the `properties` JSON object.
5.  **Mutation Handling:**
    *   Check server-side logs for the `ingestAnalyticsBatch` mutation. Ensure it reports receiving the correct number of events and executes without errors.

### G. General

1.  **Regression Testing:**
    *   Execute existing test suites (if any).
    *   Manually test core application features (e.g., user login, tweet posting, feed interaction, search functionality) to ensure they haven't been negatively impacted by the optimization changes.
2.  **Console Errors:**
    *   Open the browser developer console while testing all features.
    *   Look for any new JavaScript errors or warnings that might have been introduced.
3.  **Cross-Browser/Device Testing (Optional but Recommended):**
    *   If resources permit, test the application on a few different browsers (e.g., Chrome, Firefox, Safari) and device types (desktop, mobile) to catch any environment-specific issues.

## II. Monitoring Suggestions

### A. Convex Query Monitoring

1.  **Query Durations:**
    *   Utilize the Convex dashboard or logging to monitor average and percentile (p95, p99) durations for key queries, especially those modified for pagination, projections, or server-side caching (`getTweetStats`, `getTweetsByAccount`, `getRecentMentions`, etc.).
    *   Look for improvements (lower durations) or unexpected degradations.
2.  **Number of Records Read/Scanned:**
    *   For list queries, track if the number of records read/scanned per query execution decreases. This indicates that pagination is working effectively and fewer items are being processed at the database level for each page request.
3.  **Bandwidth Usage per Query Type:**
    *   If Convex provides metrics on bandwidth per function or query type, monitor this for the optimized list queries. It should decrease due to field projections returning less data.
4.  **Cache Hit/Miss Ratios:**
    *   **`tweetStatsCache` (Server-Side):**
        *   Modify `getTweetStats` to log (or increment a metric if a metrics system is in place) when a cache hit occurs versus a cache miss (requiring re-computation). This will help assess the cache effectiveness.
    *   **`useCachedQuery` (Client-Side):**
        *   Consider adding optional logging within `useCachedQuery` (perhaps behind a debug flag) to log cache hits, misses, and stale-while-revalidate scenarios. This can provide insights into how well the client-side cache is performing for users.

### B. Client-Side Performance

1.  **Page Load Times:**
    *   Use web performance monitoring tools (e.g., Lighthouse, WebPageTest, or APM solutions like Sentry, Datadog RUM) to track metrics like First Contentful Paint (FCP), Largest Contentful Paint (LCP), and Time to Interactive (TTI) for pages that display lists of data (e.g., tweet feeds, mention lists).
    *   Look for improvements after optimizations.
2.  **Data Fetching Times (Client Perception):**
    *   For lists, measure the perceived time from when a user action triggers a data fetch (or page load) to when the list items are rendered on screen. This can be done with custom performance marks/measures or by observing user experience.

### C. Analytics System

1.  **Volume of `ingestAnalyticsBatch` Calls:**
    *   Monitor the number of calls to the `ingestAnalyticsBatch` mutation. This count should be significantly lower than the number of individual events tracked if batching is effective.
2.  **Events Per Batch:**
    *   Log or track the `args.events.length` in the `ingestAnalyticsBatch` mutation. Monitor the average, min, and max number of events per batch to understand if batching parameters (`batchSize`, `flushIntervalMs`) are well-tuned.
3.  **Data Integrity:**
    *   Periodically query the `analyticsEvents` table to sample events and ensure all properties are being logged completely and accurately as expected.
    *   Set up alerts for any errors logged by the `ingestAnalyticsBatch` mutation.

### D. Image Handling

1.  **Frequency of `downloadImageAsBase64` Calls:**
    *   If possible, log or track the number of times the `downloadImageAsBase64` action is invoked, particularly for image IDs that have already been processed. This count should decrease as the `imageBase64` field in `generatedImages` gets populated and used by clients.
2.  **Size of `generatedImages` Table:**
    *   Monitor the overall size of the `generatedImages` table in the database.
    *   Specifically, track the storage consumed by the `imageBase64` field. This will help understand the storage impact of caching Base64 strings. Consider if cleanup strategies for old or unused Base64 strings might be needed in the long term.

### E. Overall Bandwidth

1.  **Database Read Bandwidth:**
    *   Use the Convex dashboard or other database monitoring tools to monitor overall database read bandwidth. Successful field projections and server-side caching should contribute to a reduction here.
2.  **Egress Bandwidth:**
    *   If applicable (e.g., for self-hosted frontends or services), track overall egress bandwidth from application servers. Reduced data payloads from optimized queries should lower this.

This guide provides a comprehensive starting point for testing the implemented optimizations and for ongoing monitoring to ensure they deliver the expected benefits and maintain application health.
