# BuddyChip Pro - Complete Billing Setup Guide

## 🎯 **Overview**

This guide walks you through setting up the complete billing system for BuddyChip Pro, including Clerk billing integration, Stripe configuration, webhook setup, and testing.

## 📋 **Prerequisites**

- [x] Clerk account with billing enabled
- [x] Stripe account (connected to Clerk)
- [x] Domain with HTTPS for webhook endpoints
- [x] Development environment set up

## 🚀 **Quick Setup (5 Minutes)**

### 1. **Run Setup Script**
```bash
# Install dependencies
bun install

# Run billing setup validation
node scripts/setup-billing.js
```

### 2. **Configure Environment Variables**
```bash
# Copy environment template
cp .env.example .env.local

# Edit with your actual values
nano .env.local
```

### 3. **Start Development Server**
```bash
# Start Convex backend
bun run dev:backend

# Start frontend (in another terminal)
bun run dev:web
```

## 🔧 **Detailed Setup Instructions**

### **Step 1: Clerk Dashboard Configuration**

1. **Enable Billing**
   - Go to [Clerk Dashboard](https://dashboard.clerk.com/)
   - Navigate to "Billing" section
   - Click "Enable Billing"
   - Connect your Stripe account

2. **Create Subscription Products**
   ```
   Product 1: BuddyChip Starter
   - Price: $19/month
   - Features: Basic monitoring, 100 AI responses
   
   Product 2: BuddyChip Pro  
   - Price: $49/month
   - Features: Premium AI, image generation, analytics
   
   Product 3: BuddyChip Enterprise
   - Price: $99/month
   - Features: Unlimited usage, white-label options
   ```

3. **Configure Webhook**
   - Add webhook endpoint: `https://your-domain.com/api/billing/webhook`
   - Select events: `subscription.*`, `invoice.*`, `customer.*`
   - Copy webhook secret to `CLERK_WEBHOOK_SECRET`

### **Step 2: Stripe Dashboard Configuration**

1. **Create Products**
   - Go to [Stripe Dashboard](https://dashboard.stripe.com/)
   - Navigate to "Products"
   - Create products matching your Clerk configuration

2. **Create Price Objects**
   ```
   Starter Plan:
   - Monthly: price_starter_monthly
   - Annual: price_starter_annual (optional)
   
   Pro Plan:
   - Monthly: price_pro_monthly  
   - Annual: price_pro_annual (optional)
   
   Enterprise Plan:
   - Monthly: price_enterprise_monthly
   - Annual: price_enterprise_annual (optional)
   ```

3. **Copy Price IDs**
   - Copy each price ID to your environment variables
   - Update `.env.local` with the actual IDs

### **Step 3: Environment Configuration**

Update your `.env.local` file with all required values:

```bash
# Clerk Configuration
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_key_here
CLERK_SECRET_KEY=sk_test_your_key_here
CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Stripe Configuration  
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_key_here

# Product Configuration
CLERK_BILLING_PRODUCT_STARTER=prod_your_starter_id
CLERK_BILLING_PRODUCT_PRO=prod_your_pro_id
CLERK_BILLING_PRODUCT_ENTERPRISE=prod_your_enterprise_id

# Price Configuration
STRIPE_PRICE_STARTER_MONTHLY=price_your_starter_monthly_id
STRIPE_PRICE_PRO_MONTHLY=price_your_pro_monthly_id
STRIPE_PRICE_ENTERPRISE_MONTHLY=price_your_enterprise_monthly_id

# Webhook Configuration
BILLING_WEBHOOK_ENDPOINT=https://your-domain.com/api/billing/webhook
```

### **Step 4: Local Development Setup**

1. **Install ngrok for webhook testing**
   ```bash
   # Install ngrok
   npm install -g ngrok
   
   # Start ngrok tunnel
   ngrok http 3000
   ```

2. **Update webhook URL in Clerk**
   - Copy the ngrok HTTPS URL
   - Update webhook endpoint in Clerk dashboard
   - Format: `https://abc123.ngrok.io/api/billing/webhook`

3. **Test webhook reception**
   ```bash
   # Check webhook endpoint
   curl -X POST https://abc123.ngrok.io/api/webhook/test \
     -H "Content-Type: application/json" \
     -d '{"test": "data"}'
   ```

## 🧪 **Testing the Billing System**

### **1. Unit Tests**
```bash
# Run billing system tests
bun test packages/backend/convex/billing/billing.test.ts
```

### **2. Integration Testing**

1. **Create Test Subscription**
   - Use Stripe test cards: `4242 4242 4242 4242`
   - Create subscription through Clerk
   - Verify webhook reception
   - Check database updates

2. **Test Usage Tracking**
   - Generate AI responses
   - Check usage increments
   - Verify limit enforcement
   - Test upgrade flows

3. **Test Webhook Events**
   ```bash
   # Test subscription created
   # Test subscription updated  
   # Test subscription canceled
   # Test payment succeeded
   # Test payment failed
   ```

### **3. End-to-End Testing**

1. **Complete User Journey**
   - User signs up
   - Selects subscription plan
   - Payment processed
   - Features unlocked
   - Usage tracked
   - Limits enforced

2. **Error Scenarios**
   - Payment failures
   - Webhook delivery failures
   - Network timeouts
   - Invalid data handling

## 🔍 **Troubleshooting**

### **Common Issues**

1. **Webhook Not Receiving Events**
   ```bash
   # Check webhook URL configuration
   # Verify HTTPS endpoint
   # Check firewall settings
   # Validate webhook secret
   ```

2. **Signature Verification Failing**
   ```bash
   # Verify webhook secret in environment
   # Check timestamp tolerance
   # Validate request headers
   ```

3. **Subscription Not Syncing**
   ```bash
   # Check webhook event order
   # Verify user mapping
   # Check database constraints
   # Review error logs
   ```

### **Debug Tools**

1. **Webhook Logs**
   - Clerk Dashboard → Webhooks → Logs
   - Check delivery status and responses

2. **Convex Logs**
   ```bash
   # View function logs
   bunx convex logs
   
   # Filter billing logs
   bunx convex logs --filter "billing"
   ```

3. **Manual Sync**
   ```bash
   # Use manual sync function for debugging
   # Check subscription status manually
   # Verify feature access
   ```

## 📊 **Monitoring & Maintenance**

### **Health Checks**
```bash
# Check billing system health
curl https://your-domain.com/health

# Check authenticated health
curl https://your-domain.com/health/auth \
  -H "Authorization: Bearer your_token"
```

### **Usage Monitoring**
- Monitor webhook processing rates
- Track subscription conversion rates
- Monitor payment failure rates
- Track feature usage patterns

### **Alerts Setup**
- Failed webhook deliveries
- High payment failure rates
- Unusual usage patterns
- System health issues

## 🚀 **Production Deployment**

### **Pre-Deployment Checklist**
- [ ] All environment variables configured
- [ ] Webhook endpoints using HTTPS
- [ ] Signature verification enabled
- [ ] Error handling implemented
- [ ] Monitoring and alerts configured
- [ ] Backup and recovery procedures
- [ ] Load testing completed

### **Deployment Steps**
1. Deploy backend with webhook endpoints
2. Update webhook URLs in Clerk dashboard
3. Test webhook delivery in production
4. Monitor initial subscription flows
5. Verify all integrations working

### **Post-Deployment Monitoring**
- Monitor webhook delivery success rates
- Track subscription creation/cancellation rates
- Monitor payment processing
- Check error rates and response times

## 📞 **Support & Resources**

### **Documentation**
- [Clerk Billing Docs](https://clerk.com/docs/billing)
- [Stripe API Docs](https://stripe.com/docs/api)
- [Convex HTTP Actions](https://docs.convex.dev/functions/http-actions)

### **Support Channels**
- Clerk Support: <EMAIL>
- Stripe Support: <EMAIL>
- Internal: Check logs and monitoring dashboards

---

**🎉 Congratulations!** Your billing system is now fully configured and ready for production use.
