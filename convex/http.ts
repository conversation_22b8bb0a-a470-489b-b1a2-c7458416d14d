import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";

const http = httpRouter();

/**
 * 🔐 SECURITY: Secure HTTP endpoint with authentication and security headers
 * This endpoint now requires authentication and applies security headers
 */
http.route({
  path: "/hello",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    try {
      // 🔐 SECURITY: Require authentication for HTTP endpoints
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        const response = new Response(
          JSON.stringify({ error: "Authentication required" }),
          {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
              'X-Content-Type-Options': 'nosniff',
              'X-Frame-Options': 'DENY',
              'X-XSS-Protection': '1; mode=block',
            }
          }
        );
        return response;
      }

      // Create secure response with security headers
      const response = new Response(
        JSON.stringify({
          message: "Hello from Convex!",
          authenticated: true,
          user: {
            id: identity.subject,
            name: identity.name,
          }
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            // Security headers
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            // CORS headers for development
            'Access-Control-Allow-Origin': process.env.NODE_ENV === 'production'
              ? 'https://buddychip.pro'
              : '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          }
        }
      );

      return response;
    } catch (error) {
      // 🔐 SECURITY: Sanitized error response
      const response = new Response(
        JSON.stringify({ error: "Internal server error" }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
          }
        }
      );
      return response;
    }
  }),
});

export default http;