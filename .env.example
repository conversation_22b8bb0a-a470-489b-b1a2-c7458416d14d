# =============================================================================
# BuddyChip Pro - AI Twitter Agent Environment Configuration
# =============================================================================
# Copy this file to .env.local and fill in your actual values
# Never commit .env.local to version control!
# =============================================================================

# -----------------------------------------------------------------------------
# 1. CONVEX BACKEND CONFIGURATION (Required)
# -----------------------------------------------------------------------------
# Get these from: https://dashboard.convex.dev
# 1. Create account at convex.dev
# 2. Create new project or use existing
# 3. Copy deployment URL and deployment name
CONVEX_DEPLOYMENT=dev:your-deployment-name-123
VITE_CONVEX_URL=https://your-deployment-123.convex.cloud

# -----------------------------------------------------------------------------
# 2. AUTHENTICATION CONFIGURATION (Required)
# -----------------------------------------------------------------------------
# Clerk Authentication - Primary authentication service
# Get from: https://dashboard.clerk.com/
#
# Setup Instructions:
# 1. Create account at clerk.com
# 2. Create new application
# 3. Enable Google OAuth provider
# 4. Copy publishable key and secret key
# 5. Configure billing settings for subscription management
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key_here
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key_here

# Clerk Billing Configuration (for subscription management)
# Enable billing in Clerk dashboard first
CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Stripe Configuration (used by Clerk for billing)
# Get from: https://dashboard.stripe.com/apikeys
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here

# Clerk Product IDs (configure these in Clerk dashboard)
# Map your Clerk product IDs to subscription plans
CLERK_BILLING_PRODUCT_STARTER=prod_starter_id_here
CLERK_BILLING_PRODUCT_PRO=prod_pro_id_here
CLERK_BILLING_PRODUCT_ENTERPRISE=prod_enterprise_id_here

# Stripe Price IDs (configure these in Stripe dashboard)
# Monthly subscription price IDs
STRIPE_PRICE_STARTER_MONTHLY=price_starter_monthly_id_here
STRIPE_PRICE_PRO_MONTHLY=price_pro_monthly_id_here
STRIPE_PRICE_ENTERPRISE_MONTHLY=price_enterprise_monthly_id_here

# Annual subscription price IDs (optional)
STRIPE_PRICE_STARTER_ANNUAL=price_starter_annual_id_here
STRIPE_PRICE_PRO_ANNUAL=price_pro_annual_id_here
STRIPE_PRICE_ENTERPRISE_ANNUAL=price_enterprise_annual_id_here

# Billing Configuration
BILLING_WEBHOOK_ENDPOINT=https://your-domain.com/api/billing/webhook
BILLING_GRACE_PERIOD_DAYS=3
BILLING_RETRY_ATTEMPTS=3
BILLING_ENABLE_PRORATIONS=true

# Legacy Google OAuth (if migrating from Convex Auth)
# AUTH_GOOGLE_ID=your_google_client_id_here.apps.googleusercontent.com
# AUTH_GOOGLE_SECRET=GOCSPX-your_google_client_secret_here

# -----------------------------------------------------------------------------
# 3. CORE AI SERVICES (Required for Main Features)
# -----------------------------------------------------------------------------
# OpenRouter API - Primary AI service for analysis and response generation
# Get from: https://openrouter.ai/keys
# 1. Create account at openrouter.ai
# 2. Add credits ($5+ recommended for testing)
# 3. Generate API key
# Models used: google/gemini-2.5-flash-preview, anthropic/claude-3.5-sonnet
OPENROUTER_API_KEY=sk-or-v1-your_openrouter_api_key_here

# =============================================================================
# 🤖 INTELLIGENT AI MODEL CONFIGURATION (OpenRouter Only)
# =============================================================================
# Configure different OpenRouter models for different performance needs
# All models run through your single OpenRouter API key

# -----------------------------------------------------------------------------
# TEXT GENERATION MODELS (Required)
# -----------------------------------------------------------------------------
# Ultra-fast models (< 2 seconds) - Real-time responses, chat
TEXT_MODEL_ULTRA_FAST=google/gemini-2.5-flash-preview-05-20
TEXT_MODEL_ULTRA_FAST_BACKUP=anthropic/claude-3.5-haiku

# Fast models (< 8 seconds) - Standard responses, tweets
TEXT_MODEL_FAST=google/gemini-2.5-flash-preview-05-20
TEXT_MODEL_FAST_BACKUP=openai/gpt-4o-mini

# Quality models (< 30 seconds) - Important content, analysis
TEXT_MODEL_QUALITY=google/gemini-2.5-pro-preview
TEXT_MODEL_QUALITY_BACKUP=anthropic/claude-3.5-sonnet

# Bulk models (cheap/free) - Batch processing, cost optimization
TEXT_MODEL_BULK=google/gemini-flash-1.5-8b:free
TEXT_MODEL_BULK_BACKUP=meta-llama/llama-3.1-8b-instruct:free

# Fallback chain (comma-separated, in priority order)
TEXT_MODELS_FALLBACK=google/gemini-pro-1.5,meta-llama/llama-3.1-8b-instruct,mistralai/mistral-7b-instruct

# -----------------------------------------------------------------------------
# IMAGE GENERATION MODELS (via OpenRouter)
# -----------------------------------------------------------------------------
# Fast image generation
IMAGE_MODEL_FAST=black-forest-labs/flux-1.1-pro
IMAGE_MODEL_FAST_BACKUP=black-forest-labs/flux-schnell:free

# Quality image generation (best results)
IMAGE_MODEL_QUALITY=black-forest-labs/flux-1.1-pro-ultra
IMAGE_MODEL_QUALITY_BACKUP=black-forest-labs/flux-1.1-pro

# Bulk image generation (cost-optimized)
IMAGE_MODEL_BULK=black-forest-labs/flux-schnell:free
IMAGE_MODEL_BULK_BACKUP=black-forest-labs/flux-dev

# -----------------------------------------------------------------------------
# ANALYSIS MODELS (Content analysis, viral detection)
# -----------------------------------------------------------------------------
# Fast analysis (< 3 seconds)
ANALYSIS_MODEL_FAST=google/gemini-2.0-flash-exp:free
ANALYSIS_MODEL_FAST_BACKUP=google/gemini-flash-1.5-8b:free

# Quality analysis (< 15 seconds)
ANALYSIS_MODEL_QUALITY=google/gemini-pro-1.5
ANALYSIS_MODEL_QUALITY_BACKUP=anthropic/claude-3.5-haiku

# Bulk analysis (batch processing)
ANALYSIS_MODEL_BULK=google/gemini-flash-1.5-8b:free
ANALYSIS_MODEL_BULK_BACKUP=meta-llama/llama-3.1-8b-instruct:free

# -----------------------------------------------------------------------------
# EMBEDDING MODELS (Semantic search, vector operations)
# -----------------------------------------------------------------------------
# Fast embeddings
EMBEDDING_MODEL_FAST=openai/text-embedding-3-small
EMBEDDING_MODEL_FAST_BACKUP=openai/text-embedding-ada-002

# Quality embeddings
EMBEDDING_MODEL_QUALITY=openai/text-embedding-3-large
EMBEDDING_MODEL_QUALITY_BACKUP=openai/text-embedding-3-small

# -----------------------------------------------------------------------------
# MODEL SELECTION STRATEGY
# -----------------------------------------------------------------------------
# Default strategies for different operations
# Options: ultra_fast, fast, quality, bulk, auto

DEFAULT_TEXT_STRATEGY=fast          # Standard: fast, Quality: quality, Cost-saving: bulk
DEFAULT_IMAGE_STRATEGY=quality      # Standard: quality, Fast: fast, Budget: bulk
DEFAULT_EMBEDDING_STRATEGY=fast     # Standard: fast, Quality: quality
DEFAULT_ANALYSIS_STRATEGY=fast      # Standard: fast, Deep: quality

# Auto-strategy settings (when strategy=auto)
AUTO_STRATEGY_BUSINESS_HOURS=quality
AUTO_STRATEGY_OFF_HOURS=fast
AUTO_STRATEGY_HIGH_LOAD=ultra_fast

# -----------------------------------------------------------------------------
# PERFORMANCE & TIMEOUT SETTINGS
# -----------------------------------------------------------------------------
# Timeouts in milliseconds for different performance tiers
ULTRA_FAST_TIMEOUT=2000            # Real-time responses
FAST_TIMEOUT=8000                  # Standard responses
QUALITY_TIMEOUT=30000              # Important content
BULK_TIMEOUT=60000                 # Batch processing

# Maximum retries per performance tier
MAX_RETRIES_ULTRA_FAST=1
MAX_RETRIES_FAST=2
MAX_RETRIES_QUALITY=3
MAX_RETRIES_BULK=1

# -----------------------------------------------------------------------------
# COST OPTIMIZATION
# -----------------------------------------------------------------------------
# Enable intelligent cost management
ENABLE_COST_OPTIMIZATION=true

# Maximum cost per request (in cents)
MAX_COST_PER_TEXT_REQUEST=5
MAX_COST_PER_IMAGE_REQUEST=20
MAX_COST_PER_ANALYSIS_REQUEST=2

# Daily budget limits (in dollars)
DAILY_BUDGET_TEXT=50
DAILY_BUDGET_IMAGE=20
DAILY_BUDGET_ANALYSIS=10

# Fallback to free models when budget exceeded
FALLBACK_TO_FREE_ON_BUDGET_LIMIT=true

# -----------------------------------------------------------------------------
# MONITORING & ALERTS
# -----------------------------------------------------------------------------
# Enable detailed logging
ENABLE_MODEL_USAGE_LOGGING=true
ENABLE_PERFORMANCE_LOGGING=true

# Alert thresholds
ALERT_ON_HIGH_LATENCY=10000        # Alert if requests take > 10 seconds
ALERT_ON_HIGH_ERROR_RATE=10        # Alert if > 10% requests fail
ALERT_ON_BUDGET_THRESHOLD=80       # Alert at 80% budget usage

# =============================================================================
# 🎯 QUICK SETUP PRESETS
# =============================================================================
# Uncomment one of these preset configurations:

# 🚀 DEVELOPMENT (Fast iteration, low cost)
# DEFAULT_TEXT_STRATEGY=ultra_fast
# DEFAULT_IMAGE_STRATEGY=fast  
# TEXT_MODEL_FAST=google/gemini-2.0-flash-exp:free
# FALLBACK_TO_FREE_ON_BUDGET_LIMIT=true
# MAX_COST_PER_TEXT_REQUEST=1

# 🏭 PRODUCTION (Balanced quality and speed)
# DEFAULT_TEXT_STRATEGY=fast
# DEFAULT_IMAGE_STRATEGY=quality
# ENABLE_COST_OPTIMIZATION=true
# MAX_COST_PER_TEXT_REQUEST=3

# 💎 HIGH-QUALITY (Best results, higher cost)
# DEFAULT_TEXT_STRATEGY=quality
# DEFAULT_IMAGE_STRATEGY=quality
# ENABLE_COST_OPTIMIZATION=false
# MAX_RETRIES_QUALITY=5

# 💰 COST-CONSCIOUS (Minimize costs)
# DEFAULT_TEXT_STRATEGY=bulk
# TEXT_MODEL_FAST=google/gemini-flash-1.5-8b:free
# TEXT_MODEL_QUALITY=google/gemini-pro-1.5
# FALLBACK_TO_FREE_ON_BUDGET_LIMIT=true

# =============================================================================

# -----------------------------------------------------------------------------
# 4. TWITTER/X INTEGRATION (Required for Core Functionality)
# -----------------------------------------------------------------------------
# TweetIO - Primary Twitter scraping service
# Get from: https://twitterapi.io/dashboard
# 1. Create account at twitterapi.io
# 2. Subscribe to a plan (Pro plan recommended)
# 3. Get API key from dashboard
TWEETIO_API_KEY=your_tweetio_api_key_here

# Optional: Official Twitter/X API Bearer Token
# Get from: https://developer.twitter.com/en/portal/dashboard
# Required for: Enhanced rate limits, real-time features
# Note: TwitterAPI.io is the primary service, this is supplementary
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here

# -----------------------------------------------------------------------------
# 5. ENHANCED AI SERVICES (Optional - Extended Features)
# -----------------------------------------------------------------------------
# OpenAI API - Used for DALL-E image generation and GPT-4 fallback
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-proj-your_openai_api_key_here

# xAI API - Used for Grok integration and real-time search
# Get from: https://console.x.ai/
XAI_API_KEY=xai-your_xai_api_key_here

# Perplexity API - Used for enhanced context and research
# Get from: https://www.perplexity.ai/settings/api
PERPLEXITY_API_KEY=pplx-your_perplexity_api_key_here

# Anthropic API - Direct Claude access (alternative to OpenRouter)
# Get from: https://console.anthropic.com/
ANTHROPIC_API_KEY=sk-ant-your_anthropic_api_key_here

# Fal.ai API - Advanced image generation with Flux Pro model
# Get from: https://fal.ai/dashboard
# Used for: High-quality artistic image generation
FAL_API_KEY=your_fal_api_key_here

# -----------------------------------------------------------------------------
# 6. FEATURE CONFIGURATION (Optional)
# -----------------------------------------------------------------------------
# Enable/disable specific features
ENABLE_AI_ANALYSIS=true
ENABLE_MENTION_MONITORING=true
ENABLE_TWEET_SCRAPING=true
ENABLE_RESPONSE_GENERATION=true
ENABLE_CRON_JOBS=true
ENABLE_EMBEDDINGS=true

# TweetIO API Configuration
TWEETIO_API_DELAY=1000
TWEETIO_API_RETRY_ATTEMPTS=3
TWEETIO_API_RETRY_DELAY=5000
TWEETIO_DEFAULT_MAX_RESULTS=20
TWEETIO_MAX_ACCOUNTS_PER_BATCH=10
TWEETIO_MENTION_LOOKBACK_HOURS=24

# Monitoring Configuration
DEFAULT_SCRAPE_INTERVAL_MINUTES=60
MENTION_CHECK_INTERVAL_MINUTES=15
MAX_MENTIONS_PER_ACCOUNT=100
VERIFIED_ACCOUNT_WEIGHT=2.0
HIGH_PRIORITY_FOLLOWER_THRESHOLD=10000
MEDIUM_PRIORITY_FOLLOWER_THRESHOLD=1000

# AI Cost Tracking (in credits/tokens)
TOKEN_USAGE_ENABLED=true
DEFAULT_TWEET_GENERATION_COST=0.5
DEFAULT_CONTEXT_FETCH_COST=0.05
DEFAULT_TWEET_ANALYSIS_COST=0.1
DEFAULT_IMAGE_GENERATION_COST=1.0
DEFAULT_BULK_ANALYSIS_COST=0.5
DEFAULT_PREMIUM_THEME_COST=2.0
DEFAULT_VIRAL_SCORE_COST=0.2
DEFAULT_HASHTAG_SUGGESTIONS_COST=0.1
DEFAULT_ENGAGEMENT_PREDICTION_COST=0.3
DEFAULT_CONTENT_OPTIMIZATION_COST=0.4

# -----------------------------------------------------------------------------
# 7. BLOCKCHAIN/WALLET INTEGRATION (Optional - Future Features)
# -----------------------------------------------------------------------------
# Solana wallet configuration for token payments
RECIPIENT_WALLET_ADDRESS=your_solana_wallet_address_here
TOPUP_MIN_AMOUNT=1
TOPUP_MAX_AMOUNT=10000
TOPUP_FEE_PERCENTAGE=0.1

# -----------------------------------------------------------------------------
# 8. DEVELOPMENT CONFIGURATION (Development Only)
# -----------------------------------------------------------------------------
# Legacy debug settings (deprecated - use new debug system below)
DEBUG_MODE=false
LOG_LEVEL=info
VERBOSE_LOGGING=false

# Rate limiting for development
DEV_RATE_LIMIT_ENABLED=true
DEV_MAX_REQUESTS_PER_MINUTE=60

# =============================================================================
# 🐛 COMPREHENSIVE DEBUG CONFIGURATION SYSTEM
# =============================================================================
# NEW: Environment-controlled debug system for production security
# Granular control over debugging features and logging

# -----------------------------------------------------------------------------
# MASTER DEBUG CONTROLS
# -----------------------------------------------------------------------------
# Master switches - control all debug functionality
# Production: false | Development: true
VITE_DEBUG_MODE=true                   # Frontend debug master switch
DEBUG_MODE=true                        # Backend debug master switch

# Debug logging levels (0=ERROR, 1=WARN, 2=INFO, 3=DEBUG, 4=TRACE)
# Production: 0 | Development: 3
VITE_DEBUG_LOG_LEVEL=3                 # Frontend logging level
DEBUG_LOG_LEVEL=3                      # Backend logging level

# -----------------------------------------------------------------------------
# FRONTEND DEBUG CONTROLS 🎨
# -----------------------------------------------------------------------------
# Authentication debugging
VITE_AUTH_DEBUG=true                   # Enable auth debug components
VITE_AUTH_DEBUG_COMPONENTS=true        # Show auth debug panels in UI
VITE_AUTH_DEBUG_LOGGING=true          # Auth console logging
VITE_AUTH_DEBUG_TESTING=true          # Auth testing functionality

# Performance debugging
VITE_PERFORMANCE_DEBUG=true           # Performance monitoring
VITE_PERFORMANCE_MONITORING=true      # Real-time performance tracking
VITE_PERFORMANCE_ANALYTICS=true       # Performance analytics
VITE_CACHE_DEBUG=false                # Cache debugging (verbose)

# Application debugging
VITE_APP_DEBUG=true                   # General app debugging
VITE_CONSOLE_DEBUG=true               # Console debug output
VITE_ERROR_DEBUG=true                 # Enhanced error reporting
VITE_NETWORK_DEBUG=false              # Network request debugging (verbose)

# Development tools
VITE_DEV_TOOLS=true                   # Development tool availability
VITE_DEBUG_HOTKEYS=true               # Debug keyboard shortcuts (Ctrl+Shift+D/C)
VITE_DEBUG_OVERLAY=false              # Debug overlay UI (experimental)

# -----------------------------------------------------------------------------
# BACKEND DEBUG CONTROLS ⚙️
# -----------------------------------------------------------------------------
# Authentication debugging
DEBUG_AUTH=true                       # Backend auth debugging
DEBUG_AUTH_LOGGING=true               # Auth function logging
DEBUG_AUTH_VALIDATION=true            # JWT validation debugging
DEBUG_AUTH_FUNCTIONS=true             # Debug auth functions

# Performance debugging
DEBUG_PERFORMANCE=true                # Performance monitoring
DEBUG_PERFORMANCE_MONITORING=true     # Real-time monitoring
DEBUG_CACHE=false                     # Cache debugging (verbose)
DEBUG_QUERIES=false                   # Database query debugging (verbose)

# Database debugging
DEBUG_DATABASE=false                  # Database debugging (verbose)
DEBUG_DB_QUERIES=false                # Query debugging (very verbose)
DEBUG_DB_MUTATIONS=false              # Mutation debugging (verbose)
DEBUG_DB_INDEXES=false                # Index debugging (verbose)

# External API debugging
DEBUG_EXTERNAL=true                   # External service debugging
DEBUG_TWITTER_API=true                # Twitter API debugging
DEBUG_WEBHOOKS=true                   # Webhook debugging
DEBUG_INTEGRATIONS=true               # Integration debugging

# Workflow debugging
DEBUG_WORKFLOWS=true                  # Workflow debugging
DEBUG_CRONS=true                      # Cron job debugging
DEBUG_BATCHING=false                  # Batch processing debugging (verbose)

# -----------------------------------------------------------------------------
# PRODUCTION SECURITY NOTES 🔒
# -----------------------------------------------------------------------------
# ⚠️  CRITICAL: For production deployment, set all debug modes to false!
#
# PRODUCTION VALUES:
# VITE_DEBUG_MODE=false
# DEBUG_MODE=false  
# VITE_DEBUG_LOG_LEVEL=0
# DEBUG_LOG_LEVEL=0
# NODE_ENV=production
#
# This ensures:
# ✅ No debug panels visible in UI
# ✅ No sensitive information in console logs
# ✅ Optimal performance (no debug overhead)
# ✅ Enhanced security (no debug endpoints exposed)

# -----------------------------------------------------------------------------
# DEBUG PRESETS (Quick configuration)
# -----------------------------------------------------------------------------
# Uncomment one preset for quick setup:

# 🚀 DEVELOPMENT PRESET (Full debugging enabled)
# VITE_DEBUG_MODE=true
# DEBUG_MODE=true
# VITE_DEBUG_LOG_LEVEL=3
# DEBUG_LOG_LEVEL=3

# 🧪 TESTING PRESET (Core debugging only)
# VITE_DEBUG_MODE=true
# DEBUG_MODE=true
# VITE_AUTH_DEBUG=true
# VITE_PERFORMANCE_DEBUG=false
# DEBUG_AUTH=true
# DEBUG_PERFORMANCE=false

# 🏭 STAGING PRESET (Minimal debugging)
# VITE_DEBUG_MODE=false
# DEBUG_MODE=true
# VITE_DEBUG_LOG_LEVEL=1
# DEBUG_LOG_LEVEL=2

# 🔒 PRODUCTION PRESET (No debugging)
# VITE_DEBUG_MODE=false
# DEBUG_MODE=false
# VITE_DEBUG_LOG_LEVEL=0
# DEBUG_LOG_LEVEL=0

# =============================================================================
# SETUP PRIORITY GUIDE
# =============================================================================
# 
# 🚀 MINIMUM REQUIRED (Basic functionality):
#    1. CONVEX_DEPLOYMENT & VITE_CONVEX_URL
#    2. AUTH_GOOGLE_ID & AUTH_GOOGLE_SECRET  
#    3. OPENROUTER_API_KEY
#    4. TWITTER_API_KEY
#
# 🔥 RECOMMENDED (Full features):
#    5. OPENAI_API_KEY (for image generation)
#    6. XAI_API_KEY (for enhanced AI features)
#    7. TWITTER_BEARER_TOKEN (for better rate limits)
#
# ✨ OPTIONAL (Advanced features):
#    8. PERPLEXITY_API_KEY & ANTHROPIC_API_KEY
#    9. Blockchain wallet configuration
#    10. Advanced feature flags
#
# =============================================================================
# ESTIMATED MONTHLY COSTS (USD)
# =============================================================================
#
# Basic Setup:
# - Convex: $0-25 (free tier covers most usage)
# - OpenRouter: $10-50 (depends on AI usage)
# - TweetIO: $15-50 (Pro plan recommended)
# - Google OAuth: Free
# Total: $25-125/month
#
# Full Setup:
# - Add OpenAI: $10-30
# - Add xAI: $10-30  
# - Add Perplexity: $5-20
# Total: $50-205/month
#
# =============================================================================
# QUICK SETUP COMMANDS
# =============================================================================
#
# 1. Copy this file:
#    cp .env.example .env.local
#
# 2. Install dependencies:
#    bun install
#
# 3. Setup Convex:
#    bun dev:setup
#
# 4. Start development:
#    bun dev
#
# =============================================================================