#!/usr/bin/env node

/**
 * 🧪 BuddyChip Pro - Billing System Integration Test
 * 
 * This script performs comprehensive testing of the billing system:
 * 1. Tests webhook endpoint connectivity
 * 2. Validates subscription management functions
 * 3. Tests usage tracking and limits
 * 4. Verifies access control
 * 5. Simulates complete billing workflows
 */

const https = require('https');
const http = require('http');

// Test configuration
const TEST_CONFIG = {
  webhookEndpoint: process.env.BILLING_WEBHOOK_ENDPOINT || 'http://localhost:3000/api/billing/webhook',
  testTimeout: 10000, // 10 seconds
  retryAttempts: 3,
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logTest(message) {
  log(`🧪 ${message}`, 'cyan');
}

/**
 * Test webhook endpoint connectivity
 */
async function testWebhookEndpoint() {
  logTest('Testing webhook endpoint connectivity...');
  
  const testPayload = {
    type: 'test.webhook',
    data: {
      test: true,
      timestamp: Date.now(),
    },
    object: 'event',
    created: Date.now(),
  };

  try {
    const response = await makeHttpRequest(TEST_CONFIG.webhookEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'clerk-signature': 'v1,' + Math.floor(Date.now() / 1000) + ',test_signature',
      },
      body: JSON.stringify(testPayload),
    });

    if (response.status === 200) {
      logSuccess('Webhook endpoint is accessible');
      return true;
    } else {
      logError(`Webhook endpoint returned status: ${response.status}`);
      return false;
    }
  } catch (error) {
    logError(`Webhook endpoint test failed: ${error.message}`);
    return false;
  }
}

/**
 * Test subscription webhook events
 */
async function testSubscriptionWebhooks() {
  logTest('Testing subscription webhook events...');
  
  const testEvents = [
    {
      type: 'subscription.created',
      data: {
        id: 'test_sub_123',
        user_id: 'test_user_123',
        plan_id: 'prod_pro',
        status: 'active',
        current_period_start: Date.now(),
        current_period_end: Date.now() + 30 * 24 * 60 * 60 * 1000,
      },
    },
    {
      type: 'subscription.updated',
      data: {
        id: 'test_sub_123',
        user_id: 'test_user_123',
        plan_id: 'prod_enterprise',
        status: 'active',
        current_period_start: Date.now(),
        current_period_end: Date.now() + 30 * 24 * 60 * 60 * 1000,
      },
    },
    {
      type: 'invoice.payment_succeeded',
      data: {
        id: 'test_inv_123',
        subscription_id: 'test_sub_123',
        amount_paid: 4900, // $49.00
        currency: 'usd',
      },
    },
  ];

  let allPassed = true;

  for (const event of testEvents) {
    try {
      logInfo(`Testing ${event.type} event...`);
      
      const response = await makeHttpRequest(TEST_CONFIG.webhookEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'clerk-signature': 'v1,' + Math.floor(Date.now() / 1000) + ',test_signature',
        },
        body: JSON.stringify({
          ...event,
          object: 'event',
          created: Date.now(),
        }),
      });

      if (response.status === 200) {
        logSuccess(`${event.type} event processed successfully`);
      } else {
        logError(`${event.type} event failed with status: ${response.status}`);
        allPassed = false;
      }
    } catch (error) {
      logError(`${event.type} event test failed: ${error.message}`);
      allPassed = false;
    }
  }

  return allPassed;
}

/**
 * Test health endpoints
 */
async function testHealthEndpoints() {
  logTest('Testing health endpoints...');
  
  const healthEndpoints = [
    '/health',
    '/health/auth',
    '/api/status',
  ];

  let allPassed = true;

  for (const endpoint of healthEndpoints) {
    try {
      const url = TEST_CONFIG.webhookEndpoint.replace('/api/billing/webhook', endpoint);
      logInfo(`Testing ${endpoint}...`);
      
      const response = await makeHttpRequest(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 200 || response.status === 401) {
        // 401 is expected for auth endpoints without token
        logSuccess(`${endpoint} endpoint is accessible`);
      } else {
        logError(`${endpoint} endpoint returned status: ${response.status}`);
        allPassed = false;
      }
    } catch (error) {
      logError(`${endpoint} test failed: ${error.message}`);
      allPassed = false;
    }
  }

  return allPassed;
}

/**
 * Test webhook signature validation
 */
async function testWebhookSignatureValidation() {
  logTest('Testing webhook signature validation...');
  
  const testPayload = {
    type: 'test.signature',
    data: { test: true },
    object: 'event',
    created: Date.now(),
  };

  try {
    // Test with invalid signature
    logInfo('Testing invalid signature rejection...');
    const invalidResponse = await makeHttpRequest(TEST_CONFIG.webhookEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'clerk-signature': 'invalid_signature',
      },
      body: JSON.stringify(testPayload),
    });

    if (invalidResponse.status === 401) {
      logSuccess('Invalid signature correctly rejected');
    } else {
      logWarning(`Expected 401 for invalid signature, got ${invalidResponse.status}`);
    }

    // Test with missing signature
    logInfo('Testing missing signature rejection...');
    const missingResponse = await makeHttpRequest(TEST_CONFIG.webhookEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload),
    });

    if (missingResponse.status === 401) {
      logSuccess('Missing signature correctly rejected');
    } else {
      logWarning(`Expected 401 for missing signature, got ${missingResponse.status}`);
    }

    return true;
  } catch (error) {
    logError(`Signature validation test failed: ${error.message}`);
    return false;
  }
}

/**
 * Make HTTP request with timeout
 */
function makeHttpRequest(url, options) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https://');
    const client = isHttps ? https : http;
    
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: TEST_CONFIG.testTimeout,
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          body: data,
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

/**
 * Generate test report
 */
function generateTestReport(results) {
  log('\n📊 Test Report', 'magenta');
  log('=============', 'magenta');
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(Boolean).length;
  const failedTests = totalTests - passedTests;

  console.table(results);

  log(`\nTotal Tests: ${totalTests}`, 'blue');
  log(`Passed: ${passedTests}`, 'green');
  log(`Failed: ${failedTests}`, failedTests > 0 ? 'red' : 'green');
  log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`, 
      passedTests === totalTests ? 'green' : 'yellow');

  if (passedTests === totalTests) {
    log('\n🎉 All tests passed! Billing system is ready for production.', 'green');
  } else {
    log('\n⚠️  Some tests failed. Please review the issues above.', 'yellow');
  }
}

/**
 * Main test function
 */
async function main() {
  log('🧪 BuddyChip Pro - Billing System Integration Test', 'magenta');
  log('================================================', 'magenta');
  
  // Load environment variables
  require('dotenv').config({ path: '.env.local' });
  
  logInfo(`Testing webhook endpoint: ${TEST_CONFIG.webhookEndpoint}`);
  logInfo(`Test timeout: ${TEST_CONFIG.testTimeout}ms`);
  
  const results = {};
  
  // Run all tests
  try {
    results['Webhook Connectivity'] = await testWebhookEndpoint();
    results['Health Endpoints'] = await testHealthEndpoints();
    results['Signature Validation'] = await testWebhookSignatureValidation();
    results['Subscription Webhooks'] = await testSubscriptionWebhooks();
  } catch (error) {
    logError(`Test execution failed: ${error.message}`);
    results['Test Execution'] = false;
  }
  
  // Generate report
  generateTestReport(results);
  
  // Exit with appropriate code
  const allPassed = Object.values(results).every(Boolean);
  process.exit(allPassed ? 0 : 1);
}

// Run tests
if (require.main === module) {
  main().catch(error => {
    logError(`Test runner failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  testWebhookEndpoint,
  testSubscriptionWebhooks,
  testHealthEndpoints,
  testWebhookSignatureValidation,
};
