# BuddyChip Pro Backend Environment Configuration
# Copy this file to .env.local and fill in your actual values

# Convex Configuration (Generated automatically by bun dev:setup)
CONVEX_DEPLOYMENT=dev:your-deployment-name
CONVEX_URL=https://your-deployment.convex.cloud

# TwitterAPI.io Configuration (REQUIRED)
# Get your API key from https://twitterapi.io/
TWITTERAPI_IO_API_KEY=your_api_key_here

# Environment Configuration
NODE_ENV=development

# TwitterAPI.io Rate Limiting
TWITTERAPI_ENABLE_RATE_LIMITING=true
TWITTERAPI_REQUEST_DELAY=2000
TWITTERAPI_RETRY_ATTEMPTS=3
TWITTERAPI_RETRY_DELAY=5000
TWITTERAPI_EXPONENTIAL_BACKOFF=true

# Quota Management
TWITTERAPI_ENABLE_QUOTA_TRACKING=true
TWITTERAPI_DAILY_REQUEST_LIMIT=1000
TWITTERAPI_WARNING_THRESHOLD=80
TWITTERAPI_EMERGENCY_STOP_THRESHOLD=95

# Request Configuration
TWITTERAPI_DEFAULT_MAX_RESULTS=10
TWITTERAPI_MAX_ACCOUNTS_PER_BATCH=5
TWITTERAPI_MENTION_LOOKBACK_HOURS=24
TWITTERAPI_REQUEST_TIMEOUT=30000

# Monitoring and Logging
TWITTERAPI_ENABLE_USAGE_LOGGING=true
TWITTERAPI_ENABLE_COST_TRACKING=true
TWITTERAPI_LOG_RETENTION_DAYS=30

# Application Configuration
DEFAULT_SCRAPE_INTERVAL_MINUTES=120
MENTION_CHECK_INTERVAL_MINUTES=30
MAX_MENTIONS_PER_ACCOUNT=100

# Priority Settings
VERIFIED_ACCOUNT_WEIGHT=2.0
HIGH_PRIORITY_FOLLOWER_THRESHOLD=10000
MEDIUM_PRIORITY_FOLLOWER_THRESHOLD=1000

# Feature Flags
ENABLE_AI_ANALYSIS=true
ENABLE_MENTION_MONITORING=true
ENABLE_TWEET_SCRAPING=true
ENABLE_RESPONSE_GENERATION=true

# OpenRouter API for AI response generation (REQUIRED)
# Get your API key from https://openrouter.ai/
OPENROUTER_API_KEY=your_openrouter_api_key_here

# OpenAI API Key for DALL-E image generation and direct OpenAI models (OPTIONAL)
# Get your API key from https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# xAI API Key for Live Search and Grok models (OPTIONAL)
# Get your API key from https://x.ai/
XAI_API_KEY=your_xai_api_key_here

# ===================================================================
# AI MODEL CONFIGURATION - Advanced Model Selection
# ===================================================================
# Configure different models for different use cases and performance needs

# ===================================================================
# TEXT GENERATION MODELS
# ===================================================================
# Different text models for different performance/quality needs

# Ultra-fast models (< 2 seconds) - for real-time responses
TEXT_MODEL_ULTRA_FAST=google/gemini-2.5-flash-preview-05-20
TEXT_MODEL_ULTRA_FAST_BACKUP=anthropic/claude-3.5-haiku

# Fast models (< 8 seconds) - for standard responses  
TEXT_MODEL_FAST=google/gemini-2.5-flash-preview-05-20
TEXT_MODEL_FAST_BACKUP=openai/gpt-4o-mini

# Quality models (< 30 seconds) - for important content
TEXT_MODEL_QUALITY=google/gemini-2.5-pro-preview
TEXT_MODEL_QUALITY_BACKUP=anthropic/claude-3.5-sonnet

# Bulk processing models - for batch operations (cheap/free)
TEXT_MODEL_BULK=google/gemini-flash-1.5-8b:free
TEXT_MODEL_BULK_BACKUP=meta-llama/llama-3.1-8b-instruct:free

# Fallback models (comma-separated, in priority order)
TEXT_MODELS_FALLBACK=google/gemini-pro-1.5,meta-llama/llama-3.1-8b-instruct,mistralai/mistral-7b-instruct,google/gemini-2.0-flash-exp:free

# ===================================================================
# IMAGE GENERATION MODELS  
# ===================================================================
# Different image models for different quality/speed needs

# Fast image generation (< 10 seconds)
IMAGE_MODEL_FAST=dall-e-2
IMAGE_MODEL_FAST_BACKUP=stability-ai/stable-diffusion-xl

# Quality image generation (< 30 seconds) - best results
IMAGE_MODEL_QUALITY=dall-e-3
IMAGE_MODEL_QUALITY_BACKUP=midjourney/midjourney

# Bulk image generation - for generating many images
IMAGE_MODEL_BULK=stability-ai/stable-diffusion-xl
IMAGE_MODEL_BULK_BACKUP=dall-e-2

# Fallback image models
IMAGE_MODELS_FALLBACK=stability-ai/stable-diffusion-xl,dall-e-2

# ===================================================================
# VIDEO GENERATION MODELS (Future-proofing)
# ===================================================================
# Video models for potential future video generation features

# Primary video generation
VIDEO_MODEL_PRIMARY=openai/sora
VIDEO_MODEL_PRIMARY_BACKUP=runwayml/gen-2

# Fast video generation  
VIDEO_MODEL_FAST=runwayml/gen-2
VIDEO_MODEL_FAST_BACKUP=stability-ai/stable-video-diffusion

# Fallback video models
VIDEO_MODELS_FALLBACK=runwayml/gen-2,stability-ai/stable-video-diffusion

# ===================================================================
# EMBEDDING MODELS
# ===================================================================
# Models for semantic search and vector operations

# Fast embeddings (< 2 seconds) - for real-time search
EMBEDDING_MODEL_FAST=text-embedding-3-small
EMBEDDING_MODEL_FAST_BACKUP=text-embedding-ada-002

# Quality embeddings (< 10 seconds) - for important semantic analysis
EMBEDDING_MODEL_QUALITY=text-embedding-3-large
EMBEDDING_MODEL_QUALITY_BACKUP=text-embedding-3-small

# Bulk embeddings - for processing large datasets
EMBEDDING_MODEL_BULK=text-embedding-3-small
EMBEDDING_MODEL_BULK_BACKUP=text-embedding-ada-002

# Fallback embedding models
EMBEDDING_MODELS_FALLBACK=text-embedding-ada-002,text-embedding-3-small

# ===================================================================
# ANALYSIS MODELS
# ===================================================================
# Models for content analysis, viral detection, sentiment analysis

# Fast analysis (< 3 seconds) - for real-time analysis
ANALYSIS_MODEL_FAST=google/gemini-2.0-flash-exp:free
ANALYSIS_MODEL_FAST_BACKUP=google/gemini-flash-1.5-8b:free

# Quality analysis (< 15 seconds) - for important decisions
ANALYSIS_MODEL_QUALITY=google/gemini-pro-1.5
ANALYSIS_MODEL_QUALITY_BACKUP=anthropic/claude-3.5-haiku

# Bulk analysis - for processing many items
ANALYSIS_MODEL_BULK=google/gemini-flash-1.5-8b:free
ANALYSIS_MODEL_BULK_BACKUP=meta-llama/llama-3.1-8b-instruct:free

# Fallback analysis models
ANALYSIS_MODELS_FALLBACK=anthropic/claude-3.5-haiku,google/gemini-flash-1.5-8b:free

# ===================================================================
# MODEL SELECTION STRATEGY
# ===================================================================
# Default strategies for different operations
# Options: ultra_fast, fast, quality, bulk, auto

# Default text generation strategy
DEFAULT_TEXT_STRATEGY=fast

# Default image generation strategy  
DEFAULT_IMAGE_STRATEGY=quality

# Default embedding strategy
DEFAULT_EMBEDDING_STRATEGY=fast

# Default analysis strategy
DEFAULT_ANALYSIS_STRATEGY=fast

# Auto-strategy settings (when strategy=auto)
# Will choose model based on user context, time of day, etc.
AUTO_STRATEGY_BUSINESS_HOURS=quality
AUTO_STRATEGY_OFF_HOURS=fast
AUTO_STRATEGY_HIGH_LOAD=ultra_fast

# ===================================================================
# PERFORMANCE & TIMEOUT SETTINGS
# ===================================================================
# Timeouts in milliseconds for different performance tiers

# Ultra-fast tier (real-time responses)
ULTRA_FAST_TIMEOUT=2000
MAX_RETRIES_ULTRA_FAST=1

# Fast tier (standard responses)
FAST_TIMEOUT=8000
MAX_RETRIES_FAST=2

# Quality tier (best results)
QUALITY_TIMEOUT=30000
MAX_RETRIES_QUALITY=3

# Bulk tier (batch processing)
BULK_TIMEOUT=60000
MAX_RETRIES_BULK=1

# ===================================================================
# PROVIDER-SPECIFIC SETTINGS
# ===================================================================

# OpenRouter settings
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_SITE_URL=https://buddychip.pro
OPENROUTER_APP_NAME=BuddyChip Pro AI Agent

# OpenAI settings
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_ORG_ID=your-org-id-here

# ===================================================================
# COST OPTIMIZATION
# ===================================================================
# Settings to optimize for cost vs performance

# Enable cost-aware model selection
ENABLE_COST_OPTIMIZATION=true

# Maximum cost per request (in cents)
MAX_COST_PER_TEXT_REQUEST=5
MAX_COST_PER_IMAGE_REQUEST=20
MAX_COST_PER_VIDEO_REQUEST=100

# Daily budget limits (in dollars)
DAILY_BUDGET_TEXT=50
DAILY_BUDGET_IMAGE=20
DAILY_BUDGET_VIDEO=10

# Fallback to free models when budget exceeded
FALLBACK_TO_FREE_ON_BUDGET_LIMIT=true

# ===================================================================
# FEATURE FLAGS
# ===================================================================
# Enable/disable different model capabilities

# Text generation features
ENABLE_TEXT_STREAMING=true
ENABLE_TEXT_FUNCTION_CALLING=true
ENABLE_TEXT_VISION=false

# Image generation features
ENABLE_IMAGE_GENERATION=true
ENABLE_IMAGE_EDITING=false
ENABLE_IMAGE_VARIATIONS=true

# Video generation features (future)
ENABLE_VIDEO_GENERATION=false
ENABLE_VIDEO_EDITING=false

# Analysis features
ENABLE_VIRAL_DETECTION=true
ENABLE_SENTIMENT_ANALYSIS=true
ENABLE_CONTENT_MODERATION=true

# ===================================================================
# MONITORING & LOGGING
# ===================================================================
# Settings for tracking model usage and performance

# Enable detailed model usage logging
ENABLE_MODEL_USAGE_LOGGING=true

# Log performance metrics
ENABLE_PERFORMANCE_LOGGING=true

# Alert thresholds
ALERT_ON_HIGH_LATENCY=10000
ALERT_ON_HIGH_ERROR_RATE=10
ALERT_ON_BUDGET_THRESHOLD=80

# ===================================================================
# LEGACY COMPATIBILITY (Deprecated but supported)
# ===================================================================
# These will be used as fallbacks if the new model system is not configured

# Legacy OpenRouter settings (deprecated - use TEXT_MODEL_* instead)
OPENROUTER_DEFAULT_MODEL=openai/gpt-4o-mini
OPENROUTER_FALLBACK_MODELS=anthropic/claude-3.5-haiku,google/gemini-pro-1.5

# Legacy OpenAI settings (deprecated - use IMAGE_MODEL_* instead)
OPENAI_DEFAULT_IMAGE_MODEL=dall-e-3
OPENAI_RESPONSES_MODEL=gpt-image-1
OPENAI_TEST_MODEL=gpt-3.5-turbo

# Legacy embedding settings (deprecated - use EMBEDDING_MODEL_* instead)
DEFAULT_EMBEDDING_MODEL=text-embedding-3-small

# ===================================================================
# QUICK SETUP EXAMPLES
# ===================================================================

# DEVELOPMENT (Fast iteration, low cost)
# DEFAULT_TEXT_STRATEGY=ultra_fast
# DEFAULT_IMAGE_STRATEGY=fast
# ENABLE_COST_OPTIMIZATION=true
# FALLBACK_TO_FREE_ON_BUDGET_LIMIT=true

# PRODUCTION (Balance of quality and speed)
# DEFAULT_TEXT_STRATEGY=fast
# DEFAULT_IMAGE_STRATEGY=quality
# ENABLE_COST_OPTIMIZATION=true
# MAX_COST_PER_TEXT_REQUEST=3

# HIGH-QUALITY (Best results, higher cost)
# DEFAULT_TEXT_STRATEGY=quality
# DEFAULT_IMAGE_STRATEGY=quality
# ENABLE_COST_OPTIMIZATION=false
# MAX_RETRIES_QUALITY=5

# COST-CONSCIOUS (Minimize costs)
# DEFAULT_TEXT_STRATEGY=bulk
# DEFAULT_IMAGE_STRATEGY=fast
# ENABLE_COST_OPTIMIZATION=true
# FALLBACK_TO_FREE_ON_BUDGET_LIMIT=true
# MAX_COST_PER_TEXT_REQUEST=1