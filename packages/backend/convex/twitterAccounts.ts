import { mutation } from "./_generated/server";
import { v } from "convex/values";

export const addTwitterAccount = mutation({
  args: {
    handle: v.string(),
    displayName: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    isMonitoringEnabled: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    
    // Get user first
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();
    
    if (!user) {
      throw new Error("User not found");
    }
    
    // Check if account already exists for this user
    const existingAccount = await ctx.db
      .query("twitterAccounts")
      .withIndex("by_handle", (q) => q.eq("handle", args.handle))
      .first();
    
    if (existingAccount && existingAccount.userId === user._id) {
      throw new Error("Twitter account already added");
    }
    
    // Create new Twitter account
    const accountId = await ctx.db.insert("twitterAccounts", {
      userId: user._id,
      handle: args.handle,
      displayName: args.displayName || args.handle,
      isActive: args.isActive !== false,
      isMonitoringEnabled: args.isMonitoringEnabled !== false,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    
    return await ctx.db.get(accountId);
  },
});

export const removeTwitterAccount = mutation({
  args: {
    accountId: v.id("twitterAccounts"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    
    // Get the account to verify ownership
    const account = await ctx.db.get(args.accountId);
    if (!account) {
      throw new Error("Account not found");
    }
    
    // Get user to verify ownership
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();
    
    if (!user || account.userId !== user._id) {
      throw new Error("Not authorized to delete this account");
    }
    
    // Delete the account
    await ctx.db.delete(args.accountId);
    return { success: true };
  },
});