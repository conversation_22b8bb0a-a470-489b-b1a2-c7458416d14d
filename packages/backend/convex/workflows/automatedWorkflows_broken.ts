import { action } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";

/**
 * Complete automated workflow: Scrape → Analyze → Generate Responses
 */
export const runCompleteWorkflow = action({
  args: {
    handles: v.optional(v.array(v.string())),
    maxTweets: v.optional(v.number()),
    maxMentions: v.optional(v.number()),
    generateResponses: v.optional(v.boolean()),
    analyzeContent: v.optional(v.boolean()),
  },
  handler: async (ctx, args): Promise<any> => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) {
      throw new Error("User not found");
    }

    try {
      const results = {
        scraping: {
          tweets: { success: false, scraped: 0, errors: [] as string[] },
          mentions: { success: false, found: 0, stored: 0, errors: [] as string[] },
        },
        analysis: {
          tweets: { success: false, analyzed: 0, errors: [] as string[] },
          mentions: { success: false, processed: 0, errors: [] as string[] },
        },
        responses: {
          generated: 0,
          errors: [] as string[],
        },
        timing: {
          startTime: Date.now(),
          endTime: 0,
          duration: 0,
        },
      };

      console.log("Starting complete automated workflow...");

      // Step 1: Scrape Tweets
      console.log("Step 1: Scraping tweets...");
      try {
        const tweetScrapeResult = await ctx.runAction(api.workflows.automatedWorkflows.scrapeAllTweets, {
          handles: args.handles,
          maxTweets: args.maxTweets || 20,
        });

        results.scraping.tweets = {
          success: tweetScrapeResult.success,
          scraped: tweetScrapeResult.totalScraped,
          errors: tweetScrapeResult.errors,
        };
      } catch (error) {
        console.error("Tweet scraping failed:", error);
        results.scraping.tweets.errors.push(error instanceof Error ? error.message : "Unknown error");
      }

      // Step 2: Scrape Mentions
      console.log("Step 2: Scraping mentions...");
      try {
        const mentionScrapeResult = await ctx.runAction(api.twitterScraper.bulkScrapeMentions, {
          accounts: args.handles,
          hoursBack: 24,
        });

        results.scraping.mentions = {
          success: mentionScrapeResult.success,
          found: mentionScrapeResult.totalFound || 0,
          stored: mentionScrapeResult.totalStored || 0,
          errors: mentionScrapeResult.results?.filter((r: any) => !r.success).map((r: any) => r.error) || [],
        };
      } catch (error) {
        console.error("Mention scraping failed:", error);
        results.scraping.mentions.errors.push(error instanceof Error ? error.message : "Unknown error");
      }

      // Step 3: Analyze Content (if enabled)
      if (args.analyzeContent !== false) {
        console.log("Step 3: Analyzing content...");
        
        // Analyze tweets
        try {
          const tweetAnalysisResult = await ctx.runAction(api.workflows.automatedWorkflows.analyzePendingTweets, {
            limit: 50,
          });

          results.analysis.tweets = {
            success: tweetAnalysisResult.success,
            analyzed: tweetAnalysisResult.analyzed,
            errors: tweetAnalysisResult.errors,
          };
        } catch (error) {
          console.error("Tweet analysis failed:", error);
          results.analysis.tweets.errors.push(error instanceof Error ? error.message : "Unknown error");
        }

        // Analyze mentions
        try {
          const mentionAnalysisResult = await ctx.runAction(api.mentions.mentionMutations.bulkProcessMentions, {
            unprocessedOnly: true,
            maxToProcess: 50,
          });

          results.analysis.mentions = {
            success: mentionAnalysisResult.success,
            processed: mentionAnalysisResult.successful || 0,
            errors: mentionAnalysisResult.results?.filter((r: any) => !r.success).map((r: any) => r.error) || [],
          };
        } catch (error) {
          console.error("Mention analysis failed:", error);
          results.analysis.mentions.errors.push(error instanceof Error ? error.message : "Unknown error");
        }
      }

      // Step 4: Generate Responses (if enabled)
      if (args.generateResponses !== false) {
        console.log("Step 4: Generating responses...");
        try {
          const responseResult = await ctx.runAction(api.workflows.automatedWorkflows.generateResponsesForOpportunities, {
            limit: 20,
          });

          results.responses.generated = responseResult.generated;
          results.responses.errors = responseResult.errors;
        } catch (error) {
          console.error("Response generation failed:", error);
          results.responses.errors.push(error instanceof Error ? error.message : "Unknown error");
        }
      }

      // Calculate timing
      results.timing.endTime = Date.now();
      results.timing.duration = results.timing.endTime - results.timing.startTime;

      console.log(`Workflow completed in ${results.timing.duration}ms`);

      return {
        success: true,
        results,
        summary: {
          tweetsScraped: results.scraping.tweets.scraped,
          mentionsFound: results.scraping.mentions.stored,
          tweetsAnalyzed: results.analysis.tweets.analyzed,
          mentionsProcessed: results.analysis.mentions.processed,
          responsesGenerated: results.responses.generated,
          totalErrors: results.scraping.tweets.errors.length + 
            results.scraping.mentions.errors.length +
            results.analysis.tweets.errors.length +
            results.analysis.mentions.errors.length +
            results.responses.errors.length,
          duration: results.timing.duration,
        },
      };
    } catch (error) {
      console.error("Complete workflow failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        results: null,
        summary: null,
      };
    }
  },
});

/**
 * Scrape tweets for all monitored accounts
 */
export const scrapeAllTweets = action({
  args: {
    handles: v.optional(v.array(v.string())),
    maxTweets: v.optional(v.number()),
  },
  handler: async (ctx, args): Promise<any> => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) {
      throw new Error("User not found");
    }

    try {
      // Get accounts to scrape
      const userAccounts = await ctx.runQuery(api.userQueries.getActiveTwitterAccounts, {
        userId: user._id,
      });
      const accounts = args.handles && args.handles.length > 0
        ? userAccounts.filter((acc: any) => args.handles!.includes(acc.handle))
        : userAccounts.filter((acc: any) => acc.isActive);

      if (accounts.length === 0) {
        return {
          success: true,
          message: "No accounts to scrape",
          totalScraped: 0,
          errors: [],
          results: [],
        };
      }

      const results: any[] = [];
      let totalScraped: number = 0;
      const errors: string[] = [];

      for (const account of accounts) {
        try {
          console.log(`Scraping tweets for @${account.handle}...`);
          
          const result = await ctx.runAction(api.tweets.fetchTweetsFromAccount, {
            handle: account.handle,
            maxResults: args.maxTweets || 20,
          });

          if (result.success) {
            totalScraped += result.scraped;
            results.push({
              handle: account.handle,
              scraped: result.scraped,
              success: true,
            });
          } else {
            errors.push(`${account.handle}: ${result.error}`);
            results.push({
              handle: account.handle,
              scraped: 0,
              success: false,
              error: result.error,
            });
          }

          // Small delay to avoid rate limits
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
          const errorMsg = `${account.handle}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMsg);
          results.push({
            handle: account.handle,
            scraped: 0,
            success: false,
            error: errorMsg,
          });
        }
      }

      return {
        success: true,
        totalScraped,
        accountsProcessed: accounts.length,
        errors,
        results,
      };
    } catch (error) {
      console.error("Bulk tweet scraping failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        totalScraped: 0,
        errors: [error instanceof Error ? error.message : "Unknown error"],
        results: [],
      };
    }
  },
});

/**
 * Analyze all pending tweets
 */
export const analyzePendingTweets = action({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args): Promise<any> => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) {
      throw new Error("User not found");
    }

    try {
      // Get user's Twitter accounts
      const twitterAccounts = await ctx.runQuery(api.userQueries.getUserTwitterAccounts, {
        userId: user._id,
      });

      const accountIds = twitterAccounts.map((acc: any) => acc._id);

      // Get pending tweets
      const allTweets = await ctx.runQuery(api.userQueries.getTweetsByAccountIds, {
        accountIds,
        limit: args.limit || 50,
      });

      const pendingTweets = allTweets.filter((tweet: any) => tweet.analysisStatus === "pending");

      if (pendingTweets.length === 0) {
        return {
          success: true,
          analyzed: 0,
          errors: [],
          message: "No pending tweets to analyze",
        };
      }

      console.log(`Analyzing ${pendingTweets.length} pending tweets...`);

      let analyzed: number = 0;
      const errors: string[] = [];

      // Process tweets in smaller batches
      const batchSize = 5;
      for (let i = 0; i < pendingTweets.length; i += batchSize) {
        const batch = pendingTweets.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async (tweet: any) => {
          try {
            const result = await ctx.runAction(api.ai.tweetAnalysis.analyzeTweetWorthiness, {
              tweetId: tweet.tweetId,
              content: tweet.content,
              authorHandle: tweet.authorHandle,
              authorDisplayName: tweet.author,
              authorIsVerified: false, // This could be enhanced with actual data
              authorFollowerCount: 0,   // This could be enhanced with actual data
              engagement: tweet.engagement,
            });

            // Update tweet analysis status
            const shouldRespond = result.analysis?.shouldRespond || false;
            const analysisStatus = shouldRespond ? "response_worthy" : "analyzed";
            
            await ctx.runMutation(api.tweets.updateTweetAnalysis, {
              tweetId: tweet._id,
              analysisStatus,
              analysisScore: result.analysis?.confidence || 0,
              analysisReason: result.analysis?.reasons?.join(", ") || "AI analysis completed",
            });

            analyzed++;
            return { success: true, tweetId: tweet._id };
          } catch (error) {
            const errorMsg = `Tweet ${tweet._id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
            errors.push(errorMsg);
            return { success: false, tweetId: tweet._id, error: errorMsg };
          }
        });

        await Promise.all(batchPromises);

        // Small delay between batches
        if (i + batchSize < pendingTweets.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      return {
        success: true,
        analyzed,
        total: pendingTweets.length,
        errors,
      };
    } catch (error) {
      console.error("Tweet analysis failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        analyzed: 0,
        errors: [error instanceof Error ? error.message : "Unknown error"],
      };
    }
  },
});

/**
 * Generate responses for response opportunities
 */
export const generateResponsesForOpportunities = action({
  args: {
    limit: v.optional(v.number()),
    includeResponseWorthy: v.optional(v.boolean()),
    includeHighPriorityMentions: v.optional(v.boolean()),
  },
  handler: async (ctx, args): Promise<any> => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) {
      throw new Error("User not found");
    }

    try {
      let generated: number = 0;
      const errors: string[] = [];
      const limit = args.limit || 20;

      // Get response-worthy tweets
      if (args.includeResponseWorthy !== false) {
        const twitterAccounts = await ctx.runQuery(api.userQueries.getUserTwitterAccounts, {
          userId: user._id,
        });

        const accountIds = twitterAccounts.map((acc: any) => acc._id);

        const responseWorthyTweets = await ctx.runQuery(api.userQueries.getResponseWorthyTweets, {
          limit,
        });

        const userResponseWorthyTweets = responseWorthyTweets.filter((tweet: any) => 
          accountIds.includes(tweet.twitterAccountId)
        );

        for (const tweet of userResponseWorthyTweets.slice(0, Math.floor(limit / 2))) {
          try {
            // Check if we already have responses for this tweet
            const existingResponses = await ctx.runQuery(api.userQueries.getExistingResponsesForTarget, {
              targetType: "tweet",
              targetId: tweet._id,
            });

            if (existingResponses.length > 0) {
              continue; // Skip if we already have responses
            }

            const result = await ctx.runAction(api.responseGeneration.generateSingleResponse, {
              content: tweet.content,
              responseType: "reply",
              style: "professional",
              authorInfo: {
                handle: tweet.authorHandle,
                displayName: tweet.author,
              },
            });

            // Store the response
            await ctx.runMutation(api.responseMutations.storeResponse, {
              targetType: "tweet",
              targetId: tweet._id,
              content: result.content,
              style: result.style,
              confidence: result.confidence,
              generationModel: result.model,
              responseStrategy: result.strategy,
            });

            generated++;
          } catch (error) {
            errors.push(`Tweet ${tweet._id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        }
      }

      // Get high-priority mentions that should be responded to
      if (args.includeHighPriorityMentions !== false) {
        const twitterAccounts = await ctx.runQuery(api.userQueries.getUserTwitterAccounts, {
          userId: user._id,
        });

        const accountIds = twitterAccounts.map((acc: any) => acc._id);

        const allMentions = await ctx.runQuery(api.userQueries.getHighPriorityMentions, {
          limit,
        });

        const highPriorityMentions = allMentions.filter((mention: any) => 
          accountIds.includes(mention.monitoredAccountId) &&
          mention.aiAnalysisResult?.shouldRespond
        );

        for (const mention of highPriorityMentions.slice(0, Math.floor(limit / 2))) {
          try {
            // Check if we already have responses for this mention
            const existingResponses = await ctx.runQuery(api.userQueries.getExistingResponsesForTarget, {
              targetType: "mention",
              targetId: mention._id,
            });

            if (existingResponses.length > 0) {
              continue; // Skip if we already have responses
            }

            const result = await ctx.runAction(api.mentions.mentionMutations.generateMentionResponse, {
              mentionId: mention._id,
              responseStyle: "casual",
            });

            // Store the response
            await ctx.runMutation(api.responseMutations.storeResponse, {
              targetType: "mention",
              targetId: mention._id,
              content: result.response.content,
              style: result.response.style,
              confidence: result.response.confidence,
              generationModel: result.response.model,
              responseStrategy: result.response.strategy,
            });

            generated++;
          } catch (error) {
            errors.push(`Mention ${mention._id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        }
      }

      return {
        success: true,
        generated,
        errors,
        limit,
      };
    } catch (error) {
      console.error("Response generation for opportunities failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        generated: 0,
        errors: [error instanceof Error ? error.message : "Unknown error"],
      };
    }
  },
});

/**
 * Run scheduled workflow (for cron jobs)
 */
export const runScheduledWorkflow = action({
  args: {
    workflowType: v.union(
      v.literal("hourly"),
      v.literal("daily"),
      v.literal("weekly")
    ),
  },
  handler: async (ctx, args): Promise<any> => {
    console.log(`Starting ${args.workflowType} scheduled workflow...`);

    try {
      let result;

      switch (args.workflowType) {
        case "hourly":
          // Light scraping and mention checking
          result = await ctx.runAction(api.workflows.automatedWorkflows.runCompleteWorkflow, {
            maxTweets: 10,
            maxMentions: 50,
            generateResponses: true,
            analyzeContent: true,
          });
          break;

        case "daily":
          // Full scraping and analysis
          result = await ctx.runAction(api.workflows.automatedWorkflows.runCompleteWorkflow, {
            maxTweets: 50,
            maxMentions: 200,
            generateResponses: true,
            analyzeContent: true,
          });
          break;

        case "weekly":
          // Comprehensive analysis and cleanup
          result = await ctx.runAction(api.workflows.automatedWorkflows.runCompleteWorkflow, {
            maxTweets: 100,
            maxMentions: 500,
            generateResponses: true,
            analyzeContent: true,
          });

          // Additional weekly tasks could include:
          // - Generate embeddings for new content
          // - Clean up old data
          // - Generate performance reports
          break;

        default:
          throw new Error(`Unknown workflow type: ${args.workflowType}`);
      }

      console.log(`${args.workflowType} workflow completed:`, result.summary);

      return {
        success: true,
        workflowType: args.workflowType,
        result,
        executedAt: Date.now(),
      };
    } catch (error) {
      console.error(`${args.workflowType} workflow failed:`, error);
      return {
        success: false,
        workflowType: args.workflowType,
        error: error instanceof Error ? error.message : "Unknown error",
        executedAt: Date.now(),
      };
    }
  },
});