/**
 * 🚀 SMART BATCH WORKFLOWS
 * 
 * Intelligent workflow orchestration with advanced batching,
 * activity-based scheduling, and bandwidth optimization
 */

import { action, internalAction } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
import { ActivityBasedScheduler, SmartBatchProcessor, BATCH_CONFIG } from "../lib/intelligentBatching";
import { logOptimization } from "../lib/bandwidthMonitor";

/**
 * Smart data refresh workflow with intelligent batching
 * Combines multiple operations for maximum efficiency
 */
export const smartDataRefresh = action({
  args: {
    includeTweetScraping: v.optional(v.boolean()),
    includeMentionAnalysis: v.optional(v.boolean()),
    includeEngagementUpdates: v.optional(v.boolean()),
    batchSize: v.optional(v.number()),
    maxAccountsPerBatch: v.optional(v.number()),
    priorityAccountsFirst: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const startTime = Date.now();
    const batchProcessor = new SmartBatchProcessor(ctx.db);
    
    try {
      // Determine current activity level
      const activityLevel = await ActivityBasedScheduler.determineActivityLevel(ctx.db);
      const currentHour = new Date().getUTCHours();
      
      // Calculate optimal batch configuration
      const optimalConfig = ActivityBasedScheduler.calculateOptimalFrequency(
        "MENTION_MONITORING",
        activityLevel,
        currentHour
      );
      
      if (optimalConfig.shouldSkip) {
        console.log("Skipping smart data refresh due to low activity");
        return {
          success: true,
          skipped: true,
          reason: "Low activity period",
          activityLevel,
          executionTime: Date.now() - startTime,
        };
      }
      
      // Get all monitored accounts
      const accounts = await ctx.runQuery(api.twitterAccounts.getAllActiveAccounts, {});
      
      if (accounts.length === 0) {
        return {
          success: true,
          processed: 0,
          message: "No active accounts to process",
          executionTime: Date.now() - startTime,
        };
      }
      
      // Create optimized batches with priority sorting
      const batchConfig = BATCH_CONFIG.MENTION_MONITORING;
      const { priorityBatch, regularBatch } = await batchProcessor.createOptimizedBatches(
        accounts,
        batchConfig,
        Date.now()
      );
      
      const results = {
        tweetsScraped: 0,
        mentionsAnalyzed: 0,
        engagementUpdated: 0,
        errors: [] as string[],
      };
      
      // Process priority accounts first
      if (priorityBatch.length > 0) {
        const priorityResults = await batchProcessor.executeBatchWithRateLimit(
          priorityBatch,
          async (account) => {
            return await processSingleAccount(ctx, account, args);
          },
          { maxConcurrent: 3, delayBetweenItems: 2000 }
        );
        
        // Aggregate results
        priorityResults.results.forEach(result => {
          if (result) {
            results.tweetsScraped += result.tweetsScraped || 0;
            results.mentionsAnalyzed += result.mentionsAnalyzed || 0;
            results.engagementUpdated += result.engagementUpdated || 0;
          }
        });
        
        results.errors.push(...priorityResults.errors.map(e => e.error));
      }
      
      // Process regular accounts with adaptive batching
      if (regularBatch.length > 0 && activityLevel !== "low") {
        const regularResults = await batchProcessor.executeBatchWithRateLimit(
          regularBatch,
          async (account) => {
            return await processSingleAccount(ctx, account, args);
          },
          { maxConcurrent: 2, delayBetweenItems: 3000 }
        );
        
        // Aggregate results
        regularResults.results.forEach(result => {
          if (result) {
            results.tweetsScraped += result.tweetsScraped || 0;
            results.mentionsAnalyzed += result.mentionsAnalyzed || 0;
            results.engagementUpdated += result.engagementUpdated || 0;
          }
        });
        
        results.errors.push(...regularResults.errors.map(e => e.error));
      }
      
      const executionTime = Date.now() - startTime;
      
      // Log optimization metrics
      await logOptimization(
        ctx,
        "smartDataRefresh",
        accounts.length * 5000, // Estimated original size per account
        (results.tweetsScraped + results.mentionsAnalyzed) * 2000, // Actual processed size
        executionTime,
        accounts.length, // Records scanned
        results.tweetsScraped + results.mentionsAnalyzed, // Records returned
        "smart_batching",
        false // Not a cache hit
      );
      
      return {
        success: true,
        activityLevel,
        optimalConfig,
        accountsProcessed: priorityBatch.length + regularBatch.length,
        ...results,
        executionTime,
      };
      
    } catch (error) {
      console.error("Smart data refresh failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        executionTime: Date.now() - startTime,
      };
    }
  },
});

/**
 * Process a single account with multiple operations
 */
async function processSingleAccount(
  ctx: any,
  account: any,
  args: any
): Promise<{
  tweetsScraped?: number;
  mentionsAnalyzed?: number;
  engagementUpdated?: number;
} | null> {
  try {
    const results = {
      tweetsScraped: 0,
      mentionsAnalyzed: 0,
      engagementUpdated: 0,
    };
    
    // Tweet scraping (if enabled)
    if (args.includeTweetScraping) {
      try {
        const tweetResult = await ctx.runAction(api.tweets.fetchTweetsFromAccount, {
          handle: account.handle,
          maxResults: 10, // Limit to reduce bandwidth
        });
        results.tweetsScraped = tweetResult.scraped || 0;
      } catch (error) {
        console.error(`Tweet scraping failed for ${account.handle}:`, error);
      }
    }
    
    // Mention analysis (if enabled)
    if (args.includeMentionAnalysis) {
      try {
        const mentionResult = await ctx.runMutation(api.mentions.mentionMutations.checkAccountMentions, {
          accountId: account._id,
          maxResults: 20, // Limit to reduce bandwidth
        });
        results.mentionsAnalyzed = mentionResult.newMentions || 0;
      } catch (error) {
        console.error(`Mention analysis failed for ${account.handle}:`, error);
      }
    }
    
    // Engagement updates (if enabled)
    if (args.includeEngagementUpdates) {
      try {
        const engagementResult = await ctx.runAction(api.tweets.updateAccountEngagement, {
          accountId: account._id,
          maxTweets: 5, // Limit to reduce bandwidth
        });
        results.engagementUpdated = engagementResult.updated || 0;
      } catch (error) {
        console.error(`Engagement update failed for ${account.handle}:`, error);
      }
    }
    
    return results;
    
  } catch (error) {
    console.error(`Failed to process account ${account.handle}:`, error);
    return null;
  }
}

/**
 * Comprehensive optimization workflow for off-peak hours
 */
export const comprehensiveOptimization = action({
  args: {
    includeAIAnalysis: v.optional(v.boolean()),
    includeEmbeddingGeneration: v.optional(v.boolean()),
    includeDataOptimization: v.optional(v.boolean()),
    enableBandwidthReporting: v.optional(v.boolean()),
    maxProcessingTimeHours: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const startTime = Date.now();
    const maxProcessingTime = (args.maxProcessingTimeHours || 2) * 60 * 60 * 1000;
    
    try {
      const results = {
        aiAnalysisCompleted: 0,
        embeddingsGenerated: 0,
        dataOptimized: 0,
        bandwidthReport: null as any,
      };
      
      // AI Analysis optimization (if enabled and time allows)
      if (args.includeAIAnalysis && (Date.now() - startTime) < maxProcessingTime * 0.4) {
        try {
          const aiResult = await ctx.runAction(api.ai.ensembleOrchestrator.batchAnalyzeContent, {
            maxItems: 100,
            prioritizeUnanalyzed: true,
            enableParallelProcessing: true,
          });
          results.aiAnalysisCompleted = aiResult.analyzed || 0;
        } catch (error) {
          console.error("AI analysis optimization failed:", error);
        }
      }
      
      // Embedding generation (if enabled and time allows)
      if (args.includeEmbeddingGeneration && (Date.now() - startTime) < maxProcessingTime * 0.7) {
        try {
          const embeddingResult = await ctx.runAction(api.embeddings.embeddingGeneration.generateBatchEmbeddings, {
            maxItems: 200,
            prioritizeMissing: true,
            batchSize: 50,
          });
          results.embeddingsGenerated = embeddingResult.generated || 0;
        } catch (error) {
          console.error("Embedding generation failed:", error);
        }
      }
      
      // Data optimization (if enabled and time allows)
      if (args.includeDataOptimization && (Date.now() - startTime) < maxProcessingTime * 0.9) {
        try {
          const dataResult = await ctx.runMutation(api.admin.dataManagement.optimizeDataStructures, {
            includeIndexOptimization: true,
            includeCompressionOptimization: true,
            maxProcessingTime: 30 * 60 * 1000, // 30 minutes max
          });
          results.dataOptimized = dataResult.optimized || 0;
        } catch (error) {
          console.error("Data optimization failed:", error);
        }
      }
      
      // Bandwidth reporting (if enabled)
      if (args.enableBandwidthReporting) {
        try {
          results.bandwidthReport = await ctx.runQuery(api.analytics.bandwidthAnalytics.generateOptimizationReport, {
            timeRange: "24h",
            includeRecommendations: true,
          });
        } catch (error) {
          console.error("Bandwidth reporting failed:", error);
        }
      }
      
      const executionTime = Date.now() - startTime;
      
      // Log the comprehensive optimization
      await logOptimization(
        ctx,
        "comprehensiveOptimization",
        1000000, // Estimated original processing size
        results.aiAnalysisCompleted * 5000 + results.embeddingsGenerated * 1000, // Actual processed
        executionTime,
        results.aiAnalysisCompleted + results.embeddingsGenerated, // Records processed
        1, // Single optimization result
        "comprehensive_optimization",
        false
      );
      
      return {
        success: true,
        ...results,
        executionTime,
        timeRemaining: maxProcessingTime - executionTime,
      };
      
    } catch (error) {
      console.error("Comprehensive optimization failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        executionTime: Date.now() - startTime,
      };
    }
  },
});

/**
 * Weekly deep optimization for maximum efficiency
 */
export const weeklyDeepOptimization = action({
  args: {
    enableModelRetraining: v.optional(v.boolean()),
    optimizeDatabaseIndexes: v.optional(v.boolean()),
    analyzeBandwidthPatterns: v.optional(v.boolean()),
    generateWeeklyReport: v.optional(v.boolean()),
    maxProcessingTimeHours: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const startTime = Date.now();
    const maxProcessingTime = (args.maxProcessingTimeHours || 4) * 60 * 60 * 1000;
    
    try {
      const results = {
        modelsRetrained: 0,
        indexesOptimized: 0,
        bandwidthAnalysisCompleted: false,
        weeklyReportGenerated: false,
      };
      
      // Model retraining (if enabled)
      if (args.enableModelRetraining && (Date.now() - startTime) < maxProcessingTime * 0.5) {
        try {
          const retrainingResult = await ctx.runAction(api.ai.modelTraining.weeklyModelOptimization, {
            includeSentimentModels: true,
            includeResponseModels: true,
            maxTrainingTime: 60 * 60 * 1000, // 1 hour max
          });
          results.modelsRetrained = retrainingResult.retrained || 0;
        } catch (error) {
          console.error("Model retraining failed:", error);
        }
      }
      
      // Database index optimization (if enabled)
      if (args.optimizeDatabaseIndexes && (Date.now() - startTime) < maxProcessingTime * 0.7) {
        try {
          const indexResult = await ctx.runMutation(api.admin.databaseOptimization.optimizeWeeklyIndexes, {
            analyzeQueryPatterns: true,
            rebuildeFragmentedIndexes: true,
            maxOptimizationTime: 45 * 60 * 1000, // 45 minutes max
          });
          results.indexesOptimized = indexResult.optimized || 0;
        } catch (error) {
          console.error("Database index optimization failed:", error);
        }
      }
      
      // Bandwidth pattern analysis (if enabled)
      if (args.analyzeBandwidthPatterns && (Date.now() - startTime) < maxProcessingTime * 0.9) {
        try {
          await ctx.runAction(api.analytics.bandwidthAnalytics.weeklyPatternAnalysis, {
            includeUsagePatterns: true,
            includeCostAnalysis: true,
            generateOptimizationRecommendations: true,
          });
          results.bandwidthAnalysisCompleted = true;
        } catch (error) {
          console.error("Bandwidth pattern analysis failed:", error);
        }
      }
      
      // Weekly report generation (if enabled)
      if (args.generateWeeklyReport) {
        try {
          await ctx.runAction(api.analytics.reporting.generateWeeklyOptimizationReport, {
            includeBandwidthMetrics: true,
            includePerformanceMetrics: true,
            includeCostAnalysis: true,
            includeRecommendations: true,
          });
          results.weeklyReportGenerated = true;
        } catch (error) {
          console.error("Weekly report generation failed:", error);
        }
      }
      
      const executionTime = Date.now() - startTime;
      
      return {
        success: true,
        ...results,
        executionTime,
        timeRemaining: maxProcessingTime - executionTime,
      };
      
    } catch (error) {
      console.error("Weekly deep optimization failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        executionTime: Date.now() - startTime,
      };
    }
  },
});