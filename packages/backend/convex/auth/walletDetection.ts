import { mutation, query } from "../_generated/server";
import { v } from "convex/values";

/**
 * Detect and store wallet from Clerk authentication
 * This runs automatically when user logs in with wallet via Clerk
 */
export const detectAndStoreClerkWallet = mutation({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    // Get user from database
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // Check if Clerk provided Web3 wallet info
    // Note: This depends on Clerk's actual API structure
    const web3Info = (identity as { web3?: { address?: string; walletType?: string } }).web3;
    
    if (web3Info?.address) {
      const walletAddress = web3Info.address;
      const walletType = web3Info.walletType || "unknown";
      
      // Determine blockchain from address format
      const blockchain = detectBlockchainFromAddress(walletAddress);
      
      // Check if wallet already exists
      const existingWallet = await ctx.db
        .query("wallets")
        .withIndex("by_address", (q) => q.eq("address", walletAddress))
        .first();

      if (existingWallet && existingWallet.userId === user._id) {
        // Update existing wallet
        await ctx.db.patch(existingWallet._id, {
          lastUsedAt: Date.now(),
          verified: true, // Clerk verified it
          connectedVia: "clerk",
        });
        return existingWallet;
      } else if (!existingWallet) {
        // Create new wallet
        const walletId = await ctx.db.insert("wallets", {
          userId: user._id,
          address: walletAddress,
          blockchain,
          walletType,
          verified: true, // Clerk verified it
          isPrimary: true, // First wallet is primary
          connectedVia: "clerk",
          connectedAt: Date.now(),
          lastUsedAt: Date.now(),
        });

        // Update user's primary wallet if they don't have one
        if (!user.primaryWalletId) {
          await ctx.db.patch(user._id, {
            primaryWalletId: walletId,
          });
        }

        return await ctx.db.get(walletId);
      }
    }

    return null;
  },
});

/**
 * Get user's wallets with enhanced information
 */
export const getUserWallets = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      return [];
    }

    return await ctx.db
      .query("wallets")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .collect();
  },
});

export const getUserTwitterAccounts = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      return [];
    }

    return await ctx.db
      .query("twitterAccounts")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .collect();
  },
});

/**
 * Get user's primary wallet
 */
export const getPrimaryWallet = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user || !user.primaryWalletId) {
      return null;
    }

    return await ctx.db.get(user.primaryWalletId);
  },
});

/**
 * Set primary wallet for user
 */
export const setPrimaryWallet = mutation({
  args: {
    walletId: v.id("wallets"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // Verify wallet belongs to user
    const wallet = await ctx.db.get(args.walletId);
    if (!wallet || wallet.userId !== user._id) {
      throw new Error("Wallet not found or doesn't belong to user");
    }

    // Update all user's wallets to not be primary
    const userWallets = await ctx.db
      .query("wallets")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .collect();

    for (const w of userWallets) {
      await ctx.db.patch(w._id, { isPrimary: false });
    }

    // Set new primary wallet
    await ctx.db.patch(args.walletId, { isPrimary: true });

    // Update user's primary wallet reference
    await ctx.db.patch(user._id, {
      primaryWalletId: args.walletId,
    });

    return wallet;
  },
});


/**
 * Utility function to detect blockchain from wallet address format
 */
function detectBlockchainFromAddress(address: string): "ethereum" | "solana" | "polygon" | "base" {
  // Ethereum/EVM addresses start with 0x and are 42 characters long
  if (address.startsWith("0x") && address.length === 42) {
    // Default to ethereum for now, could be enhanced to detect specific EVM chains
    return "ethereum";
  }
  
  // Solana addresses are base58 encoded and typically 32-44 characters
  if (address.length >= 32 && address.length <= 44 && !address.startsWith("0x")) {
    // Basic validation for Solana address format
    const solanaRegex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
    if (solanaRegex.test(address)) {
      return "solana";
    }
  }
  
  // Default to ethereum for unknown formats
  return "ethereum";
}