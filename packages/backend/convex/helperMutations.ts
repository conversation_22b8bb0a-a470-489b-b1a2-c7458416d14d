import { mutation } from "./_generated/server";
import { v } from "convex/values";

// Simple validation helpers to avoid type instantiation depth issues
const jobStatus = v.union(
  v.literal("pending"),
  v.literal("running"), 
  v.literal("completed"),
  v.literal("failed"),
  v.literal("cancelled")
);

const priority = v.union(
  v.literal("high"),
  v.literal("medium"),
  v.literal("low")
);

const mentionType = v.union(
  v.literal("mention"),
  v.literal("reply"),
  v.literal("quote")
);

/**
 * Store a job result - SECURED
 * 🔐 SECURITY: Requires authentication and enforces user ownership
 */
export const storeJob = mutation({
  args: {
    jobType: v.string(),
    status: jobStatus,
    progress: v.number(),
    totalItems: v.optional(v.number()),
    processedItems: v.optional(v.number()),
    metadata: v.optional(v.object({})),
    results: v.optional(v.object({})),
  },
  handler: async (ctx, args) => {
    // 🔐 SECURITY: Require authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Authentication required");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // 🔐 SECURITY: Input validation
    if (!args.jobType || args.jobType.length === 0) {
      throw new Error("Job type is required");
    }
    if (args.progress < 0 || args.progress > 100) {
      throw new Error("Progress must be between 0 and 100");
    }

    const jobId = await ctx.db.insert("jobs", {
      jobType: args.jobType,
      status: args.status,
      userId: user._id, // 🔐 SECURITY: Always use authenticated user's ID
      progress: args.progress,
      totalItems: args.totalItems,
      processedItems: args.processedItems,
      metadata: args.metadata,
      results: args.results as any,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      completedAt: args.status === "completed" ? Date.now() : undefined,
    });
    
    return jobId;
  },
});

/**
 * Update job status - SECURED
 * 🔐 SECURITY: Requires authentication and verifies job ownership
 */
export const updateJobStatus = mutation({
  args: {
    jobId: v.id("jobs"),
    status: jobStatus,
    progress: v.optional(v.number()),
    processedItems: v.optional(v.number()),
    metadata: v.optional(v.object({})),
    results: v.optional(v.object({})),
  },
  handler: async (ctx, args) => {
    // 🔐 SECURITY: Require authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Authentication required");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // 🔐 SECURITY: Verify job ownership
    const job = await ctx.db.get(args.jobId);
    if (!job) {
      throw new Error("Job not found");
    }
    if (job.userId !== user._id) {
      throw new Error("Access denied: Job not owned by user");
    }

    // 🔐 SECURITY: Input validation
    if (args.progress !== undefined && (args.progress < 0 || args.progress > 100)) {
      throw new Error("Progress must be between 0 and 100");
    }

    await ctx.db.patch(args.jobId, {
      status: args.status,
      progress: args.progress,
      processedItems: args.processedItems,
      metadata: args.metadata,
      results: args.results as any,
      updatedAt: Date.now(),
      completedAt: args.status === "completed" ? Date.now() : undefined,
    });
  },
});

/**
 * Store xAI search results for analytics and history - SECURED
 * 🔐 SECURITY: Requires authentication and associates with user
 */
export const storeXAISearchResult = mutation({
  args: {
    searchType: v.string(),
    query: v.string(),
    content: v.string(),
    citations: v.array(v.string()),
    metadata: v.optional(v.object({})),
  },
  handler: async (ctx, args) => {
    // 🔐 SECURITY: Require authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Authentication required");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // 🔐 SECURITY: Input validation
    if (!args.searchType || args.searchType.length === 0) {
      throw new Error("Search type is required");
    }
    if (!args.query || args.query.length === 0) {
      throw new Error("Query is required");
    }
    if (args.query.length > 500) {
      throw new Error("Query too long (max 500 characters)");
    }
    if (args.content.length > 50000) {
      throw new Error("Content too long (max 50000 characters)");
    }
    if (args.citations.length > 20) {
      throw new Error("Too many citations (max 20)");
    }

    // 🔐 SECURITY: Associate search result with authenticated user
    const searchId = await ctx.db.insert("searchResults", {
      searchType: args.searchType,
      query: args.query,
      content: args.content,
      citations: args.citations,
      metadata: args.metadata,
      userId: user._id, // 🔐 SECURITY: Always associate with authenticated user
      createdAt: Date.now(),
    });
    
    return searchId;
  },
});

/**
 * Store mention with duplicate check
 */
export const storeMentionWithCheck = mutation({
  args: {
    mentionTweetId: v.string(),
    mentionContent: v.string(),
    mentionAuthor: v.string(),
    mentionAuthorHandle: v.string(),
    mentionAuthorFollowers: v.optional(v.number()),
    mentionAuthorVerified: v.optional(v.boolean()),
    monitoredAccountId: v.id("twitterAccounts"),
    mentionType: mentionType,
    originalTweetId: v.optional(v.string()),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
      views: v.optional(v.number()),
    }),
    priority: priority,
    url: v.string(),
    createdAt: v.number(),
  },
  handler: async (ctx, args) => {
    // Check if mention already exists
    const existingMention = await ctx.db
      .query("mentions")
      .withIndex("by_tweet_id", (q) => q.eq("mentionTweetId", args.mentionTweetId))
      .first();
    
    if (existingMention) {
      return null; // Mention already exists
    }
    
    // Create new mention
    const mentionId = await ctx.db.insert("mentions", {
      mentionTweetId: args.mentionTweetId,
      mentionContent: args.mentionContent,
      mentionAuthor: args.mentionAuthor,
      mentionAuthorHandle: args.mentionAuthorHandle,
      mentionAuthorFollowers: args.mentionAuthorFollowers || 0,
      mentionAuthorVerified: args.mentionAuthorVerified || false,
      monitoredAccountId: args.monitoredAccountId,
      mentionType: args.mentionType,
      originalTweetId: args.originalTweetId,
      engagement: args.engagement,
      priority: args.priority,
      url: args.url,
      isProcessed: false,
      isNotificationSent: false,
      createdAt: args.createdAt,
      discoveredAt: Date.now(),
    });
    
    return mentionId;
  },
});

/**
 * Update Twitter account last scraped timestamp
 */
export const updateTwitterAccountLastScraped = mutation({
  args: {
    accountId: v.id("twitterAccounts"),
    lastScrapedAt: v.number(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.accountId, {
      lastScrapedAt: args.lastScrapedAt,
    });
  },
});

// Bulk delete functions for admin cleanup
export const bulkDeleteTweets = mutation({
  args: {
    tweetIds: v.array(v.id("tweets")),
  },
  handler: async (ctx, args) => {
    for (const tweetId of args.tweetIds) {
      await ctx.db.delete(tweetId);
    }
  },
});

export const bulkDeleteMentions = mutation({
  args: {
    mentionIds: v.array(v.id("mentions")),
  },
  handler: async (ctx, args) => {
    for (const mentionId of args.mentionIds) {
      await ctx.db.delete(mentionId);
    }
  },
});

export const bulkDeleteResponses = mutation({
  args: {
    responseIds: v.array(v.id("responses")),
  },
  handler: async (ctx, args) => {
    for (const responseId of args.responseIds) {
      await ctx.db.delete(responseId);
    }
  },
});

export const deleteEmbedding = mutation({
  args: {
    embeddingId: v.string(),
    tableName: v.union(v.literal("tweetEmbeddings"), v.literal("mentionEmbeddings"), v.literal("responseEmbeddings"), v.literal("userContextEmbeddings")),
  },
  handler: async (ctx, args) => {
    // For now, just log the deletion since embedding tables might not exist
    console.log(`Would delete embedding ${args.embeddingId} from ${args.tableName}`);
  },
});