import { action } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
import { createXAIClient, formatDateForXAI, getRecentDateRange } from "../lib/xai_client";
import { checkRateLimit } from "../lib/rate_limiter";

/**
 * xAI Live Search for Mentions with AI Analysis
 */
export const xaiLiveSearchMentions = action({
  args: {
    handles: v.array(v.string()),
    dateRange: v.optional(v.object({
      from: v.string(),
      to: v.string()
    })),
    maxResults: v.optional(v.number()),
    additionalContext: v.optional(v.string()),
    includeAnalysis: v.optional(v.boolean())
  },
  handler: async (ctx, args) => {
    try {
      const xaiClient = createXAIClient();
      
      const result = await xaiClient.searchMentions(args.handles, {
        dateRange: args.dateRange || getRecentDateRange(24),
        maxResults: args.maxResults || 20,
        additionalContext: args.additionalContext
      });

      // Store the search result for analytics
      const searchId = await ctx.runMutation(api.helpers.mutations.storeXAISearchResult, {
        searchType: "mention_search",
        query: `mentions:${args.handles.join(',')}`,
        content: result.content,
        citations: result.citations,
        createdAt: Date.now(),
        metadata: {
          handles: args.handles,
          dateRange: args.dateRange,
          mentionCount: result.relevantMentions.length
        }
      });

      return {
        success: true,
        searchId,
        content: result.content,
        citations: result.citations,
        mentions: result.relevantMentions,
        handles: args.handles,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error("Error in xAI mention search:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        mentions: [],
        handles: args.handles
      };
    }
  }
});

/**
 * xAI Live Search for Trending Topics
 */
export const xaiSearchTrendingTopics = action({
  args: {
    topics: v.array(v.string()),
    dateRange: v.optional(v.object({
      from: v.string(),
      to: v.string()
    })),
    sources: v.optional(v.array(v.union(v.literal("web"), v.literal("x"), v.literal("news")))),
    maxResults: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    try {
      const xaiClient = createXAIClient();
      
      const result = await xaiClient.searchTrendingTopics(args.topics, {
        dateRange: args.dateRange || getRecentDateRange(24),
        sources: args.sources || ["x", "web", "news"],
        maxResults: args.maxResults || 15
      });

      // Store trending topics analysis
      const searchId = await ctx.runMutation(api.helpers.mutations.storeXAISearchResult, {
        searchType: "trending_search",
        query: `trends:${args.topics.join(',')}`,
        content: result.content,
        citations: result.citations,
        createdAt: Date.now(),
        metadata: {
          topics: args.topics,
          sources: args.sources,
          trendCount: result.trends.length
        }
      });

      return {
        success: true,
        searchId,
        content: result.content,
        citations: result.citations,
        trends: result.trends,
        topics: args.topics,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error("Error in xAI trending search:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        trends: [],
        topics: args.topics
      };
    }
  }
});

/**
 * xAI Real-time Content Analysis
 */
export const xaiRealTimeContentAnalysis = action({
  args: {
    content: v.string(),
    userContext: v.object({
      expertise: v.array(v.string()),
      interests: v.array(v.string()),
      brand: v.optional(v.string()),
      tone: v.optional(v.string())
    }),
    analysisType: v.union(
      v.literal("worthiness"),
      v.literal("sentiment"),
      v.literal("trends"),
      v.literal("comprehensive")
    )
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Apply rate limiting for expensive xAI operations
      await checkRateLimit(ctx, 'xaiRealTimeContentAnalysis', 'xaiSearch');

      const xaiClient = createXAIClient();
      
      let result;
      switch (args.analysisType) {
        case "worthiness":
          result = await xaiClient.analyzeContentWorthiness(args.content, args.userContext);
          break;
        case "comprehensive":
          // Run multiple analyses for comprehensive insights
          const worthiness = await xaiClient.analyzeContentWorthiness(args.content, args.userContext);
          
          result = {
            worthinessScore: worthiness.worthinessScore,
            reasoning: worthiness.reasoning,
            suggestedStrategy: worthiness.suggestedStrategy,
            liveContext: worthiness.liveContext,
            citations: worthiness.citations,
            analysisType: "comprehensive"
          };
          break;
        default:
          throw new Error(`Unsupported analysis type: ${args.analysisType}`);
      }

      // Store analysis result
      const analysisId = await ctx.runMutation(api.helpers.mutations.storeXAISearchResult, {
        searchType: "content_analysis",
        query: `analysis:${args.analysisType}`,
        content: result.liveContext || JSON.stringify(result),
        citations: result.citations || [],
        createdAt: Date.now(),
        metadata: {
          analysisType: args.analysisType,
          worthinessScore: result.worthinessScore || 0,
          contentLength: args.content.length
        }
      });

      return {
        success: true,
        analysisId,
        ...result,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error("Error in xAI content analysis:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        analysisType: args.analysisType
      };
    }
  }
});

/**
 * xAI Enhanced Live Search with Multiple Sources
 */
export const xaiEnhancedLiveSearch = action({
  args: {
    query: v.string(),
    sources: v.optional(v.array(v.union(
      v.literal("web"),
      v.literal("x"),
      v.literal("news"),
      v.literal("rss")
    ))),
    dateRange: v.optional(v.object({
      from: v.string(),
      to: v.string()
    })),
    maxResults: v.optional(v.number()),
    filters: v.optional(v.object({
      xHandles: v.optional(v.array(v.string())),
      websites: v.optional(v.array(v.string())),
      excludeWebsites: v.optional(v.array(v.string())),
      country: v.optional(v.string()),
      safeSearch: v.optional(v.boolean())
    })),
    analysisPrompt: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Apply rate limiting for expensive xAI operations
      await checkRateLimit(ctx, 'xaiEnhancedLiveSearch', 'xaiSearch');

      const xaiClient = createXAIClient();
      
      const searchSources = (args.sources || ["x", "web"]).map(sourceType => {
        const source: any = { type: sourceType };
        
        if (sourceType === "x" && args.filters?.xHandles) {
          source.x_handles = args.filters.xHandles;
        }
        
        if ((sourceType === "web" || sourceType === "news") && args.filters) {
          if (args.filters.country) source.country = args.filters.country;
          if (args.filters.websites) source.allowed_websites = args.filters.websites;
          if (args.filters.excludeWebsites) source.excluded_websites = args.filters.excludeWebsites;
          if (args.filters.safeSearch !== undefined) source.safe_search = args.filters.safeSearch;
        }
        
        return source;
      });

      const systemPrompt = args.analysisPrompt || 
        "You are an expert social media analyst and content researcher. Provide comprehensive analysis of search results including engagement patterns, trends, sentiment, and actionable insights. Structure your response with clear sections for findings, analysis, and recommendations.";

      const searchRequest = {
        messages: [
          {
            role: "system" as const,
            content: systemPrompt
          },
          {
            role: "user" as const,
            content: `Search and analyze: ${args.query}. Provide detailed insights, engagement metrics, trending patterns, and strategic recommendations.`
          }
        ],
        model: "grok-3-latest",
        search_parameters: {
          mode: "on" as const,
          sources: searchSources,
          from_date: args.dateRange?.from,
          to_date: args.dateRange?.to,
          max_search_results: args.maxResults || 20,
          return_citations: true
        },
        temperature: 0.3,
        max_tokens: 3000
      };

      const response = await xaiClient.liveSearch(searchRequest);

      // Store enhanced search result
      const searchId = await ctx.runMutation(api.helpers.mutations.storeXAISearchResult, {
        searchType: "enhanced_live_search",
        query: args.query,
        content: response.choices[0]?.message.content || "",
        citations: response.citations || [],
        createdAt: Date.now(),
        metadata: {
          sources: args.sources,
          filters: args.filters,
          dateRange: args.dateRange,
          tokensUsed: response.usage?.total_tokens || 0
        }
      });

      return {
        success: true,
        searchId,
        content: response.choices[0]?.message.content || "",
        citations: response.citations || [],
        usage: response.usage,
        query: args.query,
        sources: args.sources,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error("Error in xAI enhanced live search:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        query: args.query
      };
    }
  }
});

/**
 * Get Search History and Analytics
 */
export const getSearchAnalytics = action({
  args: {
    timeRange: v.optional(v.string()),
    searchType: v.optional(v.string()),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    try {
      // This would query stored search results for analytics
      // Implementation depends on your schema for storing search results
      
      return {
        success: true,
        analytics: {
          totalSearches: 0,
          popularQueries: [],
          averageResults: 0,
          topSources: [],
          timeDistribution: [],
          successRate: 0
        }
      };
    } catch (error) {
      console.error("Error getting search analytics:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  }
});
