import { cronJobs } from "convex/server";
import { api } from "./_generated/api";

/**
 * BuddyChip Pro - Automated Cron Jobs
 * 
 * These cron jobs run automated workflows to:
 * - Monitor Twitter accounts for new tweets
 * - Check for mentions and replies
 * - Analyze content with AI
 * - Generate response suggestions
 * - Maintain data freshness
 */

const crons = cronJobs();

// ============================================================================
// HOURLY WORKFLOWS - Frequent monitoring and quick response generation
// ============================================================================

/**
 * Every 15 minutes: Automated mention monitoring
 * Runs frequently to catch time-sensitive mentions and opportunities
 * Uses the automated monitoring system that works without user authentication
 */
crons.interval(
  "automated-mention-monitoring",
  { minutes: 15 }, // Every 15 minutes
  api.mentions.mentionMutations.automatedMentionMonitoring,
  { batchSize: 5 } // Process 5 accounts at a time
);

/**
 * Every 2 hours: Tweet scraping for monitored accounts
 * Keeps tweet data fresh without overwhelming the API
 * TEMPORARILY DISABLED - requires user authentication fix
 */
/*
crons.interval(
  "bi-hourly-tweet-scraping",
  { minutes: 120 }, // Every 2 hours
  api.workflows.automatedWorkflows.scrapeAllTweets,
  { maxTweets: 10 }
);
*/

// ============================================================================
// DAILY WORKFLOWS - Comprehensive analysis and maintenance
// ============================================================================

/**
 * Daily: Complete workflow with full analysis
 * Runs comprehensive scraping, analysis, and response generation
 * TEMPORARILY DISABLED - requires user authentication fix
 */
/*
crons.daily(
  "daily-complete-workflow",
  { hourUTC: 2, minuteUTC: 0 }, // 2:00 AM UTC daily
  api.workflows.automatedWorkflows.runScheduledWorkflow,
  { workflowType: "daily" }
);
*/

/**
 * Daily: Analyze pending tweets and mentions
 * Processes any content that hasn't been analyzed yet
 * TEMPORARILY DISABLED - requires user authentication fix
 */
/*
crons.daily(
  "daily-content-analysis",
  { hourUTC: 6, minuteUTC: 0 }, // 6:00 AM UTC daily
  api.workflows.automatedWorkflows.analyzePendingTweets,
  { limit: 100 }
);
*/

/**
 * Daily: Generate responses for opportunities
 * Creates AI responses for high-value tweets and mentions
 * TEMPORARILY DISABLED - requires user authentication fix
 */
/*
crons.daily(
  "daily-response-generation",
  { hourUTC: 8, minuteUTC: 0 }, // 8:00 AM UTC daily
  api.workflows.automatedWorkflows.generateResponsesForOpportunities,
  { 
    limit: 50,
    includeResponseWorthy: true,
    includeHighPriorityMentions: true
  }
);
*/

/**
 * Daily: Health check and system monitoring
 * Ensures all services are working properly
 */
crons.daily(
  "daily-health-check",
  { hourUTC: 12, minuteUTC: 0 }, // 12:00 PM UTC daily
  api.monitoring.healthCheck.runSystemHealthCheck,
  {}
);

// ============================================================================
// WEEKLY WORKFLOWS - Deep analysis and data maintenance
// ============================================================================

// Temporarily commented out - will be re-enabled after functions are implemented
/*
crons.weekly(
  "weekly-comprehensive-workflow",
  { dayOfWeek: "sunday", hourUTC: 1, minuteUTC: 0 }, // Sunday 1:00 AM UTC
  api.workflows.automatedWorkflows.runScheduledWorkflow,
  { workflowType: "weekly" }
);

crons.weekly(
  "weekly-embedding-generation",
  { dayOfWeek: "sunday", hourUTC: 3, minuteUTC: 0 }, // Sunday 3:00 AM UTC
  api.embeddings.embeddingGeneration.generateTweetEmbeddingsBatch,
  { batchSize: 100 }
);

crons.weekly(
  "weekly-data-cleanup",
  { dayOfWeek: "sunday", hourUTC: 5, minuteUTC: 0 }, // Sunday 5:00 AM UTC
  api.admin.dataManagement.cleanupOldData,
  { 
    retentionDays: 90,
    cleanupTweets: true,
    cleanupMentions: true,
    cleanupResponses: false // Keep generated responses
  }
);
*/

// ============================================================================
// CONDITIONAL WORKFLOWS - Based on activity levels
// ============================================================================

/**
 * Every 5 minutes during peak hours: Enhanced mention monitoring
 * More frequent checks during business hours when engagement is high
 * Peak hours: 9 AM - 6 PM EST (14:00 - 23:00 UTC)
 */
crons.interval(
  "peak-hours-mention-check",
  { minutes: 5 },
  api.mentions.mentionMutations.automatedMentionMonitoring,
  {
    batchSize: 10, // Process more accounts during peak hours
  }
);

/**
 * Hourly: High-priority account monitoring
 * Focus on accounts with high engagement or recent viral content
 */
crons.interval(
  "priority-account-monitoring",
  { minutes: 60 }, // Every hour
  api.mentions.mentionMutations.automatedMentionMonitoring,
  { 
    batchSize: 3,
    // This will be enhanced to focus on high-priority accounts in the future
  }
);

/**
 * Every 30 minutes: Quick health check
 * Lightweight monitoring to catch issues early
 */
crons.interval(
  "frequent-health-check",
  { minutes: 30 },
  api.monitoring.healthCheck.quickHealthCheck,
  {}
);

// ============================================================================
// 🚀 BANDWIDTH OPTIMIZATION - Cache maintenance and cleanup
// ============================================================================

/**
 * Every 2 hours: Automatic cache cleanup
 * Removes expired cache entries to maintain performance and reduce storage
 */
crons.interval(
  "cache-cleanup",
  { minutes: 120 }, // Every 2 hours
  api.admin.cacheManagement.autoCleanupExpiredCache,
  {}
);

/**
 * Daily: Cache optimization analysis
 * Analyzes cache performance and provides recommendations
 */
crons.daily(
  "daily-cache-optimization",
  { hourUTC: 4, minuteUTC: 0 }, // 4:00 AM UTC daily
  api.admin.cacheManagement.getCacheAnalytics,
  { hours: 24 }
);

// ============================================================================
// EXPORT CONFIGURATION
// ============================================================================

export default crons;

/**
 * Cron Job Configuration Summary:
 * 
 * HIGH FREQUENCY (15-60 minutes):
 * - Mention monitoring for time-sensitive opportunities
 * - Quick health checks
 * - Peak hours enhanced monitoring
 * 
 * MEDIUM FREQUENCY (2-24 hours):
 * - Tweet scraping for fresh content
 * - Content analysis and AI processing
 * - Response generation
 * - Daily maintenance tasks
 * 
 * LOW FREQUENCY (Weekly):
 * - Comprehensive analysis
 * - Data cleanup and optimization
 * - Embedding generation
 * - Strategic insights
 * 
 * TOTAL ESTIMATED COST PER DAY:
 * - ~50-100 API calls to Twitter/scraping services
 * - ~200-500 AI model calls for analysis/generation
 * - ~$1-5 per day in API costs (depending on usage)
 * 
 * SCALING NOTES:
 * - Increase frequency for high-engagement accounts
 * - Reduce frequency during low-activity periods
 * - Monitor API costs and adjust limits as needed
 * - Consider user-specific schedules for personalization
 */