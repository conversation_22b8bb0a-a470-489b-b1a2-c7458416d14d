import { query } from "./_generated/server";
import { v } from "convex/values";
import type { Doc } from "./_generated/dataModel";
import { logger } from "./lib/secure_logger";

// Type aliases for better readability and type safety
type User = Doc<"users">;
type TwitterAccount = Doc<"twitterAccounts">;
type Tweet = Doc<"tweets">;
type Mention = Doc<"mentions">;
type Response = Doc<"responses">;

// Security helper function to get authenticated user
async function getAuthenticatedUser(ctx: any): Promise<User> {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error("Authentication required");
  }
  
  const user: User | null = await ctx.db
    .query("users")
    .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
    .first();
    
  if (!user) {
    throw new Error("User not found");
  }
  
  return user;
}

// Security helper to get user's Twitter accounts
async function getUserAccounts(ctx: any, userId: string): Promise<TwitterAccount[]> {
  return await ctx.db
    .query("twitterAccounts")
    .withIndex("by_user", (q) => q.eq("userId", userId))
    .collect();
}

// Security helper to verify account ownership
function verifyAccountOwnership(userAccounts: TwitterAccount[], accountId: string): boolean {
  return userAccounts.some(account => account._id === accountId);
}

/**
 * Get test users for health checks (ADMIN ONLY)
 * 🔐 SECURITY: This function should only be accessible by administrators
 */
export const getTestUsers = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user
      const user = await getAuthenticatedUser(ctx);
      
      // 🔐 SECURITY: Check if user is admin (implement admin check based on your system)
      // For now, we'll restrict this function completely for security
      // TODO: Implement proper admin role checking if needed
      throw new Error("Access denied: Admin privileges required");
      
      // 🔐 SECURITY: Input validation
      const limit = Math.min(Math.max(args.limit || 10, 1), 50);
      
      const users = await ctx.db
        .query("users")
        .take(limit);
      
      // 🔐 SECURITY: Remove sensitive information before returning
      return users.map(user => ({
        _id: user._id,
        name: user.name,
        email: user.email,
        _creationTime: user._creationTime,
      }));
      
    } catch (error) {
      logger.error('Error in getTestUsers', error, { operation: 'getTestUsers' });
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied"))) {
        throw error;
      }
      return [];
    }
  },
});

/**
 * Get active Twitter accounts for current user only
 * 🔐 SECURITY: Only returns accounts owned by authenticated user
 */
export const getActiveTwitterAccounts = query({
  args: {},
  handler: async (ctx) => {
    try {
      // 🔐 SECURITY: Authenticate user
      const user = await getAuthenticatedUser(ctx);
      
      // 🔐 SECURITY: Get only user's accounts
      const userAccounts = await getUserAccounts(ctx, user._id);
      
      // Filter to only active accounts
      const activeAccounts = userAccounts.filter(account => account.isActive);
      
      return activeAccounts;
      
    } catch (error) {
      logger.error('Error in getActiveTwitterAccounts:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("User not found"))) {
        throw error;
      }
      return [];
    }
  },
});

/**
 * Get user settings for authenticated user only
 * 🔐 SECURITY: Users can only access their own settings
 */
export const getUserSettings = query({
  args: {
    userId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user
      const authenticatedUser = await getAuthenticatedUser(ctx);
      
      // 🔐 SECURITY: Use authenticated user's ID, ignore provided userId for security
      const targetUserId = authenticatedUser._id;
      
      // 🔐 SECURITY: Additional check if userId provided - must match authenticated user
      if (args.userId && args.userId !== targetUserId) {
        throw new Error("Access denied: Cannot access other user's settings");
      }
      
      const settings = await ctx.db
        .query("userSettings")
        .filter((q) => q.eq(q.field("userId"), targetUserId))
        .first();
      
      return settings;
      
    } catch (error) {
      logger.error('Error in getUserSettings:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied"))) {
        throw error;
      }
      return null;
    }
  },
});

/**
 * Get user by Clerk ID (INTERNAL USE ONLY)
 * 🔐 SECURITY: This function should only be used internally by the system
 */
export const getUserByClerkId = query({
  args: {
    clerkId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user first
      const authenticatedUser = await getAuthenticatedUser(ctx);
      
      // 🔐 SECURITY: Users can only look up their own Clerk ID
      if (args.clerkId !== authenticatedUser.clerkId) {
        throw new Error("Access denied: Cannot access other user's data");
      }
      
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
        .first();
      
      return user;
      
    } catch (error) {
      logger.error('Error in getUserByClerkId:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied"))) {
        throw error;
      }
      return null;
    }
  },
});

/**
 * Get current authenticated user
 */
export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();
    
    return user;
  },
});

/**
 * Get tweet by ID (user's tweets only)
 * 🔐 SECURITY: Users can only access tweets from their own accounts
 */
export const getTweetById = query({
  args: {
    tweetId: v.id("tweets"),
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user
      const user = await getAuthenticatedUser(ctx);
      
      const tweet = await ctx.db.get(args.tweetId);
      if (!tweet) {
        return null;
      }
      
      // 🔐 SECURITY: Verify tweet belongs to user's accounts
      const userAccounts = await getUserAccounts(ctx, user._id);
      const hasAccess = verifyAccountOwnership(userAccounts, tweet.twitterAccountId);
      
      if (!hasAccess) {
        throw new Error("Access denied: Tweet not owned by user");
      }
      
      return tweet;
      
    } catch (error) {
      logger.error('Error in getTweetById:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied"))) {
        throw error;
      }
      return null;
    }
  },
});

/**
 * Get mention by ID (user's mentions only)
 * 🔐 SECURITY: Users can only access mentions for their own accounts
 */
export const getMentionById = query({
  args: {
    mentionId: v.id("mentions"),
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user
      const user = await getAuthenticatedUser(ctx);
      
      const mention = await ctx.db.get(args.mentionId);
      if (!mention) {
        return null;
      }
      
      // 🔐 SECURITY: Verify mention belongs to user's accounts
      const userAccounts = await getUserAccounts(ctx, user._id);
      const hasAccess = verifyAccountOwnership(userAccounts, mention.monitoredAccountId);
      
      if (!hasAccess) {
        throw new Error("Access denied: Mention not owned by user");
      }
      
      return mention;
      
    } catch (error) {
      logger.error('Error in getMentionById:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied"))) {
        throw error;
      }
      return null;
    }
  },
});

/**
 * Get response by ID (user's responses only)
 * 🔐 SECURITY: Users can only access their own responses
 */
export const getResponseById = query({
  args: {
    responseId: v.id("responses"),
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user
      const user = await getAuthenticatedUser(ctx);
      
      const response = await ctx.db.get(args.responseId);
      if (!response) {
        return null;
      }
      
      // 🔐 SECURITY: Verify response belongs to user
      if (response.userId !== user._id) {
        throw new Error("Access denied: Response not owned by user");
      }
      
      return response;
      
    } catch (error) {
      logger.error('Error in getResponseById:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied"))) {
        throw error;
      }
      return null;
    }
  },
});

/**
 * Get Twitter account by handle (user's accounts only)
 * 🔐 SECURITY: Users can only access their own Twitter accounts
 */
export const getTwitterAccountByHandle = query({
  args: {
    handle: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user
      const user = await getAuthenticatedUser(ctx);
      
      // 🔐 SECURITY: Input validation
      const handle = args.handle.trim().toLowerCase();
      if (handle.length === 0) {
        throw new Error("Handle cannot be empty");
      }
      if (handle.length > 50) {
        throw new Error("Handle too long");
      }
      
      const account = await ctx.db
        .query("twitterAccounts")
        .withIndex("by_handle", (q) => q.eq("handle", handle))
        .first();
      
      if (!account) {
        return null;
      }
      
      // 🔐 SECURITY: Verify account belongs to user
      if (account.userId !== user._id) {
        throw new Error("Access denied: Account not owned by user");
      }
      
      return account;
      
    } catch (error) {
      logger.error('Error in getTwitterAccountByHandle:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied") || error.message.includes("Handle"))) {
        throw error;
      }
      return null;
    }
  },
});

/**
 * Get unprocessed mentions for current user only
 * 🔐 SECURITY: Users can only access mentions for their own accounts
 */
export const getUnprocessedMentions = query({
  args: {
    limit: v.optional(v.number()),
    monitoredAccountId: v.optional(v.id("twitterAccounts")),
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user
      const user = await getAuthenticatedUser(ctx);
      
      // 🔐 SECURITY: Input validation
      const limit = Math.min(Math.max(args.limit || 50, 1), 100);
      
      // 🔐 SECURITY: Get user's accounts
      const userAccounts = await getUserAccounts(ctx, user._id);
      
      // 🔐 SECURITY: If specific account requested, verify ownership
      if (args.monitoredAccountId) {
        const hasAccess = verifyAccountOwnership(userAccounts, args.monitoredAccountId);
        if (!hasAccess) {
          throw new Error("Access denied: Account not owned by user");
        }
      }
      
      // Get unprocessed mentions only for user's accounts
      let allUnprocessedMentions: Mention[] = [];
      for (const account of userAccounts) {
        const unprocessedMentions: Mention[] = await ctx.db
          .query("mentions")
          .withIndex("by_monitored_account", (q) => q.eq("monitoredAccountId", account._id))
          .filter(q => q.eq(q.field("isProcessed"), false))
          .order("desc")
          .collect();
        allUnprocessedMentions.push(...unprocessedMentions);
      }
      
      // Filter by specific account if requested
      let filteredMentions = args.monitoredAccountId 
        ? allUnprocessedMentions.filter(m => m.monitoredAccountId === args.monitoredAccountId)
        : allUnprocessedMentions;
      
      // Sort by priority (high first), then by creation date
      const sortedMentions = filteredMentions.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const priorityDiff = (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
        return priorityDiff !== 0 ? priorityDiff : b.createdAt - a.createdAt;
      });
      
      return sortedMentions.slice(0, limit);
      
    } catch (error) {
      logger.error('Error in getUnprocessedMentions:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied"))) {
        throw error;
      }
      return [];
    }
  },
});

/**
 * Get tweets by account IDs (user's accounts only)
 * 🔐 SECURITY: Users can only access tweets from their own accounts
 */
export const getTweetsByAccountIds = query({
  args: {
    accountIds: v.array(v.id("twitterAccounts")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user
      const user = await getAuthenticatedUser(ctx);
      
      // 🔐 SECURITY: Input validation
      const limit = Math.min(Math.max(args.limit || 100, 1), 200);
      
      if (args.accountIds.length === 0) {
        return [];
      }
      
      if (args.accountIds.length > 10) {
        throw new Error("Too many account IDs requested");
      }
      
      // 🔐 SECURITY: Get user's accounts and verify ownership
      const userAccounts = await getUserAccounts(ctx, user._id);
      const userAccountIds = new Set(userAccounts.map(account => account._id));
      
      // 🔐 SECURITY: Filter requested account IDs to only user's accounts
      const authorizedAccountIds = args.accountIds.filter(id => userAccountIds.has(id));
      
      if (authorizedAccountIds.length === 0) {
        throw new Error("Access denied: None of the requested accounts are owned by user");
      }
      
      const tweets = await ctx.db
        .query("tweets")
        .filter((q) => authorizedAccountIds.some(id => q.eq(q.field("twitterAccountId"), id)))
        .order("desc")
        .take(limit);
      
      return tweets;
      
    } catch (error) {
      logger.error('Error in getTweetsByAccountIds:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied") || error.message.includes("Too many"))) {
        throw error;
      }
      return [];
    }
  },
});

/**
 * Get mentions by account IDs (user's accounts only)
 * 🔐 SECURITY: Users can only access mentions for their own accounts
 */
export const getMentionsByAccountIds = query({
  args: {
    accountIds: v.array(v.id("twitterAccounts")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user
      const user = await getAuthenticatedUser(ctx);
      
      // 🔐 SECURITY: Input validation
      const limit = Math.min(Math.max(args.limit || 100, 1), 200);
      
      if (args.accountIds.length === 0) {
        return [];
      }
      
      if (args.accountIds.length > 10) {
        throw new Error("Too many account IDs requested");
      }
      
      // 🔐 SECURITY: Get user's accounts and verify ownership
      const userAccounts = await getUserAccounts(ctx, user._id);
      const userAccountIds = new Set(userAccounts.map(account => account._id));
      
      // 🔐 SECURITY: Filter requested account IDs to only user's accounts
      const authorizedAccountIds = args.accountIds.filter(id => userAccountIds.has(id));
      
      if (authorizedAccountIds.length === 0) {
        throw new Error("Access denied: None of the requested accounts are owned by user");
      }
      
      const mentions = await ctx.db
        .query("mentions")
        .filter((q) => authorizedAccountIds.some(id => q.eq(q.field("monitoredAccountId"), id)))
        .order("desc")
        .take(limit);
      
      return mentions;
      
    } catch (error) {
      logger.error('Error in getMentionsByAccountIds:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied") || error.message.includes("Too many"))) {
        throw error;
      }
      return [];
    }
  },
});

/**
 * Get all responses for authenticated user only
 * 🔐 SECURITY: Users can only access their own responses
 */
export const getAllResponses = query({
  args: {
    userId: v.optional(v.id("users")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user
      const authenticatedUser = await getAuthenticatedUser(ctx);
      
      // 🔐 SECURITY: Use authenticated user's ID, ignore provided userId for security
      const targetUserId = authenticatedUser._id;
      
      // 🔐 SECURITY: Additional check if userId provided - must match authenticated user
      if (args.userId && args.userId !== targetUserId) {
        throw new Error("Access denied: Cannot access other user's responses");
      }
      
      // 🔐 SECURITY: Input validation
      const limit = Math.min(Math.max(args.limit || 100, 1), 200);
      
      const responses = await ctx.db
        .query("responses")
        .withIndex("by_user", (q) => q.eq("userId", targetUserId))
        .order("desc")
        .take(limit);
      
      return responses;
      
    } catch (error) {
      logger.error('Error in getAllResponses:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied"))) {
        throw error;
      }
      return [];
    }
  },
});

// Placeholder embedding functions (since embedding tables may not exist)
export const getUserEmbeddings = query({
  args: {
    userId: v.id("users"),
    limit: v.number(),
  },
  handler: async (ctx, args) => {
    // Return empty array since embedding tables might not exist
    return [];
  },
});

export const getAllTweetEmbeddings = query({
  args: {
    limit: v.number(),
  },
  handler: async (ctx, args) => {
    // Return empty array since embedding tables might not exist
    return [];
  },
});

export const getAllMentionEmbeddings = query({
  args: {
    limit: v.number(),
  },
  handler: async (ctx, args) => {
    // Return empty array since embedding tables might not exist
    return [];
  },
});

export const getAllResponseEmbeddings = query({
  args: {
    limit: v.number(),
  },
  handler: async (ctx, args) => {
    // Return empty array since embedding tables might not exist
    return [];
  },
});

/**
 * Get all tweets for authenticated user only
 * 🔐 SECURITY: Users can only access tweets from their own accounts
 */
export const getAllTweets = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user
      const user = await getAuthenticatedUser(ctx);
      
      // 🔐 SECURITY: Input validation
      const limit = Math.min(Math.max(args.limit || 100, 1), 200);
      
      // 🔐 SECURITY: Get user's accounts
      const userAccounts = await getUserAccounts(ctx, user._id);
      const userAccountIds = userAccounts.map(account => account._id);
      
      if (userAccountIds.length === 0) {
        return [];
      }
      
      // Get tweets only from user's accounts
      const tweets = await ctx.db
        .query("tweets")
        .filter((q) => userAccountIds.some(id => q.eq(q.field("twitterAccountId"), id)))
        .order("desc")
        .take(limit);
      
      return tweets;
      
    } catch (error) {
      logger.error('Error in getAllTweets:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied"))) {
        throw error;
      }
      return [];
    }
  },
});