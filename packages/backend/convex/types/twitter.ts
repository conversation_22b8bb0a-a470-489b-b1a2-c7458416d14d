/**
 * TypeScript types and interfaces for Twitter API integration
 */

// Twitter API response structure from TwitterAPI.io
export interface TwitterIOTweet {
  id: string;
  text: string;
  author?: {
    id: string;
    name: string;
    username: string;
    profileImageUrl?: string;
    verified?: boolean;
    followersCount?: number;
    followingCount?: number;
    tweetCount?: number;
    description?: string;
    location?: string;
    createdAt?: string;
  };
  createdAt: string;
  conversationId?: string;
  inReplyToUserId?: string;
  referencedTweets?: Array<{
    type: "retweeted" | "quoted" | "replied_to";
    id: string;
  }>;
  metrics?: {
    likeCount?: number;
    retweetCount?: number;
    replyCount?: number;
    quoteCount?: number;
    viewCount?: number;
  };
  entities?: any;
  contextAnnotations?: any[];
  retweeted_tweet?: { id: string };
  quoted_tweet?: { id: string };
  retweetCount?: number;
  likeCount?: number;
  replyCount?: number;
  quoteCount?: number;
  viewCount?: number;
}

export interface TwitterUser {
  id: string;
  username: string;
  name: string;
  profile_image_url?: string;
  verified?: boolean;
  followers_count?: number;
  following_count?: number;
  tweet_count?: number;
  listed_count?: number;
  description?: string;
  location?: string;
  url?: string;
  created_at?: string;
  public_metrics?: {
    followers_count: number;
    following_count: number;
    tweet_count: number;
    listed_count: number;
  };
}

export interface TwitterTweet {
  id: string;
  text: string;
  author_id: string;
  created_at: string;
  conversation_id?: string;
  in_reply_to_user_id?: string;
  referenced_tweets?: Array<{
    type: "retweeted" | "quoted" | "replied_to";
    id: string;
  }>;
  public_metrics?: {
    retweet_count: number;
    like_count: number;
    reply_count: number;
    quote_count: number;
    bookmark_count?: number;
    impression_count?: number;
  };
  attachments?: {
    media_keys?: string[];
    poll_ids?: string[];
  };
  context_annotations?: Array<{
    domain: {
      id: string;
      name: string;
      description?: string;
    };
    entity: {
      id: string;
      name: string;
      description?: string;
    };
  }>;
  entities?: {
    urls?: Array<{
      start: number;
      end: number;
      url: string;
      expanded_url: string;
      display_url: string;
      unwound_url?: string;
    }>;
    hashtags?: Array<{
      start: number;
      end: number;
      tag: string;
    }>;
    mentions?: Array<{
      start: number;
      end: number;
      username: string;
      id: string;
    }>;
  };
}

export interface TwitterApiResponse<T> {
  data?: T;
  includes?: {
    users?: TwitterUser[];
    tweets?: TwitterTweet[];
  };
  meta?: {
    result_count?: number;
    next_token?: string;
    previous_token?: string;
    newest_id?: string;
    oldest_id?: string;
  };
  errors?: Array<{
    detail: string;
    title: string;
    resource_type: string;
    parameter: string;
    resource_id: string;
    type: string;
  }>;
}

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number; // Unix timestamp
}

// Configuration interfaces
export interface TwitterClientConfig {
  rateLimitHandling: {
    enableRateLimiting: boolean;
    defaultDelay: number;
    retryAttempts: number;
    retryDelay: number;
    exponentialBackoff: boolean;
  };
  scraping: {
    requestTimeout: number;
  };
  quotaManagement: {
    enableQuotaTracking: boolean;
  };
  monitoring: {
    enableUsageLogging: boolean;
  };
}

// Method options interfaces
export interface UserTweetsOptions {
  maxResults?: number;
  sinceId?: string;
  untilId?: string;
  excludeReplies?: boolean;
  excludeRetweets?: boolean;
}

export interface SearchMentionsOptions {
  maxResults?: number;
  sinceId?: string;
  untilId?: string;
  startTime?: string | number; // ISO 8601 format or Unix timestamp
  endTime?: string | number; // ISO 8601 format or Unix timestamp
  cursor?: string; // For pagination with TwitterAPI.io
}

export interface SearchTweetsOptions {
  maxResults?: number;
  cursor?: string;
  queryType?: "Latest" | "Top";
}

// Response interfaces
export interface UserTweetsResponse {
  tweets: TwitterTweet[];
  users: TwitterUser[];
  targetUser?: TwitterUser | null;
}

export interface TweetResponse {
  tweet: TwitterTweet | null;
  users: TwitterUser[];
}

export interface SearchResponse {
  tweets: TwitterTweet[];
  users: TwitterUser[];
  hasNextPage?: boolean;
  nextCursor?: string;
}

// Normalized data structures for database storage
export interface NormalizedTweet {
  tweetId: string;
  content: string;
  author: string;
  authorHandle: string;
  authorProfileImage?: string;
  isRetweet: boolean;
  retweetedFrom?: string;
  createdAt: number;
  scrapedAt: number;
  engagement: {
    likes: number;
    retweets: number;
    replies: number;
    views: number;
  };
  url: string;
  metadata: {
    isThread: boolean;
    hasMedia: boolean;
    language?: string;
  };
}

export type MentionPriority = "high" | "medium" | "low";

// Convex context type for dependency injection
export interface ConvexContext {
  runQuery: (name: string, args?: any) => Promise<any>;
  runMutation: (name: string, args: any) => Promise<any>;
}