/**
 * Secure Production Logger
 * 🔐 SECURITY: Prevents sensitive data leakage in production logs
 */

import { isProduction, isDevelopment, maskApiKey } from './production_config';

interface LogMeta {
  userId?: string;
  requestId?: string;
  operation?: string;
  duration?: number;
  [key: string]: any;
}

interface LogError {
  message: string;
  stack?: string;
  code?: string;
  statusCode?: number;
  [key: string]: any;
}

/**
 * Sensitive data patterns to scrub from logs
 */
const SENSITIVE_PATTERNS = [
  /password/i,
  /token/i,
  /api[_-]?key/i,
  /secret/i,
  /auth/i,
  /bearer/i,
  /credential/i,
  /private[_-]?key/i,
  /session/i,
];

/**
 * Sensitive field names to remove from objects
 */
const SENSITIVE_FIELDS = [
  'password',
  'token',
  'apiKey',
  'api_key',
  'secret',
  'authorization',
  'bearer',
  'credential',
  'privateKey',
  'private_key',
  'sessionId',
  'session_id',
  'clerkId',
  'clerk_id',
];

/**
 * Sanitizes objects by removing or masking sensitive data
 */
function sanitizeObject(obj: any, depth = 0): any {
  if (depth > 5) return '[Deep Object]'; // Prevent infinite recursion
  
  if (obj === null || obj === undefined) return obj;
  
  if (typeof obj === 'string') {
    return sanitizeString(obj);
  }
  
  if (typeof obj === 'number' || typeof obj === 'boolean') {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item, depth + 1));
  }
  
  if (typeof obj === 'object') {
    const sanitized: any = {};
    
    for (const [key, value] of Object.entries(obj)) {
      // Remove sensitive fields entirely
      if (SENSITIVE_FIELDS.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
        sanitized[key] = '[REDACTED]';
        continue;
      }
      
      // Check if key name suggests sensitive data
      if (SENSITIVE_PATTERNS.some(pattern => pattern.test(key))) {
        sanitized[key] = '[REDACTED]';
        continue;
      }
      
      // Recursively sanitize the value
      sanitized[key] = sanitizeObject(value, depth + 1);
    }
    
    return sanitized;
  }
  
  return obj;
}

/**
 * Sanitizes strings by masking potential sensitive data
 */
function sanitizeString(str: string): string {
  // Check if string looks like an API key or token
  if (str.length > 20 && /^[a-zA-Z0-9_-]+$/.test(str)) {
    return maskApiKey(str);
  }
  
  // Check for common sensitive patterns
  if (SENSITIVE_PATTERNS.some(pattern => pattern.test(str))) {
    return '[REDACTED]';
  }
  
  return str;
}

/**
 * Sanitizes error objects for production logging
 */
function sanitizeError(error: any): LogError {
  if (!error) return { message: 'Unknown error' };
  
  const sanitized: LogError = {
    message: error.message || 'Unknown error',
  };
  
  // In development, include more details
  if (isDevelopment()) {
    if (error.stack) sanitized.stack = error.stack;
    if (error.code) sanitized.code = error.code;
    if (error.statusCode) sanitized.statusCode = error.statusCode;
  } else {
    // In production, only include safe error details
    if (error.statusCode && typeof error.statusCode === 'number') {
      sanitized.statusCode = error.statusCode;
    }
    if (error.code && typeof error.code === 'string') {
      sanitized.code = error.code;
    }
  }
  
  return sanitized;
}

/**
 * Creates a timestamp string for logs
 */
function getTimestamp(): string {
  return new Date().toISOString();
}

/**
 * Secure logger that prevents sensitive data leakage
 */
export const logger = {
  /**
   * Log informational messages
   */
  info: (message: string, meta?: LogMeta): void => {
    const timestamp = getTimestamp();
    const sanitizedMeta = isProduction() ? sanitizeObject(meta) : meta;
    
    console.log(`[${timestamp}] INFO: ${message}`, sanitizedMeta ? sanitizedMeta : '');
  },

  /**
   * Log warning messages
   */
  warn: (message: string, meta?: LogMeta): void => {
    const timestamp = getTimestamp();
    const sanitizedMeta = isProduction() ? sanitizeObject(meta) : meta;
    
    console.warn(`[${timestamp}] WARN: ${message}`, sanitizedMeta ? sanitizedMeta : '');
  },

  /**
   * Log error messages with secure error handling
   */
  error: (message: string, error?: any, meta?: LogMeta): void => {
    const timestamp = getTimestamp();
    const sanitizedError = sanitizeError(error);
    const sanitizedMeta = isProduction() ? sanitizeObject(meta) : meta;
    
    console.error(`[${timestamp}] ERROR: ${message}`, {
      error: sanitizedError,
      meta: sanitizedMeta,
    });
  },

  /**
   * Log debug messages (only in development)
   */
  debug: (message: string, meta?: LogMeta): void => {
    if (isDevelopment()) {
      const timestamp = getTimestamp();
      console.debug(`[${timestamp}] DEBUG: ${message}`, meta || '');
    }
  },

  /**
   * Log API request/response information
   */
  api: (operation: string, duration: number, meta?: LogMeta): void => {
    const timestamp = getTimestamp();
    const logMeta = {
      operation,
      duration: `${duration}ms`,
      ...meta,
    };
    const sanitizedMeta = isProduction() ? sanitizeObject(logMeta) : logMeta;
    
    console.log(`[${timestamp}] API: ${operation}`, sanitizedMeta);
  },

  /**
   * Log authentication events
   */
  auth: (event: string, userId?: string, meta?: LogMeta): void => {
    const timestamp = getTimestamp();
    const logMeta = {
      event,
      userId: userId ? maskApiKey(userId) : undefined, // Mask user IDs in production
      ...meta,
    };
    const sanitizedMeta = isProduction() ? sanitizeObject(logMeta) : logMeta;
    
    console.log(`[${timestamp}] AUTH: ${event}`, sanitizedMeta);
  },

  /**
   * Log security events (always logged, even in production)
   */
  security: (event: string, meta?: LogMeta): void => {
    const timestamp = getTimestamp();
    const sanitizedMeta = sanitizeObject(meta); // Always sanitize security logs
    
    console.error(`[${timestamp}] SECURITY: ${event}`, sanitizedMeta);
  },
};

/**
 * Performance monitoring wrapper
 */
export function withPerformanceLogging<T>(
  operation: string,
  fn: () => Promise<T>
): Promise<T> {
  const startTime = Date.now();
  
  return fn()
    .then((result) => {
      const duration = Date.now() - startTime;
      logger.api(operation, duration, { status: 'success' });
      return result;
    })
    .catch((error) => {
      const duration = Date.now() - startTime;
      logger.api(operation, duration, { status: 'error' });
      logger.error(`Operation ${operation} failed`, error);
      throw error;
    });
}

/**
 * Export sanitization functions for use in other modules
 */
export const sanitization = {
  sanitizeObject,
  sanitizeString,
  sanitizeError,
  maskApiKey,
};