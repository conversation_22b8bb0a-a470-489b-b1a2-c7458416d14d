/**
 * Configuration file for BuddyChip Pro backend
 * 
 * This file centralizes all environment variable configuration
 * and provides type-safe access to configuration values.
 * 
 * TwitterAPI.io Integration Configuration:
 * - Get your API key from https://twitterapi.io/
 * - Configure rate limiting and quota management
 * - Set up proper environment variable validation
 */

export interface TwitterAPIConfig {
  apiKey: string;
  baseUrl: string;
  rateLimitHandling: {
    enableRateLimiting: boolean;
    defaultDelay: number; // milliseconds between requests
    retryAttempts: number;
    retryDelay: number; // milliseconds
    exponentialBackoff: boolean;
  };
  scraping: {
    defaultMaxResults: number;
    maxAccountsPerBatch: number;
    mentionLookbackHours: number;
    requestTimeout: number; // milliseconds
  };
  quotaManagement: {
    enableQuotaTracking: boolean;
    dailyRequestLimit: number;
    warningThreshold: number; // percentage (80 = 80%)
    emergencyStopThreshold: number; // percentage (95 = 95%)
  };
  monitoring: {
    enableUsageLogging: boolean;
    enableCostTracking: boolean;
    logRetentionDays: number;
  };
}

export interface AppConfig {
  twitterapi: TwitterAPIConfig;
  features: {
    enableAiAnalysis: boolean;
    enableMentionMonitoring: boolean;
    enableTweetScraping: boolean;
    enableResponseGeneration: boolean;
  };
  monitoring: {
    defaultScrapeInterval: number; // minutes
    mentionCheckInterval: number; // minutes
    maxMentionsPerAccount: number;
    priorityWeights: {
      verified: number;
      followerThresholds: {
        high: number;
        medium: number;
      };
    };
  };
  environment: {
    isDevelopment: boolean;
    isProduction: boolean;
    nodeEnv: string;
  };
}

/**
 * Load and validate configuration from environment variables
 */
export function loadConfig(): AppConfig {
  // Primary API key validation
  const twitterApiKey = process.env.TWITTERAPI_IO_API_KEY || process.env.TWEETIO_API_KEY;
  
  if (!twitterApiKey) {
    throw new Error(
      "TWITTERAPI_IO_API_KEY environment variable is required. " +
      "Get your API key from https://twitterapi.io/ and add it to your .env.local file or Convex environment variables. " +
      "See setup instructions in CLAUDE.md for detailed configuration."
    );
  }

  // Environment detection
  const nodeEnv = process.env.NODE_ENV || "development";
  const isDevelopment = nodeEnv === "development";
  const isProduction = nodeEnv === "production";

  return {
    twitterapi: {
      apiKey: twitterApiKey,
      baseUrl: process.env.TWITTERAPI_IO_BASE_URL || "https://api.twitterapi.io",
      rateLimitHandling: {
        enableRateLimiting: process.env.TWITTERAPI_ENABLE_RATE_LIMITING !== "false",
        defaultDelay: parseInt(process.env.TWITTERAPI_REQUEST_DELAY || (isDevelopment ? "2000" : "1000")),
        retryAttempts: parseInt(process.env.TWITTERAPI_RETRY_ATTEMPTS || "3"),
        retryDelay: parseInt(process.env.TWITTERAPI_RETRY_DELAY || "5000"),
        exponentialBackoff: process.env.TWITTERAPI_EXPONENTIAL_BACKOFF !== "false",
      },
      scraping: {
        defaultMaxResults: parseInt(process.env.TWITTERAPI_DEFAULT_MAX_RESULTS || (isDevelopment ? "10" : "20")),
        maxAccountsPerBatch: parseInt(process.env.TWITTERAPI_MAX_ACCOUNTS_PER_BATCH || (isDevelopment ? "5" : "10")),
        mentionLookbackHours: parseInt(process.env.TWITTERAPI_MENTION_LOOKBACK_HOURS || "24"),
        requestTimeout: parseInt(process.env.TWITTERAPI_REQUEST_TIMEOUT || "30000"),
      },
      quotaManagement: {
        enableQuotaTracking: process.env.TWITTERAPI_ENABLE_QUOTA_TRACKING !== "false",
        dailyRequestLimit: parseInt(process.env.TWITTERAPI_DAILY_REQUEST_LIMIT || (isProduction ? "10000" : "1000")),
        warningThreshold: parseInt(process.env.TWITTERAPI_WARNING_THRESHOLD || "80"),
        emergencyStopThreshold: parseInt(process.env.TWITTERAPI_EMERGENCY_STOP_THRESHOLD || "95"),
      },
      monitoring: {
        enableUsageLogging: process.env.TWITTERAPI_ENABLE_USAGE_LOGGING !== "false",
        enableCostTracking: process.env.TWITTERAPI_ENABLE_COST_TRACKING !== "false",
        logRetentionDays: parseInt(process.env.TWITTERAPI_LOG_RETENTION_DAYS || "30"),
      },
    },
    features: {
      enableAiAnalysis: process.env.ENABLE_AI_ANALYSIS !== "false",
      enableMentionMonitoring: process.env.ENABLE_MENTION_MONITORING !== "false",
      enableTweetScraping: process.env.ENABLE_TWEET_SCRAPING !== "false",
      enableResponseGeneration: process.env.ENABLE_RESPONSE_GENERATION !== "false",
    },
    monitoring: {
      defaultScrapeInterval: parseInt(process.env.DEFAULT_SCRAPE_INTERVAL_MINUTES || (isDevelopment ? "120" : "60")),
      mentionCheckInterval: parseInt(process.env.MENTION_CHECK_INTERVAL_MINUTES || (isDevelopment ? "30" : "15")),
      maxMentionsPerAccount: parseInt(process.env.MAX_MENTIONS_PER_ACCOUNT || "100"),
      priorityWeights: {
        verified: parseFloat(process.env.VERIFIED_ACCOUNT_WEIGHT || "2.0"),
        followerThresholds: {
          high: parseInt(process.env.HIGH_PRIORITY_FOLLOWER_THRESHOLD || "10000"),
          medium: parseInt(process.env.MEDIUM_PRIORITY_FOLLOWER_THRESHOLD || "1000"),
        },
      },
    },
    environment: {
      isDevelopment,
      isProduction,
      nodeEnv,
    },
  };
}

/**
 * Validate that all required environment variables are present and properly formatted
 */
export function validateConfig(): { valid: boolean; errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check for required API key
  const twitterApiKey = process.env.TWITTERAPI_IO_API_KEY || process.env.TWEETIO_API_KEY;
  if (!twitterApiKey) {
    errors.push("TWITTERAPI_IO_API_KEY is required. Get your API key from https://twitterapi.io/");
  } else {
    // Basic API key format validation
    if (twitterApiKey.length < 20) {
      warnings.push("TwitterAPI.io API key appears to be too short. Please verify your key.");
    }
    if (!process.env.TWITTERAPI_IO_API_KEY && process.env.TWEETIO_API_KEY) {
      warnings.push("Using legacy TWEETIO_API_KEY. Consider updating to TWITTERAPI_IO_API_KEY.");
    }
  }

  // Validate numeric environment variables
  const numericEnvVars = [
    { key: 'TWITTERAPI_REQUEST_DELAY', min: 100, max: 10000 },
    { key: 'TWITTERAPI_RETRY_ATTEMPTS', min: 1, max: 10 },
    { key: 'TWITTERAPI_RETRY_DELAY', min: 1000, max: 60000 },
    { key: 'TWITTERAPI_DEFAULT_MAX_RESULTS', min: 1, max: 100 },
    { key: 'TWITTERAPI_MAX_ACCOUNTS_PER_BATCH', min: 1, max: 50 },
    { key: 'TWITTERAPI_MENTION_LOOKBACK_HOURS', min: 1, max: 168 },
    { key: 'TWITTERAPI_REQUEST_TIMEOUT', min: 5000, max: 120000 },
    { key: 'TWITTERAPI_DAILY_REQUEST_LIMIT', min: 100, max: 100000 },
    { key: 'TWITTERAPI_WARNING_THRESHOLD', min: 50, max: 99 },
    { key: 'TWITTERAPI_EMERGENCY_STOP_THRESHOLD', min: 70, max: 100 },
    { key: 'TWITTERAPI_LOG_RETENTION_DAYS', min: 1, max: 365 },
    { key: 'DEFAULT_SCRAPE_INTERVAL_MINUTES', min: 5, max: 1440 },
    { key: 'MENTION_CHECK_INTERVAL_MINUTES', min: 1, max: 720 },
    { key: 'MAX_MENTIONS_PER_ACCOUNT', min: 10, max: 1000 },
    { key: 'HIGH_PRIORITY_FOLLOWER_THRESHOLD', min: 1000, max: ******** },
    { key: 'MEDIUM_PRIORITY_FOLLOWER_THRESHOLD', min: 100, max: 1000000 },
  ];

  for (const { key, min, max } of numericEnvVars) {
    const value = process.env[key];
    if (value) {
      const numValue = parseInt(value);
      if (isNaN(numValue)) {
        errors.push(`${key} must be a valid number, got: ${value}`);
      } else if (numValue < min || numValue > max) {
        warnings.push(`${key} value ${numValue} is outside recommended range ${min}-${max}`);
      }
    }
  }

  // Validate percentage values
  const percentageEnvVars = ['TWITTERAPI_WARNING_THRESHOLD', 'TWITTERAPI_EMERGENCY_STOP_THRESHOLD'];
  for (const envVar of percentageEnvVars) {
    const value = process.env[envVar];
    if (value) {
      const numValue = parseInt(value);
      if (!isNaN(numValue) && (numValue < 0 || numValue > 100)) {
        errors.push(`${envVar} must be between 0 and 100, got: ${numValue}`);
      }
    }
  }

  // Validate float values
  const floatEnvVars = ['VERIFIED_ACCOUNT_WEIGHT'];
  for (const envVar of floatEnvVars) {
    const value = process.env[envVar];
    if (value && isNaN(parseFloat(value))) {
      errors.push(`${envVar} must be a valid decimal number, got: ${value}`);
    }
  }

  // Environment-specific validations
  const nodeEnv = process.env.NODE_ENV || "development";
  if (nodeEnv === "production") {
    if (!process.env.TWITTERAPI_IO_API_KEY) {
      errors.push("TWITTERAPI_IO_API_KEY must be explicitly set in production (not legacy TWEETIO_API_KEY)");
    }
    
    const dailyLimit = parseInt(process.env.TWITTERAPI_DAILY_REQUEST_LIMIT || "10000");
    if (dailyLimit < 1000) {
      warnings.push("Daily request limit appears low for production environment");
    }
  }

  // Cross-validation
  const warningThreshold = parseInt(process.env.TWITTERAPI_WARNING_THRESHOLD || "80");
  const emergencyThreshold = parseInt(process.env.TWITTERAPI_EMERGENCY_STOP_THRESHOLD || "95");
  if (!isNaN(warningThreshold) && !isNaN(emergencyThreshold) && warningThreshold >= emergencyThreshold) {
    errors.push("Warning threshold must be lower than emergency stop threshold");
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Get feature flags
 */
export function getFeatureFlags() {
  const config = loadConfig();
  return config.features;
}

/**
 * Get TwitterAPI.io configuration
 */
export function getTwitterAPIConfig(): TwitterAPIConfig {
  const config = loadConfig();
  return config.twitterapi;
}

/**
 * Legacy function name for backwards compatibility
 * @deprecated Use getTwitterAPIConfig instead
 */
export function getTweetIOConfig(): TwitterAPIConfig {
  return getTwitterAPIConfig();
}

/**
 * Legacy function name for backwards compatibility
 * @deprecated Use getTwitterAPIConfig instead
 */
export function getTwitterConfig(): TwitterAPIConfig {
  return getTwitterAPIConfig();
}

/**
 * Get environment-specific configuration
 */
export function getEnvironmentConfig() {
  const config = loadConfig();
  return config.environment;
}

/**
 * Check if we're in development mode
 */
export function isDevelopment(): boolean {
  return getEnvironmentConfig().isDevelopment;
}

/**
 * Check if we're in production mode
 */
export function isProduction(): boolean {
  return getEnvironmentConfig().isProduction;
}

/**
 * Get API usage and quota information
 */
export function getQuotaConfig() {
  return getTwitterAPIConfig().quotaManagement;
}

/**
 * Get monitoring configuration
 */
export function getAPIMonitoringConfig() {
  return getTwitterAPIConfig().monitoring;
}

/**
 * Get monitoring configuration  
 */
export function getMonitoringConfig() {
  const config = loadConfig();
  return config.monitoring;
}