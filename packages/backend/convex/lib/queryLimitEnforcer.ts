/**
 * 🚀 QUERY LIMIT ENFORCEMENT SYSTEM
 * 
 * Advanced query protection to prevent runaway bandwidth consumption
 * and enforce strict limits on all database operations
 */

import { GenericDatabaseReader } from "convex/server";

/**
 * Global query limits to prevent bandwidth disasters
 */
export const QUERY_LIMITS = {
  // Absolute maximum limits (emergency circuit breakers)
  ABSOLUTE_MAX: {
    QUERY_TAKE_LIMIT: 2000,        // Never allow more than 2000 records in a single query
    COLLECT_PROHIBITION: true,      // Ban .collect() calls entirely
    MAX_CONCURRENT_QUERIES: 10,     // Max concurrent database queries per operation
    MAX_EXECUTION_TIME: 30000,      // Max 30 seconds per query operation
  },
  
  // Operation-specific limits
  DASHBOARD: {
    MAX_ACCOUNTS: 50,               // Max user accounts to process
    MAX_TWEETS_PER_ACCOUNT: 500,    // Max tweets per account for stats
    MAX_MENTIONS_PER_ACCOUNT: 300,  // Max mentions per account for stats
    MAX_RESPONSES: 200,             // Max responses for user stats
  },
  
  MENTIONS: {
    MAX_RECENT_MENTIONS: 100,       // Max mentions in recent queries
    MAX_UNPROCESSED_MENTIONS: 200,  // Max unprocessed mentions to return
    MAX_SEARCH_RESULTS: 50,         // Max mention search results
    MAX_STATS_MENTIONS: 500,        // Max mentions for statistics calculation
  },
  
  TWEETS: {
    MAX_RECENT_TWEETS: 200,         // Max tweets in recent queries
    MAX_ACCOUNT_TWEETS: 100,        // Max tweets per account query
    MAX_SEARCH_RESULTS: 50,         // Max tweet search results
    MAX_STATS_TWEETS: 1000,         // Max tweets for statistics calculation
  },
  
  ANALYTICS: {
    MAX_EVENTS: 1000,               // Max analytics events per query
    MAX_LOGS: 500,                  // Max bandwidth logs per query
    MAX_TIME_RANGE_DAYS: 90,        // Max time range for analytics queries
  },
  
  CACHE: {
    MAX_CACHE_ENTRIES: 10000,       // Max cache entries to scan
    MAX_CLEANUP_BATCH: 1000,        // Max cache entries to cleanup per batch
    MAX_STATS_ENTRIES: 5000,        // Max cache entries for statistics
  },
} as const;

/**
 * Query limit enforcer with automatic protection
 */
export class QueryLimitEnforcer {
  private static activeQueries = new Map<string, number>();
  private static queryStartTimes = new Map<string, number>();
  
  /**
   * Create a protected query with automatic limits
   */
  static createLimitedQuery<T>(
    db: GenericDatabaseReader<any>,
    tableName: string,
    operationType: keyof typeof QUERY_LIMITS,
    operationId: string = Math.random().toString(36)
  ): ProtectedQuery<T> {
    return new ProtectedQuery<T>(db, tableName, operationType, operationId);
  }
  
  /**
   * Enforce absolute maximum take limit
   */
  static enforceTakeLimit(requestedLimit: number, operationType: keyof typeof QUERY_LIMITS): number {
    const maxLimit = QUERY_LIMITS[operationType as keyof typeof QUERY_LIMITS];
    
    // Get the max limit for this operation type or use absolute max
    let operationMax = QUERY_LIMITS.ABSOLUTE_MAX.QUERY_TAKE_LIMIT;
    
    if (typeof maxLimit === 'object' && 'MAX_RECENT_MENTIONS' in maxLimit) {
      // Mentions operations
      if (operationType === 'MENTIONS') {
        operationMax = Math.min(operationMax, maxLimit.MAX_RECENT_MENTIONS || operationMax);
      }
    } else if (typeof maxLimit === 'object' && 'MAX_RECENT_TWEETS' in maxLimit) {
      // Tweet operations  
      if (operationType === 'TWEETS') {
        operationMax = Math.min(operationMax, maxLimit.MAX_RECENT_TWEETS || operationMax);
      }
    }
    
    const enforcedLimit = Math.min(requestedLimit, operationMax);
    
    if (enforcedLimit < requestedLimit) {
      console.warn(`Query limit enforced: requested ${requestedLimit}, limited to ${enforcedLimit} for ${operationType}`);
    }
    
    return enforcedLimit;
  }
  
  /**
   * Check if .collect() is prohibited
   */
  static isCollectProhibited(): boolean {
    return QUERY_LIMITS.ABSOLUTE_MAX.COLLECT_PROHIBITION;
  }
  
  /**
   * Track query execution for monitoring
   */
  static trackQueryStart(operationId: string): void {
    const currentActive = this.activeQueries.get(operationId) || 0;
    this.activeQueries.set(operationId, currentActive + 1);
    this.queryStartTimes.set(operationId, Date.now());
    
    // Check concurrent query limits
    const totalActive = Array.from(this.activeQueries.values()).reduce((sum, count) => sum + count, 0);
    if (totalActive > QUERY_LIMITS.ABSOLUTE_MAX.MAX_CONCURRENT_QUERIES) {
      console.warn(`High concurrent query count: ${totalActive} active queries`);
    }
  }
  
  /**
   * Track query completion
   */
  static trackQueryEnd(operationId: string): number {
    const startTime = this.queryStartTimes.get(operationId);
    const executionTime = startTime ? Date.now() - startTime : 0;
    
    // Update active query count
    const currentActive = this.activeQueries.get(operationId) || 0;
    if (currentActive > 0) {
      this.activeQueries.set(operationId, currentActive - 1);
    }
    
    // Clean up tracking
    this.queryStartTimes.delete(operationId);
    
    // Check execution time limits
    if (executionTime > QUERY_LIMITS.ABSOLUTE_MAX.MAX_EXECUTION_TIME) {
      console.warn(`Slow query detected: ${executionTime}ms for operation ${operationId}`);
    }
    
    return executionTime;
  }
  
  /**
   * Get current query statistics
   */
  static getQueryStats(): {
    activeQueries: number;
    longestRunningQuery: number;
    totalTrackedOperations: number;
  } {
    const totalActive = Array.from(this.activeQueries.values()).reduce((sum, count) => sum + count, 0);
    const now = Date.now();
    const queryAges = Array.from(this.queryStartTimes.values()).map(startTime => now - startTime);
    const longestRunning = queryAges.length > 0 ? Math.max(...queryAges) : 0;
    
    return {
      activeQueries: totalActive,
      longestRunningQuery: longestRunning,
      totalTrackedOperations: this.activeQueries.size,
    };
  }
}

/**
 * Protected query wrapper with automatic limit enforcement
 */
export class ProtectedQuery<T> {
  constructor(
    private db: GenericDatabaseReader<any>,
    private tableName: string,
    private operationType: keyof typeof QUERY_LIMITS,
    private operationId: string
  ) {}
  
  /**
   * Create a protected query with index
   */
  withIndex<IndexName extends string>(
    indexName: IndexName,
    indexQuery?: (q: any) => any
  ): ProtectedQueryWithIndex<T> {
    return new ProtectedQueryWithIndex<T>(
      this.db,
      this.tableName,
      this.operationType,
      this.operationId,
      indexName,
      indexQuery
    );
  }
  
  /**
   * Create a protected query without index
   */
  query(): ProtectedQueryBuilder<T> {
    const baseQuery = this.db.query(this.tableName);
    return new ProtectedQueryBuilder<T>(baseQuery, this.operationType, this.operationId);
  }
}

/**
 * Protected query with index support
 */
export class ProtectedQueryWithIndex<T> {
  constructor(
    private db: GenericDatabaseReader<any>,
    private tableName: string,
    private operationType: keyof typeof QUERY_LIMITS,
    private operationId: string,
    private indexName: string,
    private indexQuery?: (q: any) => any
  ) {}
  
  /**
   * Execute the indexed query with protection
   */
  execute(): ProtectedQueryBuilder<T> {
    const baseQuery = this.indexQuery
      ? this.db.query(this.tableName).withIndex(this.indexName, this.indexQuery)
      : this.db.query(this.tableName).withIndex(this.indexName);
    
    return new ProtectedQueryBuilder<T>(baseQuery, this.operationType, this.operationId);
  }
}

/**
 * Protected query builder with limit enforcement
 */
export class ProtectedQueryBuilder<T> {
  constructor(
    private query: any,
    private operationType: keyof typeof QUERY_LIMITS,
    private operationId: string
  ) {}
  
  /**
   * Add filter with protection
   */
  filter(filterFn: (q: any) => any): ProtectedQueryBuilder<T> {
    this.query = this.query.filter(filterFn);
    return this;
  }
  
  /**
   * Add ordering with protection
   */
  order(direction: "asc" | "desc"): ProtectedQueryBuilder<T> {
    this.query = this.query.order(direction);
    return this;
  }
  
  /**
   * Take with automatic limit enforcement
   */
  async take(limit: number): Promise<T[]> {
    // Enforce limits
    const enforcedLimit = QueryLimitEnforcer.enforceTakeLimit(limit, this.operationType);
    
    // Track query execution
    QueryLimitEnforcer.trackQueryStart(this.operationId);
    
    try {
      const result = await this.query.take(enforcedLimit);
      const executionTime = QueryLimitEnforcer.trackQueryEnd(this.operationId);
      
      // Log if we enforced a lower limit
      if (enforcedLimit < limit) {
        console.log(`🚀 QUERY LIMIT ENFORCED: ${this.operationType} limited from ${limit} to ${enforcedLimit} (${executionTime}ms)`);
      }
      
      return result;
    } catch (error) {
      QueryLimitEnforcer.trackQueryEnd(this.operationId);
      throw error;
    }
  }
  
  /**
   * First with protection
   */
  async first(): Promise<T | null> {
    QueryLimitEnforcer.trackQueryStart(this.operationId);
    
    try {
      const result = await this.query.first();
      QueryLimitEnforcer.trackQueryEnd(this.operationId);
      return result;
    } catch (error) {
      QueryLimitEnforcer.trackQueryEnd(this.operationId);
      throw error;
    }
  }
  
  /**
   * Collect is prohibited - throws error
   */
  async collect(): Promise<never> {
    if (QueryLimitEnforcer.isCollectProhibited()) {
      throw new Error(
        `🚨 COLLECT() PROHIBITED: Use .take(limit) instead for ${this.operationType}. ` +
        `Maximum allowed: ${QUERY_LIMITS.ABSOLUTE_MAX.QUERY_TAKE_LIMIT} records.`
      );
    }
    
    // This should never execute due to the prohibition
    throw new Error("Collect operation not allowed");
  }
}

/**
 * Bandwidth guard for high-level operations
 */
export class BandwidthGuard {
  /**
   * Check if an operation should be allowed based on recent bandwidth usage
   */
  static async shouldAllowOperation(
    db: GenericDatabaseReader<any>,
    operationType: string,
    estimatedBandwidth: number
  ): Promise<{
    allowed: boolean;
    reason?: string;
    suggestedDelay?: number;
  }> {
    try {
      // Check recent bandwidth usage (last hour)
      const oneHourAgo = Date.now() - 60 * 60 * 1000;
      const recentLogs = await db
        .query("bandwidthLogs")
        .withIndex("by_timestamp", q => q.gte("timestamp", oneHourAgo))
        .take(500); // Limited query
      
      const recentBandwidth = recentLogs.reduce((sum, log) => sum + log.bytesRead, 0);
      const hourlyLimit = 100 * 1024 * 1024; // 100MB per hour limit
      
      if (recentBandwidth + estimatedBandwidth > hourlyLimit) {
        const excessBandwidth = (recentBandwidth + estimatedBandwidth) - hourlyLimit;
        const suggestedDelay = Math.min((excessBandwidth / hourlyLimit) * 3600000, 1800000); // Max 30 min delay
        
        return {
          allowed: false,
          reason: `Bandwidth limit exceeded: ${Math.round(recentBandwidth / 1024 / 1024)}MB used in last hour`,
          suggestedDelay,
        };
      }
      
      return { allowed: true };
      
    } catch (error) {
      // If we can't check bandwidth, allow but log warning
      console.warn("Bandwidth guard check failed:", error);
      return { allowed: true, reason: "Unable to verify bandwidth usage" };
    }
  }
  
  /**
   * Estimate bandwidth for common operations
   */
  static estimateOperationBandwidth(operationType: string, recordCount: number): number {
    const avgRecordSizes = {
      mentions: 3000,    // 3KB per mention
      tweets: 2500,      // 2.5KB per tweet  
      responses: 2000,   // 2KB per response
      users: 1000,       // 1KB per user
      analytics: 500,    // 500B per analytics event
    };
    
    const recordSize = avgRecordSizes[operationType as keyof typeof avgRecordSizes] || 1000;
    return recordCount * recordSize;
  }
}

/**
 * Safe query helpers for common patterns
 */
export class SafeQueryHelpers {
  /**
   * Safe user dashboard query with all protections
   */
  static async getUserDashboardData(
    db: GenericDatabaseReader<any>,
    userId: string,
    timeframe: string = "7d"
  ): Promise<any> {
    const operationId = `dashboard_${userId}_${Date.now()}`;
    
    // Check bandwidth allowance
    const bandwidthCheck = await BandwidthGuard.shouldAllowOperation(
      db,
      "dashboard",
      BandwidthGuard.estimateOperationBandwidth("mentions", 100) + 
      BandwidthGuard.estimateOperationBandwidth("tweets", 100)
    );
    
    if (!bandwidthCheck.allowed) {
      throw new Error(`Dashboard query blocked: ${bandwidthCheck.reason}`);
    }
    
    // Execute protected queries
    const mentionQuery = QueryLimitEnforcer.createLimitedQuery(db, "mentions", "MENTIONS", operationId);
    const tweetQuery = QueryLimitEnforcer.createLimitedQuery(db, "tweets", "TWEETS", operationId);
    
    // This would integrate with existing dashboard logic
    return {
      queryProtected: true,
      operationId,
      message: "Dashboard query executed with full protection",
    };
  }
  
  /**
   * Safe mention search with all protections
   */
  static async searchMentionsSafely(
    db: GenericDatabaseReader<any>,
    searchTerm: string,
    limit: number = 20
  ): Promise<any> {
    const operationId = `search_${Date.now()}`;
    
    // Enforce search-specific limits
    const safeLimit = Math.min(limit, QUERY_LIMITS.MENTIONS.MAX_SEARCH_RESULTS);
    
    // Check bandwidth
    const estimatedBandwidth = BandwidthGuard.estimateOperationBandwidth("mentions", safeLimit);
    const bandwidthCheck = await BandwidthGuard.shouldAllowOperation(db, "mentions", estimatedBandwidth);
    
    if (!bandwidthCheck.allowed) {
      throw new Error(`Search query blocked: ${bandwidthCheck.reason}`);
    }
    
    // Execute protected search
    const mentionQuery = QueryLimitEnforcer.createLimitedQuery(db, "mentions", "MENTIONS", operationId);
    
    return {
      queryProtected: true,
      operationId,
      enforcedLimit: safeLimit,
      originalLimit: limit,
      message: "Search executed with full protection",
    };
  }
}