/**
 * Input Sanitization and Validation for AI Prompts and User Content
 * 🔐 SECURITY: Prevents prompt injection, XSS, and other input-based attacks
 */

import { PRODUCTION_SECURITY_CONFIG } from './production_config';
import { logger } from './secure_logger';

/**
 * Dangerous patterns that could be used for prompt injection
 */
const PROMPT_INJECTION_PATTERNS = [
  // Direct instruction attempts
  /ignore\s+(previous|all)\s+(instructions?|prompts?)/i,
  /forget\s+(everything|all|previous)/i,
  /new\s+(instructions?|task|role)/i,
  /system\s*:\s*/i,
  /assistant\s*:\s*/i,
  /human\s*:\s*/i,
  
  // Role manipulation
  /you\s+are\s+now/i,
  /act\s+as\s+(if\s+)?you\s+are/i,
  /pretend\s+(to\s+be|you\s+are)/i,
  /roleplay\s+as/i,
  
  // Instruction overrides
  /override\s+(previous|all)/i,
  /disregard\s+(previous|all)/i,
  /instead\s+of\s+.+,\s*do/i,
  
  // Prompt leaking attempts
  /show\s+me\s+(your|the)\s+(prompt|instructions?)/i,
  /what\s+(are\s+)?your\s+(instructions?|prompt)/i,
  /repeat\s+(your|the)\s+(prompt|instructions?)/i,
  
  // Code execution attempts
  /```[\s\S]*```/,
  /<script[\s\S]*<\/script>/i,
  /javascript\s*:/i,
  /eval\s*\(/i,
  /function\s*\(/i,
];

/**
 * XSS patterns to prevent cross-site scripting
 */
const XSS_PATTERNS = [
  /<script[\s\S]*?<\/script>/gi,
  /<iframe[\s\S]*?<\/iframe>/gi,
  /<object[\s\S]*?<\/object>/gi,
  /<embed[\s\S]*?<\/embed>/gi,
  /<link[\s\S]*?>/gi,
  /<meta[\s\S]*?>/gi,
  /javascript\s*:/gi,
  /vbscript\s*:/gi,
  /data\s*:/gi,
  /on\w+\s*=/gi, // onclick, onload, etc.
];

/**
 * SQL injection patterns (for completeness, though Convex uses NoSQL)
 */
const SQL_INJECTION_PATTERNS = [
  /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
  /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
  /('|\"|`|;|--|\*|\/\*|\*\/)/g,
];

/**
 * Sensitive information patterns
 */
const SENSITIVE_PATTERNS = [
  // API keys and tokens
  /[a-zA-Z0-9]{32,}/g, // Generic long alphanumeric strings
  /sk-[a-zA-Z0-9]{48}/g, // OpenAI API keys
  /xai-[a-zA-Z0-9]{32}/g, // xAI API keys
  /Bearer\s+[a-zA-Z0-9]+/gi,
  
  // Personal information
  /\b\d{3}-\d{2}-\d{4}\b/g, // SSN
  /\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b/g, // Credit card
  /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // Email
];

export interface SanitizationResult {
  sanitized: string;
  isValid: boolean;
  warnings: string[];
  blocked: boolean;
  originalLength: number;
  sanitizedLength: number;
}

export interface ValidationOptions {
  maxLength?: number;
  allowHtml?: boolean;
  strictMode?: boolean;
  logViolations?: boolean;
}

/**
 * Main sanitization function for AI prompts and user content
 */
export function sanitizeInput(
  input: string,
  context: string = 'general',
  options: ValidationOptions = {}
): SanitizationResult {
  const startTime = Date.now();
  const originalLength = input.length;
  const warnings: string[] = [];
  let sanitized = input;
  let blocked = false;

  // Apply default options
  const opts = {
    maxLength: options.maxLength || PRODUCTION_SECURITY_CONFIG.MAX_CONTENT_LENGTH,
    allowHtml: options.allowHtml || false,
    strictMode: options.strictMode || true,
    logViolations: options.logViolations !== false,
    ...options,
  };

  try {
    // 1. Length validation
    if (sanitized.length > opts.maxLength) {
      sanitized = sanitized.substring(0, opts.maxLength);
      warnings.push(`Content truncated to ${opts.maxLength} characters`);
    }

    // 2. Check for prompt injection attempts
    for (const pattern of PROMPT_INJECTION_PATTERNS) {
      if (pattern.test(sanitized)) {
        if (opts.strictMode) {
          blocked = true;
          warnings.push('Potential prompt injection detected');
          break;
        } else {
          sanitized = sanitized.replace(pattern, '[FILTERED]');
          warnings.push('Prompt injection pattern filtered');
        }
      }
    }

    // 3. XSS prevention
    if (!opts.allowHtml) {
      for (const pattern of XSS_PATTERNS) {
        if (pattern.test(sanitized)) {
          sanitized = sanitized.replace(pattern, '[FILTERED]');
          warnings.push('Potential XSS content filtered');
        }
      }
    }

    // 4. SQL injection prevention (defensive)
    for (const pattern of SQL_INJECTION_PATTERNS) {
      if (pattern.test(sanitized)) {
        if (opts.strictMode) {
          blocked = true;
          warnings.push('Potential SQL injection detected');
          break;
        } else {
          sanitized = sanitized.replace(pattern, '[FILTERED]');
          warnings.push('SQL injection pattern filtered');
        }
      }
    }

    // 5. Sensitive information redaction
    for (const pattern of SENSITIVE_PATTERNS) {
      if (pattern.test(sanitized)) {
        sanitized = sanitized.replace(pattern, '[REDACTED]');
        warnings.push('Sensitive information redacted');
      }
    }

    // 6. Additional sanitization
    if (!opts.allowHtml) {
      // Remove HTML tags
      sanitized = sanitized.replace(/<[^>]*>/g, '');
      
      // Decode HTML entities
      sanitized = sanitized
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#x27;/g, "'");
    }

    // 7. Normalize whitespace
    sanitized = sanitized.replace(/\s+/g, ' ').trim();

    // Log security violations
    if (opts.logViolations && (warnings.length > 0 || blocked)) {
      logger.security('Input sanitization triggered', {
        context,
        originalLength,
        sanitizedLength: sanitized.length,
        warnings,
        blocked,
        processingTime: Date.now() - startTime,
      });
    }

    return {
      sanitized: blocked ? '' : sanitized,
      isValid: !blocked && warnings.length === 0,
      warnings,
      blocked,
      originalLength,
      sanitizedLength: sanitized.length,
    };

  } catch (error) {
    logger.error('Input sanitization failed', {
      context,
      error: error instanceof Error ? error.message : String(error),
      originalLength,
    });

    return {
      sanitized: '',
      isValid: false,
      warnings: ['Sanitization failed'],
      blocked: true,
      originalLength,
      sanitizedLength: 0,
    };
  }
}

/**
 * Specialized sanitization for AI prompts
 */
export function sanitizeAIPrompt(prompt: string): SanitizationResult {
  return sanitizeInput(prompt, 'ai_prompt', {
    maxLength: PRODUCTION_SECURITY_CONFIG.MAX_QUERY_LENGTH,
    allowHtml: false,
    strictMode: true,
    logViolations: true,
  });
}

/**
 * Sanitization for user-generated content
 */
export function sanitizeUserContent(content: string): SanitizationResult {
  return sanitizeInput(content, 'user_content', {
    maxLength: PRODUCTION_SECURITY_CONFIG.MAX_CONTENT_LENGTH,
    allowHtml: false,
    strictMode: false, // More lenient for user content
    logViolations: true,
  });
}

/**
 * Sanitization for tweet content
 */
export function sanitizeTweetContent(content: string): SanitizationResult {
  return sanitizeInput(content, 'tweet_content', {
    maxLength: 2800, // Allow for longer tweet threads
    allowHtml: false,
    strictMode: false,
    logViolations: false, // Don't log every tweet
  });
}

/**
 * Validate and sanitize batch inputs
 */
export function sanitizeBatch(
  inputs: string[],
  context: string = 'batch',
  options: ValidationOptions = {}
): SanitizationResult[] {
  if (inputs.length > PRODUCTION_SECURITY_CONFIG.MAX_BATCH_SIZE) {
    logger.security('Batch size limit exceeded', {
      context,
      requestedSize: inputs.length,
      maxSize: PRODUCTION_SECURITY_CONFIG.MAX_BATCH_SIZE,
    });
    
    inputs = inputs.slice(0, PRODUCTION_SECURITY_CONFIG.MAX_BATCH_SIZE);
  }

  return inputs.map((input, index) => 
    sanitizeInput(input, `${context}_${index}`, options)
  );
}

/**
 * Check if content contains suspicious patterns
 */
export function containsSuspiciousPatterns(content: string): boolean {
  const allPatterns = [
    ...PROMPT_INJECTION_PATTERNS,
    ...XSS_PATTERNS,
    ...SQL_INJECTION_PATTERNS,
  ];

  return allPatterns.some(pattern => pattern.test(content));
}

/**
 * Extract and validate URLs from content
 */
export function sanitizeUrls(content: string): string {
  const urlPattern = /(https?:\/\/[^\s]+)/g;
  const allowedDomains = [
    'twitter.com',
    'x.com',
    'buddychip.pro',
    'github.com',
    'docs.convex.dev',
  ];

  return content.replace(urlPattern, (url) => {
    try {
      const urlObj = new URL(url);
      if (allowedDomains.some(domain => urlObj.hostname.includes(domain))) {
        return url;
      } else {
        return '[FILTERED_URL]';
      }
    } catch {
      return '[INVALID_URL]';
    }
  });
}
