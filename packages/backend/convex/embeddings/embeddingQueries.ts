import { action, query } from "../_generated/server";
import { v } from "convex/values";
import { getOpenRouterClient } from "../lib/openrouter_client";
import { api } from "../_generated/api";

/**
 * Find similar tweets using vector search
 */
export const findSimilarTweets = action({
  args: {
    query: v.string(),
    limit: v.optional(v.number()),
    threshold: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) {
      throw new Error("User not found");
    }

    try {
      const client = getOpenRouterClient();
      
      // Generate embedding for the query
      const queryEmbedding = await client.generateEmbedding(args.query);

      // Search for similar tweet embeddings
      const results = await ctx.vectorSearch("tweetEmbeddings", "by_embedding", {
        vector: queryEmbedding.embedding,
        limit: args.limit || 10,
        filter: (q: any) => q.eq("model", queryEmbedding.model),
      });

      // Get the actual tweets and filter by user's accounts
      const twitterAccounts = await ctx.runQuery(api.userQueries.getUserTwitterAccounts, {
        userId: user._id,
      });

      const accountIds = new Set(twitterAccounts.map((acc: any) => acc._id));

      const similarTweets = [];
      
      for (const result of results) {
        if (args.threshold && result._score < args.threshold) {
          continue;
        }

        const embedding = await ctx.runQuery(api.userQueries.getTweetEmbedding, {
          embeddingId: result._id,
        });
        if (!embedding) continue;

        const tweet = await ctx.runQuery(api.userQueries.getTweetById, {
          tweetId: embedding.tweetId,
        });
        if (!tweet || !accountIds.has(tweet.twitterAccountId)) continue;

        similarTweets.push({
          tweet,
          similarity: result._score,
          embeddingId: embedding._id,
        });
      }

      return {
        query: args.query,
        results: similarTweets,
        totalFound: similarTweets.length,
      };
    } catch (error) {
      console.error("Similar tweets search failed:", error);
      throw new Error(`Similar tweets search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Find similar mentions using vector search
 */
export const findSimilarMentions = action({
  args: {
    query: v.string(),
    limit: v.optional(v.number()),
    threshold: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) {
      throw new Error("User not found");
    }

    try {
      const client = getOpenRouterClient();
      
      // Generate embedding for the query
      const queryEmbedding = await client.generateEmbedding(args.query);

      // Search for similar mention embeddings
      const results = await ctx.vectorSearch("mentionEmbeddings", "by_embedding", {
        vector: queryEmbedding.embedding,
        limit: args.limit || 10,
        filter: (q: any) => q.eq("model", queryEmbedding.model),
      });

      // Get the actual mentions and filter by user's accounts
      const twitterAccounts = await ctx.runQuery(api.userQueries.getUserTwitterAccounts, {
        userId: user._id,
      });

      const accountIds = new Set(twitterAccounts.map((acc: any) => acc._id));

      const similarMentions = [];
      
      for (const result of results) {
        if (args.threshold && result._score < args.threshold) {
          continue;
        }

        const embedding = await ctx.runQuery(api.userQueries.getMentionEmbedding, {
          embeddingId: result._id,
        });
        if (!embedding) continue;

        const mention = await ctx.runQuery(api.userQueries.getMentionById, {
          mentionId: embedding.mentionId,
        });
        if (!mention || !accountIds.has(mention.monitoredAccountId)) continue;

        similarMentions.push({
          mention,
          similarity: result._score,
          embeddingId: embedding._id,
        });
      }

      return {
        query: args.query,
        results: similarMentions,
        totalFound: similarMentions.length,
      };
    } catch (error) {
      console.error("Similar mentions search failed:", error);
      throw new Error(`Similar mentions search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Find similar successful responses for context
 */
export const findSimilarResponses = action({
  args: {
    query: v.string(),
    targetType: v.optional(v.union(v.literal("tweet"), v.literal("mention"))),
    style: v.optional(v.string()),
    limit: v.optional(v.number()),
    threshold: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) {
      throw new Error("User not found");
    }

    try {
      const client = getOpenRouterClient();
      
      // Generate embedding for the query
      const queryEmbedding = await client.generateEmbedding(args.query);

      // Search for similar response embeddings
      const results = await ctx.vectorSearch("responseEmbeddings", "by_embedding", {
        vector: queryEmbedding.embedding,
        limit: args.limit || 10,
        filter: (q: any) => q.eq("model", queryEmbedding.model),
      });

      const similarResponses = [];
      
      for (const result of results) {
        if (args.threshold && result._score < args.threshold) {
          continue;
        }

        const embedding = await ctx.runQuery(api.userQueries.getResponseEmbedding, {
          embeddingId: result._id,
        });
        if (!embedding) continue;

        const response = await ctx.runQuery(api.userQueries.getResponseById, {
          responseId: embedding.responseId,
        });
        if (!response || response.userId !== user._id) continue;

        // Apply additional filters
        if (args.targetType && response.targetType !== args.targetType) continue;
        if (args.style && response.style !== args.style) continue;

        // Prefer successful responses (posted with good engagement)
        const isSuccessful = response.status === "posted" && 
          response.actualEngagement && 
          (response.actualEngagement.likes + response.actualEngagement.retweets) > 0;

        similarResponses.push({
          response,
          similarity: result._score,
          embeddingId: embedding._id,
          isSuccessful,
        });
      }

      // Sort by success and similarity
      similarResponses.sort((a: any, b: any) => {
        if (a.isSuccessful && !b.isSuccessful) return -1;
        if (!a.isSuccessful && b.isSuccessful) return 1;
        return b.similarity - a.similarity;
      });

      return {
        query: args.query,
        results: similarResponses,
        totalFound: similarResponses.length,
      };
    } catch (error) {
      console.error("Similar responses search failed:", error);
      throw new Error(`Similar responses search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Get user context for personalized responses
 */
export const getUserContext = query({
  args: {
    contextTypes: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) {
      throw new Error("User not found");
    }

    let query = ctx.db
      .query("userContextEmbeddings")
      .withIndex("by_user", (q: any) => q.eq("userId", user._id));

    const contexts = await query.collect();

    // Filter by context types if specified
    let filteredContexts = contexts;
    if (args.contextTypes && args.contextTypes.length > 0) {
      filteredContexts = contexts.filter((ctx: any) => 
        args.contextTypes!.includes(ctx.contextType)
      );
    }

    // Group by context type
    const groupedContexts: Record<string, any[]> = {};
    filteredContexts.forEach((context: any) => {
      if (!groupedContexts[context.contextType]) {
        groupedContexts[context.contextType] = [];
      }
      groupedContexts[context.contextType].push({
        id: context._id,
        content: context.content,
        weight: context.weight,
        createdAt: context.createdAt,
        updatedAt: context.updatedAt,
      });
    });

    return {
      userId: user._id,
      contexts: groupedContexts,
      totalContexts: filteredContexts.length,
    };
  },
});

/**
 * Find contextually relevant content for response generation
 */
export const findRelevantContext = action({
  args: {
    query: v.string(),
    contextTypes: v.optional(v.array(v.string())),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) {
      throw new Error("User not found");
    }

    try {
      const client = getOpenRouterClient();
      
      // Generate embedding for the query
      const queryEmbedding = await client.generateEmbedding(args.query);

      // Search user context embeddings
      const results = await ctx.vectorSearch("userContextEmbeddings", "by_embedding", {
        vector: queryEmbedding.embedding,
        limit: args.limit || 5,
        filter: (q: any) => {
          let filter = q.eq("userId", user._id).eq("model", queryEmbedding.model);
          
          if (args.contextTypes && args.contextTypes.length > 0) {
            // Note: This is a simplified filter - in practice you might need a more complex query
            filter = filter.eq("contextType", args.contextTypes[0]);
          }
          
          return filter;
        },
      });

      const relevantContexts = [];
      
      for (const result of results) {
        const embedding = await ctx.runQuery(api.userQueries.getUserContextEmbedding, {
          embeddingId: result._id,
        });
        if (!embedding) continue;

        relevantContexts.push({
          contextType: embedding.contextType,
          content: embedding.content,
          weight: embedding.weight,
          similarity: result._score,
          relevanceScore: result._score * (embedding.weight || 1.0),
        });
      }

      // Sort by relevance score
      relevantContexts.sort((a: any, b: any) => b.relevanceScore - a.relevanceScore);

      return {
        query: args.query,
        contexts: relevantContexts,
        totalFound: relevantContexts.length,
      };
    } catch (error) {
      console.error("Context search failed:", error);
      throw new Error(`Context search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Get embedding statistics
 */
export const getEmbeddingStats = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Get user's Twitter accounts
    const twitterAccounts = await ctx.db
      .query("twitterAccounts")
      .withIndex("by_user", (q: any) => q.eq("userId", user._id))
      .collect();

    const accountIds = new Set(twitterAccounts.map((acc: any) => acc._id));

    // Count embeddings
    const allTweetEmbeddings = await ctx.runQuery(api.userQueries.getAllTweetEmbeddings, { limit: 10000 });
    const allMentionEmbeddings = await ctx.runQuery(api.userQueries.getAllMentionEmbeddings, { limit: 10000 });
    const allResponseEmbeddings = await ctx.runQuery(api.userQueries.getAllResponseEmbeddings, { limit: 10000 });
    const userContextEmbeddings = await ctx.db
      .query("userContextEmbeddings")
      .withIndex("by_user", (q: any) => q.eq("userId", user._id))
      .collect();

    // Filter by user's content
    const allTweets = await ctx.runQuery(api.userQueries.getAllTweets, { limit: 10000 });
    const userTweets = allTweets.filter((tweet: any) => accountIds.has(tweet.twitterAccountId));
    const userTweetIds = new Set(userTweets.map((t: any) => t._id));

    const allMentions = await ctx.runQuery(api.userQueries.getAllMentions, { limit: 10000 });
    const userMentions = allMentions.filter((mention: any) => accountIds.has(mention.monitoredAccountId));
    const userMentionIds = new Set(userMentions.map((m: any) => m._id));

    const allResponses = await ctx.runQuery(api.userQueries.getAllResponses, { limit: 10000 });
    const userResponses = allResponses.filter((r: any) => r.userId === user._id);
    const userResponseIds = new Set(userResponses.map((r: any) => r._id));

    const tweetEmbeddings = allTweetEmbeddings.filter((e: any) => userTweetIds.has(e.tweetId));
    const mentionEmbeddings = allMentionEmbeddings.filter((e: any) => userMentionIds.has(e.mentionId));
    const responseEmbeddings = allResponseEmbeddings.filter((e: any) => userResponseIds.has(e.responseId));

    return {
      tweets: {
        total: userTweets.length,
        embedded: tweetEmbeddings.length,
        coverage: userTweets.length > 0 ? Math.round((tweetEmbeddings.length / userTweets.length) * 100) : 0,
      },
      mentions: {
        total: userMentions.length,
        embedded: mentionEmbeddings.length,
        coverage: userMentions.length > 0 ? Math.round((mentionEmbeddings.length / userMentions.length) * 100) : 0,
      },
      responses: {
        total: userResponses.length,
        embedded: responseEmbeddings.length,
        coverage: userResponses.length > 0 ? Math.round((responseEmbeddings.length / userResponses.length) * 100) : 0,
      },
      userContext: {
        total: userContextEmbeddings.length,
        byType: userContextEmbeddings.reduce((acc: any, ctx: any) => {
          acc[ctx.contextType] = (acc[ctx.contextType] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
      },
      totalEmbeddings: tweetEmbeddings.length + mentionEmbeddings.length + 
        responseEmbeddings.length + userContextEmbeddings.length,
    };
  },
});

/**
 * Get tweet embedding by tweet ID
 */
export const getTweetEmbedding = query({
  args: {
    tweetId: v.id("tweets"),
  },
  handler: async (ctx, args) => {
    const embedding = await ctx.db
      .query("tweetEmbeddings")
      .withIndex("by_tweet", (q: any) => q.eq("tweetId", args.tweetId))
      .first();
    
    return embedding;
  },
});

/**
 * Get user context embeddings
 */
export const getUserContextEmbeddings = query({
  args: {
    userId: v.id("users"),
    limit: v.optional(v.number()),
    contextType: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("userContextEmbeddings")
      .withIndex("by_user", (q: any) => q.eq("userId", args.userId));
    
    if (args.contextType) {
      query = query.filter((q: any) => q.eq(q.field("contextType"), args.contextType));
    }
    
    const embeddings = await query
      .order("desc")
      .take(args.limit || 10);
    
    return embeddings;
  },
});

/**
 * Get mention embedding by mention ID
 */
export const getMentionEmbedding = query({
  args: {
    mentionId: v.id("mentions"),
  },
  handler: async (ctx, args) => {
    const embedding = await ctx.db
      .query("mentionEmbeddings")
      .withIndex("by_mention", (q: any) => q.eq("mentionId", args.mentionId))
      .first();
    
    return embedding;
  },
});

/**
 * Get response embedding by response ID
 */
export const getResponseEmbedding = query({
  args: {
    responseId: v.id("responses"),
  },
  handler: async (ctx, args) => {
    const embedding = await ctx.db
      .query("responseEmbeddings")
      .withIndex("by_response", (q: any) => q.eq("responseId", args.responseId))
      .first();
    
    return embedding;
  },
});