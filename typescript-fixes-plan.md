# TypeScript Error Fixes Plan

## Overview
Found 249 TypeScript errors across 52 files. These errors fall into several categories:

### Error Categories:
1. **Missing type annotations** (implicit any types)
2. **Property access on undefined/unknown types**
3. **Incorrect parameter types in function calls**
4. **Missing properties on objects**
5. **Query/Index type mismatches in Convex**
6. **Action/mutation type issues**

## Execution Plan

### Phase 1: Backend Core Types (Convex Schema Issues)
- [x] Fix schema-related query errors in mentions/sentimentAnalytics.ts
- [x] Fix schema-related query errors in mentions/sentimentQueries.ts
- [x] Fix schema-related query errors in queries/optimizedQueries.ts (partially - 33 errors remaining)
- [x] Fix schema-related query errors in mentions/optimizedMentionQueries.ts
- [x] Fix schema-related query errors in mentions/mentionQueries.ts

### Phase 2: Error Handling & Type Safety
- [ ] Fix error handling in monitoring/healthCheck.ts
- [ ] Fix error handling in monitoring/smartHealthCheck.ts
- [ ] Fix error handling in lib/openrouter_client.ts
- [ ] Fix error handling in lib/config_validator.ts

### Phase 3: Action/Mutation Type Annotations
- [ ] Fix action types in xaiLiveSearch.ts
- [ ] Fix action types in responseMutations.ts
- [ ] Fix action types in ai/testNewModels.ts
- [ ] Fix action types in ai/unifiedImageGeneration.ts

### Phase 4: Frontend Component Types
- [ ] Fix auth component types
- [ ] Fix billing component types
- [ ] Fix chatbot component types
- [ ] Fix live-search component types
- [ ] Fix reply-guy component types

### Phase 5: Utility & Helper Functions
- [ ] Fix projection types in lib/projections.ts
- [ ] Fix optimization config types
- [ ] Fix debug config types
- [ ] Fix workflow types

### Phase 6: Test Files
- [ ] Fix test file types

## Implementation Strategy
1. Start with backend schema/query issues as they affect multiple files
2. Add proper type annotations for all implicit any types
3. Fix property access issues with proper null checks
4. Update function signatures to match expected types
5. Add proper error handling with typed catch blocks
6. Ensure all Convex actions/mutations have proper return types

## Key Fixes Needed:
- Add proper type annotations for query parameters
- Fix index names and field names in Convex queries
- Add null/undefined checks for optional properties
- Type error objects in catch blocks
- Add return type annotations for actions/mutations
- Fix property access on user/mention/tweet objects
