# Archived Documentation Index

This directory contains documentation that is no longer actively maintained but preserved for historical reference.

## Recently Archived (January 2025)

### Performance & Implementation Documents
- **`performance-enhancement-document.md`** - Archived performance improvement strategies (moved from root)
- **`plan.md`** - Old project plan (replaced by updated TASKS.md)

### UI/UX Documentation  
- **`docs/UI-UX.md`** - UI/UX guidelines (consolidated into main docs)
- **`docs/FETCHMENTION.md`** - Mention fetching documentation (feature completed)

### Security Documentation
- **`SECURITY_AUDIT_REPORT.md`** - Security audit findings
- **`SECURITY_IMPLEMENTATION_SUMMARY.md`** - Security implementation details
- **`security-audit-plan.md`** - Security audit planning

### UI Improvement Plans
- **`CLERK_UI_IMPROVEMENT_PLAN.md`** - Clerk authentication UI improvements
- **`UI.md`** - General UI improvement documentation

## Active Documentation (Not Archived)

### Current Project Documentation
- **`TASKS.md`** - Current task priorities and roadmap
- **`TASKSLOGS.md`** - Complete archive of completed tasks
- **`README.md`** - Project setup and overview
- **`CLAUDE.md`** - Technical architecture
- **`IMPLEMENTATION_SUMMARY.md`** - Implementation details
- **`PERFORMANCE_FIXES_SUMMARY.md`** - Performance optimization summary

### Specialized Documentation
- **`docs/BILLING_IMPLEMENTATION.md`** - Billing system documentation
- **`docs/CHATBOT_PLAN.md`** - AI chatbot integration plans
- **`docs/FEATURES.md`** - Feature specifications
- **`docs/OPTIMIZATION_TESTING_AND_MONITORING_GUIDE.md`** - Performance monitoring

## Archive Policy

Documents are archived when:
1. Features are fully implemented and stable
2. Documentation is superseded by newer versions
3. Plans are completed or no longer relevant
4. Information is consolidated into other documents

Archived documents are preserved for:
- Historical reference
- Implementation pattern examples
- Debugging legacy issues
- Understanding project evolution
